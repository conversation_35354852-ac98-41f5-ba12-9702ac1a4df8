package com.yelink.dfscommon.utils;

import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/3/6 9:50
 */
public class PrintDataUtils {

    /**
     * 截取位数  （按 - 字符截取位数，例如 批次号：123456 ， sliceDigits：2-4，截取后字符串为：234）
     *
     * @param data 数据
     */
    public static String sliceDigits(String data, String sliceDigits) {
        if (StringUtils.isBlank(data) || data.equals(Constant.NULL)) {
            return "";
        }
        if (StringUtils.isBlank(sliceDigits)) {
            return data;
        }
        String barCodeSubstring;
        // 截取位数
        String[] split = sliceDigits.split(Constant.CROSSBAR);
        if (data.length() > Integer.parseInt(split[split.length - 1])) {
            // 如果截取后只有一个数字，则从该位开始截取到最后一位
            if (split.length - 1 == 0) {
                barCodeSubstring = data.substring(Integer.parseInt(split[0]) - 1);
            } else {
                barCodeSubstring = data.substring(Integer.parseInt(split[0]) - 1, Integer.parseInt(split[split.length - 1]));
            }
        } else {
            if (Integer.parseInt(split[0]) <= data.length()) {
                barCodeSubstring = data.substring(Integer.parseInt(split[0]) - 1);
            } else {
                // 打印位数超出范围
                throw new ResponseException(RespCodeEnum.BAR_CODE_RULE_PRINT_DIGIT_OUT_OF_RANGE);
            }
        }
        return barCodeSubstring;
    }

    public static String sliceDigits(Number data, String sliceDigits) {
        if (data == null) {
            return "";
        }
        // 将Number转换为字符串，并去除小数末尾的零
        String dataStr = removeTrailingZeros(data);
        if (StringUtils.isBlank(sliceDigits)) {
            return dataStr;
        }
        String barCodeSubstring;
        // 截取位数规则
        String[] split = sliceDigits.split(Constant.CROSSBAR);

        try {
            int startIndex = Integer.parseInt(split[0]);
            int endIndex = (split.length > 1) ? Integer.parseInt(split[split.length - 1]) : -1;

            // 验证索引必须为正整数
            if (startIndex <= 0 || (endIndex != -1 && endIndex <= 0)) {
                throw new ResponseException(RespCodeEnum.PRINT_DATA_RULE_INVALID_INDEX);
            }
            // 处理索引超出范围的情况
            if (dataStr.length() < startIndex) {
                throw new ResponseException(RespCodeEnum.BAR_CODE_RULE_PRINT_DIGIT_OUT_OF_RANGE);
            }

            // 执行截取操作
            if (endIndex == -1) {
                barCodeSubstring = dataStr.substring(startIndex - 1);
            } else if (dataStr.length() >= endIndex) {
                barCodeSubstring = dataStr.substring(startIndex - 1, endIndex);
            } else {
                barCodeSubstring = dataStr.substring(startIndex - 1);
            }
        } catch (NumberFormatException e) {
            throw new ResponseException(RespCodeEnum.BAR_CODE_RULE_PRINT_DIGIT_OUT_OF_RANGE);
        }
        return barCodeSubstring;
    }

    /**
     * 去除小数末尾的零
     */
    private static String removeTrailingZeros(Number number) {
        String numStr = number.toString();
        // 处理科学计数法 (如 1.0E+6)
        if (numStr.contains("E") || numStr.contains("e")) {
            try {
                BigDecimal bd = new BigDecimal(numStr);
                numStr = bd.toPlainString();
            } catch (NumberFormatException e) {
                // 保持原始字符串
            }
        }
        // 去除小数末尾的零
        if (numStr.contains(".")) {
            // 去除末尾连续的零
            numStr = numStr.replaceAll("0+$", "");
            // 如果小数点后没有数字，移除小数点
            numStr = numStr.replaceAll("\\.$", "");
        }
        return numStr;
    }

}
