package com.yelink.dfscommon.dto.dfs;


import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.material.MaterialEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报工表VO
 *
 * <AUTHOR>
 * @date 2022-08-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReporterRecordVO {

    // 工单表
    /**
     * 生产工单id
     */
    @ApiModelProperty("生产工单id")
    private Integer workOrderId;
    /**
     * 生产工单编号
     */
    @ApiModelProperty("生产工单编号")
    private String workOrderNumber;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String state;
    /**
     * 物料编号
     */
    @ApiModelProperty("物料编号")
    @UnitColumn
    private String materialCode;
    /**
     * 计划数量
     */
    @ApiModelProperty("计划数量")
    @UnitFormatColumn
    private Double planQuantity;
    /**
     * 工序名称
     */
    @ApiModelProperty("工序名称")
    private String craftName;

    /**
     * 制造单元模型名称
     */
    @ApiModelProperty("制造单元模型名称")
    private String lineModelName;
    /**
     * 产线名称
     */
    @ApiModelProperty("产线名称")
    private String lineName;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 截止时间
     */
    @ApiModelProperty("截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    @UnitFormatColumn
    private Double inputTotal;
    /**
     * 已完成数量
     */
    @ApiModelProperty("已完成数量")
    @UnitFormatColumn
    private Double finishCount;
    /**
     * 不合格量
     */
    @ApiModelProperty("不合格量")
    @UnitFormatColumn
    private Double unqualified;
    /**
     * 计划工时
     */
    @ApiModelProperty("计划工时")
    private Double plannedWorkingHours;
    /**
     * 生产时长
     */
    @ApiModelProperty("生产时长")
    private Double actualWorkingHours;
    /**
     * 流转时长
     */
    @ApiModelProperty("流转时长")
    private Double circulationDuration;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    private String orderNumber;
    /**
     * 销售订单号
     */
    @ApiModelProperty("销售订单号")
    private String saleOrderNumber;
    /**
     * 销售订单号-客户名称
     */
    @ApiModelProperty("客户名称")
    private String customer;

    /**
     * 销售订单号-客户编号
     */
    @ApiModelProperty("客户编号")
    private String customerCode;

    // 报工表
    /**
     * 生产报工id
     */
    @ApiModelProperty("生产报工id")
    private Integer reportLineId;
    /**
     * 上报方式
     */
    @ApiModelProperty("上报方式")
    private String type;
    /**
     * 批次
     */
    @ApiModelProperty("批次")
    private String batch;

    /**
     * 批次备注
     */
    @ApiModelProperty(value = "批次备注")
    private String batchRemark;
    /**
     * 载具号
     */
    @ApiModelProperty("载具号")
    private String vehicleCode;
    /**
     * 班次名称
     */
    @ApiModelProperty("班次名称")
    private String shiftType;
    /**
     * 完成数量 - 对应报工表的reportCount字段
     */
    @ApiModelProperty("完成数量 - 对应报工表的reportCount字段")
    @UnitFormatColumn
    private Double finishCountTwo;
    /**
     * 不合格数量 - 对应报工表的reportUnqualified字段
     */
    @ApiModelProperty("不合格数量 - 对应报工表的reportUnqualified字段")
    @UnitFormatColumn
    private Double unqualifiedTwo;
    /**
     * 不良描述
     */
    @ApiModelProperty("不良描述")
    private String defectDesc;
    /**
     * 报工日期
     */
    @ApiModelProperty("报工日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date reportDate;

    /**
     * 报工开始时间
     */
    @ApiModelProperty("报工开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportTime;
    /**
     * 报工结束时间
     */
    @ApiModelProperty("报工结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reportEndTime;

    /**
     * 操作员
     */
    @ApiModelProperty("操作员")
    private String operator;
    /**
     * 计数器参考值
     */
    @ExcelProperty("计数器参考值")
    private Double counterPerReported;

    /**
     * 计数器累计参考值
     */
    @ExcelProperty("计数器累计参考值")
    private Double counterReported;
    /**
     * 上报人
     */
    @ApiModelProperty("上报人")
    private String userNickname;
    /**
     * 上报时间
     */
    @ApiModelProperty("上报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    // 物料表
    /**
     * 物料对象
     */
    @ApiModelProperty("物料对象")
    private MaterialEntity materialFields;

    /**
     * 物料名称
     */
    private String materialName;


    /**
     * 上报工时
     */
    @ApiModelProperty("上报工时")
    private double effectiveHours;

    /**
     * 产品规格
     */
    @ApiModelProperty("产品规格")
    private String productStandard;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    private String workCenterName;


    /**
     * 基本生产单元类型
     */
    @ApiModelProperty("基本生产单元类型")
    private String workCenterTypeName;

    /**
     * 关联资源类型
     */
    @ApiModelProperty("关联资源类型")
    private String resourceTypeName;

    /**
     * 制造单元
     */
    @ApiModelProperty("制造单元")
    private String resourceLineName;

    /**
     * 班组名称
     */
    @ApiModelProperty("班组名称")
    private String resourceTeamName;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String resourceDeviceName;

    /**
     * 产成品损耗数
     */
    @ApiModelProperty("产成品损耗数")
    @UnitFormatColumn
    private Double lossQuantity;

    /**
     * 人员数量
     */
    @ApiModelProperty("人员数量")
    private Integer crewSize;

    @ApiModelProperty("工序id (多个','分隔)")
    private String procedureIds;

    @ApiModelProperty("工序 (多个','分隔)")
    private String procedureName;

    @ApiModelProperty("工序别名 (多个','分隔)")
    private String procedureAlias;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 报工图片
     */
    @ApiModelProperty("报工图片")
    private String[] picUrls;

    /**
     * 质量等级(多个按逗号分隔)
     */
    @ApiModelProperty("质量等级(多个按逗号分隔)")
    private String qualityLevel;

    @ExcelProperty(value = "物料编码")
    private String code;

    @ExcelProperty(value = "单位")
    private String unit;

    @ExcelProperty(value = "图号")
    private String drawingNumber;

    @ExcelProperty(value = "物料规格")
    private String standard;

    @ExcelProperty(value = "材质")
    private String rawMaterial;

    /**
     * 单件理论工时
     */
    @ExcelProperty(value = "单件理论工时")
    private Double theoryHour;
    /**
     * 产出理论工时
     */
    @ExcelProperty(value = "产出理论工时")
    private Double produceTheoryHour;


    /**
     * 单重
     */
    @ExcelProperty(value = "单重")
    private Double scaleFactor;
    /**
     * 标准单重
     */
    @ExcelProperty(value = "标准单重")
    private Double standardScaleFactor;
    /**
     * 总重
     */
    @ExcelProperty(value = "总重")
    private Double totalScale;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("班组名称")
    private String teamName;

//    @ExcelProperty(value = "客户物料名称")
//    private String customerMaterialName;
//
//    @ExcelProperty(value = "客户物料编码")
//    private String customerMaterialCode;
//
//    @ExcelProperty(value = "客户物料规格")
//    private String customerMaterialStandard;

    @ApiModelProperty("报工扩展字段1")
    private String reportFieldOne;

    @ApiModelProperty("报工扩展字段2")
    private String reportFieldTwo;

    @ApiModelProperty("报工扩展字段3")
    private String reportFieldThree;

    @ApiModelProperty("报工扩展字段4")
    private String reportFieldFour;

    @ApiModelProperty("报工扩展字段5")
    private String reportFieldFive;

    @ApiModelProperty("报工扩展字段1自定义名称")
    private String reportFieldOneName;

    @ApiModelProperty("报工扩展字段2自定义名称")
    private String reportFieldTwoName;

    @ApiModelProperty("报工扩展字段3自定义名称")
    private String reportFieldThreeName;

    @ApiModelProperty("报工扩展字段4自定义名称")
    private String reportFieldFourName;

    @ApiModelProperty("报工扩展字段5自定义名称")
    private String reportFieldFiveName;

    private String orderReportType;

    private String orderReportName;

    private String supplierCode;

    private String supplierName;
    private String userName;
    /**
     * 工装号列表
     */
    @ApiModelProperty("工装号列表")
    @TableField(exist = false)
    private List<String> processAssemblyList;

    /**
     * 工装号列表
     */
    @ApiModelProperty("工装号列表")
    @TableField(exist = false)
    private String  processAssemblyCodeList;

    public String getProcessAssemblyCodeList() {
        if (CollectionUtils.isEmpty(this.processAssemblyList)) {
            return "";
        }
        return  processAssemblyList.stream().collect(Collectors.joining(Constants.SEP));
    }

    private Integer skuId;

    private SkuEntity skuEntity;


    /**
     * 上报: 完成数（在审核后会将 finish_count 填进来）
     */
    private Double reportCount;

    /**
     * 上报: 不良数（在审核后会将 unqualified 填进来）
     */
    private Double reportUnqualified;

    private Double reportHumanHour;

    /**
     *  最终取值的人员工时
     */
    private Double humanHour;

    /**
     * 审核：完成数
     */
    public Double auditCount;
    /**
     * 审核：不良数
     */
    public Double auditUnqualified;

    /**
     * 审核: 人员工时
     */
    private Double auditHumanHour;

    /**
     * 审核人
     */
    private String auditor;
    @TableField(exist = false)
    private String auditorName;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date auditTime;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectDefineName;

}

