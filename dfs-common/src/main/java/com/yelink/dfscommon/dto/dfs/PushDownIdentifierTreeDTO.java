package com.yelink.dfscommon.dto.dfs;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 下推标识树形结构DTO
 * 用于返回下推标识配置的树形结构，包含名称和对应的下推状态
 *
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PushDownIdentifierTreeDTO {

    /**
     * 配置名称
     */
    @ApiModelProperty("配置名称")
    private String name;

    /**
     * 配置编码
     */
    @ApiModelProperty("配置编码")
    private String code;

    /**
     * 下推标识状态列表（未下推、部分下推、已下推）
     */
    @ApiModelProperty("下推标识状态列表")
    private List<PushDownIdentifierStateDTO> children;

    /**
     * 下推标识状态DTO
     */
    @Data
    @Builder
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PushDownIdentifierStateDTO {

        /**
         * 状态编码
         */
        @ApiModelProperty("状态编码")
        private String code;

        /**
         * 状态名称
         */
        @ApiModelProperty("状态名称")
        private String name;

        /**
         * 完整的标识编码（配置编码_状态编码）
         */
        @ApiModelProperty("完整的标识编码")
        private String fullCode;

        /**
         * 完整的显示名称（配置名称-状态名称）
         */
        @ApiModelProperty("完整的显示名称")
        private String fullName;
    }
}
