package com.yelink.dfscommon.dto.dfs;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 单据下推标识查询DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class OrderPushDownIdentifierSelectDTO {

    /**
     * 源单据类型
     */
    @ApiModelProperty("源单据类型")
    private String orderType;

    /**
     * 单据物料行id
     */
    @ApiModelProperty("单据物料行id")
    private String orderMaterialId;

    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    private String batchNumber;

    /**
     * 下推标识状态
     */
    @ApiModelProperty("下推标识状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)")
    private String state;

    /**
     * 目标单据类型
     */
    @ApiModelProperty("目标单据类型")
    private String targetOrderType;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer current = 1;

    /**
     * 每页大小
     */
    @ApiModelProperty("每页大小")
    private Integer size = 10;

}
