package com.yelink.dfscommon.dto.dfs;


import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.PurchaseEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderEntity;
import com.yelink.dfscommon.entity.dfs.barcode.BarCodeEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2021/8/4 19:26
 */

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PrintInfoDTO {

    /**
     * 生产工单
     */
    @UnitFormatContainer
    private WorkOrderEntity workOrderEntity;

    /**
     * 生产订单
     */
    @UnitFormatContainer
    private ProductOrderEntity productOrderEntity;

    /**
     * 批次
     */
    @UnitFormatContainer
    private BarCodeEntity barCodeEntity;

    /**
     * 采购订单
     */
    @UnitFormatContainer
    private PurchaseEntity purchaseEntity;
    /**
     * 生产工单报工记录
     */
    @UnitFormatContainer
    private ReporterRecordVO reporterRecordVO;

}
