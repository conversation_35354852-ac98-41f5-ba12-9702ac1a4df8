package com.yelink.dfscommon.dto.dfs;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 下推标识信息DTO
 * 用于返回单据的下推标识信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PushDownIdentifierInfoDTO {

    /**
     * 下推标识中文名称
     */
    @ApiModelProperty("下推标识中文名称")
    private String identifierName;

    /**
     * 目标单据类型
     */
    @ApiModelProperty("目标单据类型")
    private String targetOrderType;

    /**
     * 下推状态
     */
    @ApiModelProperty("下推状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)")
    private String state;

}
