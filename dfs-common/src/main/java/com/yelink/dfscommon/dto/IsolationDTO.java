package com.yelink.dfscommon.dto;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 权限产线绑定DTO
 *
 * <AUTHOR>
 * @Date 2021-05-18 11:56
 */
@Data
@Builder
@ToString
@AllArgsConstructor
public class IsolationDTO {

    /**
     * 隔离id列表(工作中心ID+“-”+生产基本单元id)
     */
    private List<String> isolationIds;

    /**
     * 生产基本单元id列表
     */
    private List<Integer> ids;

    /**
     * 工作中心id列表
     */
    private List<String> workCenterIds;

    /**
     * 是否全选工作中心
     */
    private Boolean allWorkCenter;

    public IsolationDTO() {
        this.isolationIds = Collections.emptyList();
        this.allWorkCenter = false;
        this.ids = Collections.emptyList();
        this.workCenterIds = Collections.emptyList();
    }

    /**
     * 根据目标的隔离id集合，查看是否展示
     * @param col 目标的隔离id集合
     * @param workOrderIsolationId 工单上记录的隔离id，用于与工作中心对比
     * @return 是否展示
     */
    public boolean isShow(Collection<String> col, String workOrderIsolationId) {
        if (Boolean.TRUE.equals(this.allWorkCenter)) {
            return true;
        }
        // 取交集
        return CollUtil.intersection(this.isolationIds, col).size() > 0 || this.workCenterIds.contains(workOrderIsolationId);
    }
}
