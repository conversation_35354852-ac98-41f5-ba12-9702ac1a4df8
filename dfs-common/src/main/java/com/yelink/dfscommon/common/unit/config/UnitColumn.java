package com.yelink.dfscommon.common.unit.config;




import com.yelink.dfscommon.common.unit.service.UnitFormatService;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 精度单位标记
 * 配合 {@link UnitFormatService}的实现类 使用
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface UnitColumn {

    /**
     * 默认是物料编码
     */
    QueryType type() default QueryType.MATERIAL;

    Class<? extends UnitCodeFormatter> formatClass() default DefaultUnitCodeFormatter.class;

    enum QueryType {
        /**
         * 物料编码
         */
        MATERIAL,

        /**
         * 单位名称
         */
        UNIT,
        ;
    }
}
