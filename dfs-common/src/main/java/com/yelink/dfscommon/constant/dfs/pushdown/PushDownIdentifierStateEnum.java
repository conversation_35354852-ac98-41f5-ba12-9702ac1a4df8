package com.yelink.dfscommon.constant.dfs.pushdown;

import com.yelink.dfscommon.entity.CommonEnumInterface;

/**
 * 下推标识状态枚举
 * 
 * <AUTHOR>
 */
public enum PushDownIdentifierStateEnum implements CommonEnumInterface {
    /**
     * 下推标识状态
     */
    NO_PUSH_DOWN("noPushDown", "未下推"),
    PART_PUSH_DOWN("partPushDown", "部分下推"),
    ALL_PUSH_DOWN("allPushDown", "已下推");

    private final String code;
    private final String name;

    PushDownIdentifierStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 枚举值
     */
    public static PushDownIdentifierStateEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PushDownIdentifierStateEnum stateEnum : PushDownIdentifierStateEnum.values()) {
            if (code.equals(stateEnum.getCode())) {
                return stateEnum;
            }
        }
        return null;
    }

    /**
     * 根据名称获取枚举
     *
     * @param name 状态名称
     * @return 枚举值
     */
    public static PushDownIdentifierStateEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (PushDownIdentifierStateEnum stateEnum : PushDownIdentifierStateEnum.values()) {
            if (name.equals(stateEnum.getName())) {
                return stateEnum;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     *
     * @param code 状态代码
     * @return 状态名称
     */
    public static String getNameByCode(String code) {
        PushDownIdentifierStateEnum stateEnum = getByCode(code);
        return stateEnum != null ? stateEnum.getName() : "";
    }
}
