package com.yelink.dfscommon.entity.ams;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.entity.ams.dto.ProcedureProcessVO;
import com.yelink.dfscommon.entity.dfs.MaterialEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 生产订单关联表实体
 *
 * <AUTHOR>
 * @Date 2022-08-22 15:15
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_product_order_material")
public class ProductOrderMaterialEntity extends Model<ProductOrderMaterialEntity> {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 生产订单物料行号
     */
    @ApiModelProperty("生产订单物料行号")
    @TableField(value = "line_number")
    private Integer lineNumber;

    /**
     * 关联的销售订单物料行号
     */
    @ApiModelProperty("关联的销售订单物料行号")
    @TableField(value = "related_sale_order_material_line_number")
    private Integer relatedSaleOrderMaterialLineNumber;

    /**
     * 生产订单id
     */
    @ApiModelProperty("生产订单id")
    @TableField(value = "product_order_id")
    private Integer productOrderId;

    /**
     * 生产订单编号
     */
    @ApiModelProperty("生产订单编号")
    @TableField(value = "product_order_number")
    private String productOrderNumber;

    /**
     * 物料 id
     */
    @ApiModelProperty("物料 id")
    @TableField(value = "material_id")
    private Integer materialId;

    /**
     * ERP关联物料行id
     */
    @ApiModelProperty("ERP关联物料行id")
    @TableField(value = "external_material_id")
    private String externalMaterialId;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @TableField(value = "material_code")
    @UnitColumn
    private String materialCode;

    /**
     * 计划数量
     */
    @ApiModelProperty("计划数量")
    @TableField(value = "plan_quantity")
    @UnitFormatColumn
    private Double planQuantity;

    /**
     * 排产数量(关联的非取消状态的生产工单的计划数量之和)
     */
    @ApiModelProperty("排产数量(关联的非取消状态的生产工单的计划数量之和)")
    @TableField(value = "scheduled_production_quantity")
    @UnitFormatColumn
    private Double scheduledProductionQuantity;

    /**
     * 需生产数量/下发数量
     */
    @ApiModelProperty("需生产数量/下发数量")
    @TableField(value = "need_produce_quantity")
    @UnitFormatColumn
    private Double needProduceQuantity;

    /**
     * 完成数量(关联的非取消状态的生产工单的完成数量之和)
     */
    @ApiModelProperty("完成数量(关联的非取消状态的生产工单的完成数量之和)")
    @TableField(value = "finish_count")
    @UnitFormatColumn
    private Double finishCount;

    /**
     * 不合格数
     */
    @ApiModelProperty("不合格数")
    @TableField(value = "unqualified_count")
    @UnitFormatColumn
    private Double unqualifiedCount;


    /**
     * 计划批数
     */
    @ApiModelProperty("计划批数")
    @TableField(value = "planned_batches")
    private Double plannedBatches;

    /**
     * 每批计划数
     */
    @ApiModelProperty("每批计划数")
    @TableField(value = "plans_per_batch")
    private Double plansPerBatch;

    /**
     * 实际批数
     */
    @ApiModelProperty("实际批数")
    @TableField(value = "actual_batches")
    private Double actualBatches;


    /**
     * 计划开始生产时间
     */
    @ApiModelProperty("计划开始生产时间")
    @TableField(value = "plan_product_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planProductStartTime;

    /**
     * 计划生产完成时间
     */
    @ApiModelProperty("计划生产完成时间")
    @TableField(value = "plan_product_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date planProductEndTime;

    /**
     * 实际开始生产时间
     */
    @ApiModelProperty("实际开始生产时间")
    @TableField(value = "actual_product_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualProductStartTime;

    /**
     * 实际生产完成时间
     */
    @ApiModelProperty("实际生产完成时间")
    @TableField(value = "actual_product_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualProductEndTime;

    /**
     * 销售订单编码
     */
    @ApiModelProperty("销售订单编码")
    @TableField(value = "sale_order_code")
    private String saleOrderCode;


    @TableField(exist = false)
    private Integer saleOrderId;

    /**
     * 关联销售订单的物料行ID
     */
    @ApiModelProperty("关联销售订单的物料行ID")
    @TableField(value = "relate_sale_order_material_id")
    private Integer relateSaleOrderMaterialId;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * 排程状态（unScheduled-不排程 toBeScheduled-待排程 scheduled-已排程）
     */
    @ApiModelProperty("排程状态（unScheduled-不排程 toBeScheduled-待排程 scheduled-已排程）")
    @TableField(value = "scheduling_status")
    private String schedulingStatus;

    /**
     * 排程状态名称（unScheduled-不排程 toBeScheduled-待排程 scheduled-已排程）
     */
    @ApiModelProperty("排程状态名称（unScheduled-不排程 toBeScheduled-待排程 scheduled-已排程）")
    @TableField(exist = false)
    private String schedulingStatusName;

    /**
     * 排程方式（produceOnFirst-优先生产   stockOnFirst-优先库存）
     */
    @ApiModelProperty("排程方式（produceOnFirst-优先生产   stockOnFirst-优先库存）")
    @TableField(value = "schedule_mode")
    private String scheduleMode;

    /**
     * 排程方式名称（produceOnFirst-优先生产   stockOnFirst-优先库存）
     */
    @ApiModelProperty("排程方式名称（produceOnFirst-优先生产   stockOnFirst-优先库存）")
    @TableField(exist = false)
    private String scheduleModeName;

    /**
     * 进度（完成率）
     */
    @ApiModelProperty("进度（完成率）")
    @TableField(value = "progress")
    private Double progress;

    /**
     * 生产订单工序进度
     */
    @ApiModelProperty("生产订单工序进度")
    @TableField(value = "procedure_process")
    private Double procedureProcess;

    /**
     * 生产订单工序进度详情
     */
    @ApiModelProperty("生产订单工序进度详情")
    @TableField(value = "procedure_process_detail")
    private String procedureProcessDetail;
    /**
     * 带别名的工序进度详情
     */
    @TableField(exist = false)
    private String procedureAliasProcessDetail;

    /**
     * 合单状态(0-否 1-被合 2-合)
     */
    @ApiModelProperty("合单状态(0-否 1-被合 2-合)")
    @TableField(value = "merged_state")
    private Integer mergedState;

    /**
     * 合单状态(0-否 1-被合 2-合)
     */
    @ApiModelProperty("合单状态(0-否 1-被合 2-合)")
    @TableField(exist = false)
    private String mergedStateName;

    /**
     * 关联的合单号
     */
    @ApiModelProperty("关联的合单号")
    @TableField(value = "merge_order_number")
    private String mergeOrderNumber;

    /**
     * 优先级（1-正常、2-优先、3-加急、4-特急）
     */
    @ApiModelProperty("优先级（1-正常、2-优先、3-加急、4-特急）")
    @TableField(value = "priority")
    private String priority;

    /**
     * 车间编码
     */
    @ApiModelProperty("车间编码")
    @TableField(value = "grid_code")
    private String gridCode;

    /**
     * 车间名称
     */
    @ApiModelProperty("车间名称")
    @TableField(exist = false)
    private String gridName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 导入时间
     */
    @ApiModelProperty("导入时间")
    @TableField(value = "import_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date importTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建人名字
     */
    @ApiModelProperty("创建人名字")
    @TableField(exist = false)
    private String createName;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 同步时间
     */
    @ApiModelProperty("同步时间")
    @TableField(value = "synchronization_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date synchronizationTime;

    /**
     * BOM编码
     */
    @ApiModelProperty("BOM编码")
    @TableField(value = "bom_number")
    private String bomNumber;

    /**
     * BOM版本
     */
    @ApiModelProperty("BOM版本")
    @TableField(value = "bom_version")
    private String bomVersion;


    /**
     * 根据Bom拆分后的层级数，辅助计划的齐备性算法需要使用到
     */
    @ApiModelProperty("根据Bom拆分后的层级数，辅助计划的齐备性算法需要使用到")
    @TableField(value = "bom_layer_num")
    private Integer bomLayerNum;

    /**
     * bom系数，辅助计划的齐备性算法需要使用到
     */
    @ApiModelProperty("bom系数，辅助计划的齐备性算法需要使用到")
    @TableField(value = "bom_ratio")
    private Double bomRatio;

    /**
     * 下单日期/接单日期
     * 关联的销售订单带出
     */
    @ApiModelProperty("下单日期/接单日期 关联的销售订单带出")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderDate;

    /**
     * 要货日期
     * 计划交货时间  --> 要货日期
     * 关联的销售订单带出
     */
    @ApiModelProperty("要货日期 计划交货时间  --> 要货日期 关联的销售订单带出")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date requireGoodsDate;

    /**
     * 发货日期
     * 实际交货时间  -- > 发货日期
     * 关联的销售订单带出
     */
    @ApiModelProperty("发货日期 实际交货时间  -- > 发货日期 关联的销售订单带出")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    /**
     * 单价
     * 关联的销售订单带出
     */
    @ApiModelProperty("单价 关联的销售订单带出")
    @TableField(exist = false)
    private Double price;

    /**
     * 销售数量
     * 关联的销售订单带出
     */
    @ApiModelProperty("销售数量 关联的销售订单带出")
    @TableField(exist = false)
    private Double salesQuantity;

    /**
     * 总价
     * 关联的销售订单带出
     */
    @ApiModelProperty("总价 关联的销售订单带出")
    @TableField(exist = false)
    private Double totalPrice;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    @TableField(exist = false)
    private MaterialEntity materialFields;

    /**
     * 物料 当前库存数量
     */
    @ApiModelProperty("物料 当前库存数量")
    @TableField(exist = false)
    private Double stockQuantity;

    /**
     * 生产订单状态
     */
    @ApiModelProperty("生产订单状态")
    @TableField(exist = false)
    private Integer state;

    /**
     * 生产订单状态名称
     */
    @ApiModelProperty("生产订单状态名称")
    @TableField(exist = false)
    private String stateName;

    /**
     * 物料辅助分析时间（用于物料辅助分析）
     */
    @ApiModelProperty("物料辅助分析时间（用于物料辅助分析）")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date analysisTime;

    /**
     * 同步ERP的物料行表示（同步时可判断是新增还是更新生产订单）
     */
    @ApiModelProperty("同步ERP的物料行表示（同步时可判断是新增还是更新生产订单）")
    @TableField(exist = false)
    private String productRowId;

    /**
     * 物料是否齐套(1-是 0-否) 用于工单排程时按齐套情况导入生产订单
     */
    @ApiModelProperty("物料是否齐套(1-是 0-否) 用于工单排程时按齐套情况导入生产订单")
    @TableField(value = "material_is_complete")
    private Boolean materialIsComplete;

    /**
     * 合格率
     */
    @TableField(exist = false)
    private Double passRate;

    @TableField(exist = false)
    private List<List<ProcedureProcessVO>> processVOS;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    @TableField(value = "sku_id")
    private Integer skuId;

    /**
     * 包装方案编码
     */
    @ApiModelProperty("包装方案编码")
    @TableField(value = "package_scheme_code")
    private String packageSchemeCode;
    /**
     * 过程状态
     */
    @ApiModelProperty("过程状态")
    @TableField(value = "process_status")
    private Integer processStatus;

    /**
     * 过程状态名称
     */
    @ApiModelProperty("生产状态名称")
    @TableField(exist = false)
    private String processStatusName;

    /**
     * 领料状态
     */
    @ApiModelProperty("领料状态")
    @TableField(value = "picking_state_name")
    private String pickingStateName;

    /**
     * 已领料数
     */
    @ApiModelProperty("已领料数")
    @TableField(value = "picking_quantity")
    @UnitFormatColumn
    private Double pickingQuantity;

    /**
     * 已入库数
     */
    @ApiModelProperty("已入库数")
    @TableField(value = "inventory_quantity")
    @UnitFormatColumn
    private Double inventoryQuantity;

    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    @TableField(value = "customer_material_code")
    private String customerMaterialCode;

    /**
     * 客户订单编号
     */
    @ApiModelProperty("客户订单编号")
    @TableField(value = "customer_order_number")
    private String customerOrderNumber;

    /**
     * 客户物料名称
     */
    @ApiModelProperty("客户物料名称")
    @TableField(value = "customer_material_name")
    private String customerMaterialName;

    /**
     * 客户物料规格
     */
    @ApiModelProperty("客户物料规格")
    @TableField(value = "customer_specification")
    private String customerSpecification;

    /**
     * 生产订单物料拓展字段1
     */
    @ApiModelProperty(value = "生产订单物料拓展字段1")
    @TableField(value = "product_order_material_extend_field_one")
    private String productOrderMaterialExtendFieldOne;
    /**
     * 生产订单物料拓展字段2
     */
    @ApiModelProperty(value = "生产订单物料拓展字段2")
    @TableField(value = "product_order_material_extend_field_two")
    private String productOrderMaterialExtendFieldTwo;
    /**
     * 生产订单物料拓展字段3
     */
    @ApiModelProperty(value = "生产订单物料拓展字段3")
    @TableField(value = "product_order_material_extend_field_three")
    private String productOrderMaterialExtendFieldThree;
    /**
     * 生产订单物料拓展字段4
     */
    @ApiModelProperty(value = "生产订单物料拓展字段4")
    @TableField(value = "product_order_material_extend_field_four")
    private String productOrderMaterialExtendFieldFour;
    /**
     * 生产订单物料拓展字段5
     */
    @ApiModelProperty(value = "生产订单物料拓展字段5")
    @TableField(value = "product_order_material_extend_field_five")
    private String productOrderMaterialExtendFieldFive;

    /**
     * 生产订单物料拓展字段6
     */
    @ApiModelProperty(value = "生产订单物料拓展字段6")
    @TableField(value = "product_order_material_extend_field_six")
    private String productOrderMaterialExtendFieldSix;
    /**
     * 生产订单物料拓展字段7
     */
    @ApiModelProperty(value = "生产订单物料拓展字段7")
    @TableField(value = "product_order_material_extend_field_seven")
    private String productOrderMaterialExtendFieldSeven;
    /**
     * 生产订单物料拓展字段8
     */
    @ApiModelProperty(value = "生产订单物料拓展字段8")
    @TableField(value = "product_order_material_extend_field_eight")
    private String productOrderMaterialExtendFieldEight;
    /**
     * 生产订单物料拓展字段9
     */
    @ApiModelProperty(value = "生产订单物料拓展字段9")
    @TableField(value = "product_order_material_extend_field_nine")
    private String productOrderMaterialExtendFieldNine;
    /**
     * 生产订单物料拓展字段10
     */
    @ApiModelProperty(value = "生产订单物料拓展字段10")
    @TableField(value = "product_order_material_extend_field_ten")
    private String productOrderMaterialExtendFieldTen;

    @TableField(exist = false)
    private String productOrderMaterialExtendFieldOneName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldTwoName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldThreeName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldFourName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldFiveName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldSixName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldSevenName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldEightName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldNineName;
    @TableField(exist = false)
    private String productOrderMaterialExtendFieldTenName;
    /**
     * 计划理论工时
     */
    @TableField(exist = false)
    private Double planTheoryHour;

    @TableField(exist = false)
    private Double theoryHour;


    @Override
    public Serializable pkVal() {
        return this.productOrderId;
    }

    public interface Insert {
    }

    public interface Update {
    }
}
