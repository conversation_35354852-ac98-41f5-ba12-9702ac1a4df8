package com.yelink.dfscommon.entity.ams;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.entity.dfs.MaterialEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 采购订单关联物料
 * <AUTHOR>
 * @Date 2021-04-10 16:43
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@LogTag(bean = "purchaseMaterialServiceImpl", method = "getById", param = "id")
@TableName("ams_purchase_material")
public class PurchaseMaterialEntity extends Model<PurchaseMaterialEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty("ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 采购订单ID
     */
    @ApiModelProperty("采购订单ID")
    @TableField(value = "purchase_id")
    private Integer purchaseId;

    /**
     * 采购订单单号
     */
    @ApiModelProperty("采购订单单号")
    @TableField(value = "purchase_code")
    private String purchaseCode;

    /**
     * 物料id
     */
    @ApiModelProperty("物料id")
    @TableField(value = "material_id")
    private Integer materialId;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @TableField(value = "code")
    @LogTag(isKey = true, name = "物料编码")
    @UnitColumn
    private String code;

    /**
     * 实际采购数量
     */
    @ApiModelProperty("实际采购数量")
    @TableField(value = "num")
    @LogTag(name = "实际采购数量")
    @UnitFormatColumn
    private Double num;

    /**
     * 计划采购数量
     */
    @ApiModelProperty("计划采购数量")
    @TableField(value = "plan_num")
    @LogTag(name = "计划采购数量")
    @UnitFormatColumn
    private Double planNum;

    /**
     * 赠品数量
     */
    @ApiModelProperty("赠品数量")
    @TableField(value = "gift_quantity")
    @LogTag(name = "赠品数量")
    @UnitFormatColumn
    private Double giftQuantity;

    /**
     * 成本单价
     */
    @ApiModelProperty("成本单价")
    @TableField(value = "price")
    @LogTag(name = "单价")
    @UnitFormatColumn
    private Double price;

    /**
     * 单位
     */
//    @TableField(value = "comp")
//    private String comp;

    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @TableField(value = "material_desc")
    private String materialDesc;

    /**
     * 物料备注
     */
    @ApiModelProperty("物料备注")
    @TableField(value = "material_remark")
    private String materialRemark;

    /**
     * 采购总价
     */
    @ApiModelProperty("采购总价")
    @TableField(value = "sum_price")
    private Double sumprice;

    /**
     * 需求采购单Id
     */
    @ApiModelProperty("需求采购单Id")
    @TableField(exist = false)
    private Integer requestId;
    /**
     * 销售订单编号
     */
    @ApiModelProperty("销售订单编号")
    @TableField(exist = false)
    private String saleOrderNumber;

    /**
     * 节点名称
     */
    @ApiModelProperty("节点名称")
    @TableField(exist = false)
    private String nodeDefinitionName;

    /**
     * 节点编号
     */
    @ApiModelProperty("节点编号")
    @TableField(exist = false)
    private String nodeDefinitionCode;

    /**
     * 节点名称
     */
    @ApiModelProperty("节点名称")
    @TableField(exist = false)
    private String configureCode;

    /**
     * 收货数量：采购需求对应的采购订单所关联的收货单，对应收货数量求和，除创建和取消状态的单据。
     */
    @ApiModelProperty("收货数量：采购需求对应的采购订单所关联的收货单，对应收货数量求和，除创建和取消状态的单据。")
    @TableField(exist = false)
    @UnitFormatColumn
    private Double receiptQuantity;

    /**
     * 入库数量：关联的入库单(类型为采购入库)，对应数量求和，除创建和取消状态的单据。
     */
    @ApiModelProperty("入库数量：关联的入库单(类型为采购入库)，对应数量求和，除创建和取消状态的单据。")
    @TableField(exist = false)
    @UnitFormatColumn
    private Double inventoryQuantity;

    /**
     * 检验合格数量：关联的来料检验单(合格类型为合格)，对应的数量求和。
     */
    @ApiModelProperty("检验合格数量：关联的来料检验单(合格类型为合格)，对应的数量求和。")
    @TableField(exist = false)
    @UnitFormatColumn
    private Double qualifiedQuantity;

    /**
     * 检验不合格数量：关联的来料检验单(合格类型为不合格)，对应的数量求和。
     */
    @ApiModelProperty("检验不合格数量：关联的来料检验单(合格类型为不合格)，对应的数量求和。")
    @TableField(exist = false)
    @UnitFormatColumn
    private Double unqualifiedQuantity;

    /**
     * 物料字段
     */
    @TableField(exist = false)
    private MaterialEntity materialFields;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    @TableField(value = "sku_id")
    private Integer skuId;
    
    /**
     * 要货日期
     */
    @ApiModelProperty("要货日期")
    @TableField(value = "purchase_date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date purchaseDate;

    /**
     * 供应商物料编码
     */
    @ApiModelProperty("供应商物料编码")
    @TableField(exist = false)
    private String supplierMaterialCode;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(value = "update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
