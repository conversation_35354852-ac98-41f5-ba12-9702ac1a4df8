package com.yelink.dfscommon.entity.dfs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021-03-17 11:59
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 工单id
     */
    @ApiModelProperty("工单id")
    @TableId(value = "work_order_id", type = IdType.AUTO)
    private Integer workOrderId;

    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    private String workOrderNumber;

    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    @LogTag(name = "工单名称")
    private String workOrderName;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @LogTag(name = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 截止时间
     */
    @ApiModelProperty("截止时间")
    @LogTag(name = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @LogTag(name = "实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    @LogTag(name = "实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualEndDate;

    /**
     * 状态
     * 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消
     */
    @ApiModelProperty("状态 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消")
    private Integer state;

    /**
     * 派工状态
     */
    @ApiModelProperty("派工状态 toBeAssigned-待派工、assigned-已派工")
    private String assignmentState;

    /**
     * 存在项目合同 0 不存在 1 存在
     */
    @ApiModelProperty("存在项目合同")
    private Boolean projectContract;

    /**
     * 项目定义ID
     */
    @ApiModelProperty("项目定义ID")
    private String projectDefineId;

    /**
     * 合同ID
     */
    @ApiModelProperty("合同ID")
    private String contractId;

    /**
     * 项目名称
     */
    private String projectDefineName;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 派工状态名称
     */
    @ApiModelProperty("派工状态名称")
    private String assignmentStateName;

    /**
     * 产线模型编号
     */
    @ApiModelProperty("产线编号")
    private String lineModelCode;
    /**
     * 产线模型名称
     */
    @ApiModelProperty("产线模型名称")
    private String lineModelName;
    /**
     * 产线编号
     */
    @ApiModelProperty("产线编号")
    private String lineCode;

    /**
     * 产线名称
     */
    @ApiModelProperty("产线名称")
    @LogTag(name = "产线名称")
    private String lineName;

    /**
     * 产线id
     */
    @ApiModelProperty("产线id")
    private Integer lineId;
    /**
     * 产线id
     */
//    private String lineIds;

    /**
     * 计划数量
     */
    @ApiModelProperty("计划数量")
    @LogTag(name = "计划数量")
    @UnitFormatColumn
    private Double planQuantity;

    /**
     * 计划批数
     */
    @ApiModelProperty("计划批数")
    private Double plannedBatches;

    /**
     * 每批计划数
     */
    @ApiModelProperty("每批计划数")
    private Double plansPerBatch;

    /**
     * 实际批数
     */
    @ApiModelProperty("实际批数")
    private Double actualBatches;

    /**
     * 关联销售订单的物料行ID
     */
    @ApiModelProperty("关联销售订单的物料行ID")
    private Integer relateOrderMaterialId;

    /**
     * 物料编号
     */
    @ApiModelProperty("物料编号")
    @LogTag(name = "物料编号")
    @UnitColumn
    private String materialCode;

    private String materialName;

    /**
     * 不合格量
     */
    @ApiModelProperty("不合格量")
    @LogTag(name = "不合格数量")
    @UnitFormatColumn
    private Double unqualified;

    /**
     * 关联的父工单
     */
    @ApiModelProperty("关联的父工单")
//    @LogTag(name = "关联的父工单号")
    private String pnumber;

    /**
     * 关联的子工单
     */
//    private List<WorkOrderEntity> subWorkOrder;

    /**
     * 是否有父工单 1是 0否
     */
    @ApiModelProperty("是否有父工单 1是 0否")
    private Boolean isPid;

    /**
     * 子工单顺序
     */
    @ApiModelProperty("子工单顺序")
    @LogTag(name = "子工单顺序")
    private String idSequence;

    /**
     * 进度（完成率）
     */
    @ApiModelProperty("进度（完成率）")
    private Double progress;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @LogTag(name = "备注")
    private String remark;

    /**
     * 物料当前库存
     */
    @ApiModelProperty("物料当前库存")
    private Double currentInventory;
    /**
     * 物料是否齐套
     */
    @ApiModelProperty("物料是否齐套")
    private Boolean materialIsComplete;
    /**
     * 物料齐套数量
     */
    @ApiModelProperty("物料齐套数量")
    private Double materialCompleteNum;
    /**
     * 物料欠套数量
     */
    @ApiModelProperty("物料欠套数量")
    private Double materialOweNum;
    /**
     * 理论工作时长
     */
    @ApiModelProperty("理论工作时长")
    private Double theoreticalWorkingHours;

    /**
     * 类型（该类型用于指标）
     */
    @ApiModelProperty("类型（该类型用于指标）")
    private String type;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    @TableField(value = "business_type")
    private String businessType;

    @ApiModelProperty("业务类型名称")
    @TableField(exist = false)
    @LogTag(name = "业务类型名称")
    private String businessTypeName;

    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型")
    @TableField(value = "order_type")
    private String orderType;

    @ApiModelProperty("单据类型名称")
    @TableField(exist = false)
    @LogTag(name = "单据类型名称")
    private String orderTypeName;

    /**
     * CreateBy
     */
    @ApiModelProperty("CreateBy")
    private String createBy;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String createName;

    /**
     * CreateDate
     */
    @ApiModelProperty("CreateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人
     */
    @ApiModelProperty("UpdateBy")
    private String updateBy;

    private String updateByName;

    /**
     * 更新时间
     */
    @ApiModelProperty("UpdateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 删除人
     */
    private String deleteName;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deleteTime;

    /**
     * 有效工时
     */
    @ApiModelProperty("有效工时")
    private Double effectiveHours;

    /**
     * 计划员真实姓名
     */
    @ApiModelProperty("计划员真实姓名")
    private String magNickname;

    /**
     * 计划员手机号
     */
    @ApiModelProperty("计划员手机号")
    private String magPhone;

    /**
     * 计划员账号
     */
    @ApiModelProperty("计划员账号")
    @LogTag(name = "计划员", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String magName;

    /**
     * 已完成数量
     */
    @ApiModelProperty("已完成数量")
    @UnitFormatColumn
    private Double finishCount;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    @UnitFormatColumn
    private Double inputTotal;

    /**
     * 工单接收人（工单完成时通知人员账号，逗号隔开）
     */
    @ApiModelProperty("工单接收人（工单完成时通知人员账号，逗号隔开）")
    private String noticeUsername;

    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    @LogTag(name = "优先级")
    private String priority;

    /**
     * 工单状态名称 0-创建 1-发放 2-完成 3-关闭 4-取消
     */
    @ApiModelProperty("工单状态名称 0-创建 1-发放 2-完成 3-关闭 4-取消")
    @LogTag(name = "状态")
    private String stateName;

    public String getStateName() {
        if (this.state != null) {
            return WorkOrderStateEnum.getNameByCode(this.getState());
        }
        return null;
    }

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    private String customerCode;

    /**
     * 计划交付时间/计划开始生产时间
     */
    @ApiModelProperty("计划交付时间/计划开始生产时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handOverDate;

    /**
     * 订单号，用逗号隔开
     */
    @ApiModelProperty("订单号，用逗号隔开")
    private String productOrderNumber;

    private Integer productOrderId;

    /**
     * 销售订单号，用逗号隔开
     */
    @ApiModelProperty("销售订单号，用逗号隔开")
    private String saleOrderNumber;


    private Integer saleOrderId;

    /**
     * 包装数
     */
    @ApiModelProperty("包装数")
    private Double packageQuantity;

    /**
     * 报工系数
     */
    @ApiModelProperty("报工系数")
    private Double coefficient;

    /**
     * 是否备料
     */
    @ApiModelProperty("是否备料")
    @LogTag(name = "是否备料")
    private Boolean prepared;

    /**
     * 是否发料
     */
    @ApiModelProperty("是否发料")
    @LogTag(name = "是否发料")
    private Boolean assigned;

    /**
     * erp关联单据编号
     */
    @ApiModelProperty("erp关联单据编号")
    @LogTag(name = "erp关联单据编号")
    private String erpDocumentCode;

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    @LogTag(name = "是否下发")
    private Boolean issue;

    /**
     * 生产订单列表
     */
    @ApiModelProperty("生产订单列表")
    @UnitFormatContainer
    private List<ProductOrderEntity> productOrderList;
    /**
     * 销售订单列表
     */
    @ApiModelProperty("销售订单列表")
    @UnitFormatContainer
    private List<SaleOrderEntity> saleOrderList;

    @ApiModelProperty("物料列表")
    private List<MaterialEntity> materialEntityList;

    /**
     * 领料出库单列表
     */
    @ApiModelProperty("领料出库单列表")
    private List<StockInAndOutEntity> takeOutList;

    /**
     * 入库单列表
     */
    @ApiModelProperty("入库单列表")
    private List<StockInAndOutEntity> inputList;

    /**
     * 退库单列表
     */
    @ApiModelProperty("退库单列表")
    private List<StockInAndOutEntity> returnList;

    /**
     * 计量重量(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量重量(工单物料计划数量记录计算用)")
    private Double measurementQuantity;

    /**
     * 计量单位(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量单位(工单物料计划数量记录计算用)")
    private String measurementUnit;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    @LogTag(name = "批准人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String approver;

    /**
     * 审核人名字
     */
    @ApiModelProperty("审核人名字")
    private String approverName;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    @LogTag(name = "审核人", bean = "sysUserServiceImpl", method = "getNicknameByUsername")
    private String actualApprover;

    /**
     * 实际审批人签名地址
     */
    @ApiModelProperty("实际审批人签名地址")
    private String actualApproverSignatureUrl;

    /**
     * 实际审批人名字
     */
    @ApiModelProperty("实际审批人名字")
    private String actualApproverName;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    @LogTag(name = "审批状态")
    private Integer approvalStatus;

    /**
     * 审批状态名称
     */
    @ApiModelProperty("审批状态名称")
    private String approvalStatusName;

    /**
     * 审批建议
     */
    @ApiModelProperty("审批建议")
    @LogTag(name = "审批建议")
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 规则id（批量创建工单）
     */
//    private Integer numberRuleId;

    /**
     * 生产时间段
     * <p>
     * [
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * {"startDate": "2021-11-11 13:28:33:00","endDate": "2021-11-11 13:33:04:00"}
     * ]
     */
    @ApiModelProperty("生产时间段")
    private List<Map> produceRange;

    /**
     * 物料iD
     */
    @ApiModelProperty("物料iD")
    private Integer materialId;
    /**
     * 工艺Id
     */
    @ApiModelProperty("工艺Id")
    private Integer craftId;

    /**
     * 工艺编号
     */
    @ApiModelProperty("工艺编号")
    private String craftCode;
    /**
     * 产前状态
     */
    @ApiModelProperty("产前状态")
    private Boolean prenatalStatus;

    /**
     * 计划工时
     */
    @ApiModelProperty("计划工时")
    private Double plannedWorkingHours;

    /**
     * 实际工时
     */
    @ApiModelProperty("实际工时")
    private Double actualWorkingHours;
    /**
     * 工单执行
     */
    @ApiModelProperty("工单执行")
    private String executionStatus;
    /**
     * 工单执行
     */
    @ApiModelProperty("工单执行")
    private String executionStatusName;
    /**
     * 时差
     */
    @ApiModelProperty("时差")
    private Double timeDifference;

    /**
     * 调机时长
     */
    @ApiModelProperty("调机时长")
    private String workOrderRecordTime;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    private Double productCount;

    /**
     * 待排数量
     */
    @ApiModelProperty("待排数量")
    @UnitFormatColumn
    private Double pendentQuantity;

    /**
     * 流转数量
     */
    @ApiModelProperty("流转数量")
    @UnitFormatColumn
    private Double inStockCount;

    /**
     * 工作中心ID
     */
    @ApiModelProperty("工作中心ID")
    private Integer workCenterId;

    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    private String workCenterName;


    /**
     * 基本生产单元名称
     */
    @ApiModelProperty(value = "基本生产单元编码")
    private String productionBasicUnitCode;

    /**
     * 基本生产单元名称
     */
    @ApiModelProperty("基本生产单元名称")
    private String productionBasicUnitName;

    /**
     * 入库数量（先保留一段时间，避免业务存在问题，后续待仓库kafka消息推送后再删除，使用inventoryQuantity字段）
     */
    @Deprecated
    @ApiModelProperty("入库数量")
    private Double inputCount;

    /**
     * 已领料数
     */
    @ApiModelProperty("已领料数")
    @UnitFormatColumn
    private Double pickingQuantity;

    /**
     * 已入库数
     */
    @ApiModelProperty("已入库数")
    @UnitFormatColumn
    private Double inventoryQuantity;

    /**
     * 供应商code
     */
    @ApiModelProperty("供应商code")
    private String supplierCode;

    /**
     * 供应商名字
     */
    @ApiModelProperty("供应商名字")
    private String supplierName;

    /**
     * 是否正在计数
     */
    @ApiModelProperty("是否正在计数")
    @Builder.Default
    private Boolean isCountNow = false;
    /**
     * 附件列表
     */
    @ApiModelProperty("附件列表")
    private List<AppendixEntity> appendixEntities;

    /**
     * 工序id
     */
    @ApiModelProperty("工序id")
    private String procedureIds;

    /**
     * 工序名称(用于大屏展示)
     */
    @ApiModelProperty("工序名称(用于大屏展示)")
    private String procedureName;
    @ApiModelProperty("工序别名")
    private String procedureAlias;

    /**
     * 有效工时=报工数量/标准产能
     */
    private Double effectiveWorkingHour;

    /**
     * 计数器累计参考值
     */
    private Double autoCount;

    /**
     * 绑定的工艺工序列表
     */
    @ApiModelProperty("绑定的工艺工序列表")
    private List<CraftProcedureEntity> craftProcedureEntities;

    /**
     * 流转时长
     */
    @ApiModelProperty("流转时长")
    private Double circulationDuration;

    /**
     * 数据导入时间
     */
    @ApiModelProperty("数据导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date importTime;

    /**
     * 第一次投入时是否更改了投产时间
     */
    private Boolean isChangeInvestTime;

    /**
     * 旧投产时间
     */
    private Date oldInvestTime;

    /**
     * 物料库存数量
     */
    @ApiModelProperty("物料库存数量")
    private Double stockQuantity;

    /**
     * 状态变更时间
     */
    @ApiModelProperty("状态变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stateChangeTime;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    private MaterialEntity materialFields;

    /**
     * 排产状态(0-不可排,1-待排产,2-已排产)
     */
    @ApiModelProperty("排产状态(0-不可排,1-待排产,2-已排产)")
    private Integer planState;

    /**
     * 排程顺序
     */
    @ApiModelProperty("排程顺序")
    private Integer schedulingSequence;

    /**
     * 排程状态
     */
    @ApiModelProperty("排程状态")
    private Integer schedulingState;

    /**
     * 排程状态名称
     */
    @ApiModelProperty("排程状态名称")
    private String schedulingStateName;

    /**
     * 排程数量
     */
    @ApiModelProperty("排程数量")
    private Double schedulingCount;

    @ApiModelProperty("流转状态(0-不可流转,1-可流转,2-流转超时,3-已流转)")
    private Integer circulationState;

    /**
     * 班组ID
     */
    @ApiModelProperty("班组ID")
    private Integer teamId;

    /**
     * 设备ID
     */
    @ApiModelProperty("设备ID")
    private Integer deviceId;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 生产基本单元id
     */
    private Integer productionBasicUnitId;

    /**
     * 隔离ID(工作中心ID+“-”+生产基本单元id)
     */
    private String isolationId;

    @ApiModelProperty("绑定的生产基本单元列表")
    @TableField(exist = false)
    private List<WorkOrderBasicUnitRelationEntity> productBasicUnits;

    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    private String customerMaterialCode;

    /**
     * 客户物料名称
     */
    @ApiModelProperty("客户物料名称")
    private String customerMaterialName;

    /**
     * 客户物料规格
     */
    @ApiModelProperty("客户物料规格")
    private String customerSpecification;

    /**
     * 班组名称
     */
    @ApiModelProperty("班组名称")
    private String teamName;

    /**
     * 班组长真实姓名
     */
    @ApiModelProperty("班组长真实姓名")
    private String teamLeaderNickName;

    /**
     * 班组组员名称
     */
    @ApiModelProperty("班组组员名称")
    private String teamMemberNickNames;

    /**
     * 工单事件(大屏的工单备注信息要用)
     */
    private String workOrderEvent;

    private String planStateName;

    private String circulationStateName;

    /**
     * 工作中心编码
     */
    private String workCenterCode;

    /**
     * 工作中心type
     */
    private String workCenterType;

    /**
     * 工作中心类型名称
     */
    private String workCenterTypeName;

    /**
     * 工作中心关联资源type
     */
    private String workCenterRelevanceType;

    /**
     * 工单关联班组id
     */
    private List<Integer> relevanceTeamIds;

    /**
     * 工单关联班组名称
     */
    private List<String> relevanceTeamNames;

    /**
     * 工单关联设备id
     */
    private List<Integer> relevanceDeviceIds;
    /**
     * 工单关联设备名称
     */
    private List<String> relevanceDeviceNames;

    /**
     * 工单关联制造单元id
     */
    private List<Integer> relevanceLineIds;
    /**
     * 工单关联制造单元名称
     */
    private List<String> relevanceLineNames;


    ///**
    // * 不良描述
    // */
    //private String defectDesc;
    ///**
    // * 报工照片
    // */
    //private List<String> pictureUrls;

    /**
     * 合格率= 完成数/（不良数+完成数）
     */
    private Double passRate;

    /**
     * 工艺工序id
     */
    private Integer craftProcedureId;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    private Integer skuId;

    /**
     * 包装方案编码
     */
    @ApiModelProperty("包装方案编码")
    private String packageSchemeCode;

    /**
     * 关联的销售订单物料行号
     */
    @ApiModelProperty("关联的销售订单物料行号")
    private Integer relatedSaleOrderMaterialLineNumber;

    /**
     * 关联的生产订单物料行号
     */
    @ApiModelProperty("关联的生产订单物料行号")
    private Integer relatedProductOrderMaterialLineNumber;

    /**
     * 生产订单按工艺路线下推次数
     */
    @ApiModelProperty("生产订单按工艺路线下推次数")
    private Integer pushTimes;

    /**
     * 是否已打印
     */
    @ApiModelProperty("是否已打印")
    private Boolean isPrint;

    /**
     * 领料状态名称
     */
    @ApiModelProperty("领料状态名称")
    private String pickingStateName;

    /**
     * 销售订单
     */
    private SaleOrderVO saleOrderVO;

    /**
     * 生产订单
     */
    private ProductOrderEntity productOrderEntity;

    /**
     * 关联的批次计划总数
     */
    private Double relatedBarCodePlanSum;

    /**
     * 是否通过对外接口插入/更新
     * 某些字段接口需要提供更新，但是不能影响系统原本的业务
     */
    private Boolean operateByApi = false;

    /**
     * 上料防错类型
     */
    @ApiModelProperty(value = "上料防错类型")
    private String materialCheckType;

    /**
     * 上料防错类型名称
     */
    @ApiModelProperty(value = "上料防错类型名称")
    private String materialCheckTypeName;

    /**
     * 投产结果明细
     */
    @ApiModelProperty(value = "投产结果明细")
    private String investCheckResultDetail;

    /**
     * 上料防错是否支持替代料，0否1是
     */
    @ApiModelProperty("上料防错是否支持替代料")
    private Boolean materialCheckReplace;

    /**
     * 使用到的编码规则id
     *
     * @param
     * @return
     */
    private Integer numberRuleId;

    /**
     * 工单扩展字段1
     */
    @ApiModelProperty(value = "工单扩展字段1")
    private String workOrderExtendFieldOne;
    /**
     * 工单扩展字段2
     */
    @ApiModelProperty(value = "工单扩展字段2")
    private String workOrderExtendFieldTwo;
    /**
     * 工单扩展字段3
     */
    @ApiModelProperty(value = "工单扩展字段3")
    private String workOrderExtendFieldThree;
    /**
     * 工单扩展字段4
     */
    @ApiModelProperty(value = "工单扩展字段4")
    private String workOrderExtendFieldFour;
    /**
     * 工单扩展字段5
     */
    @ApiModelProperty(value = "工单扩展字段5")
    private String workOrderExtendFieldFive;

    /**
     * 工单扩展字段6
     */
    @ApiModelProperty(value = "工单扩展字段6")
    private String workOrderExtendFieldSix;
    /**
     * 工单扩展字段7
     */
    @ApiModelProperty(value = "工单扩展字段7")
    private String workOrderExtendFieldSeven;
    /**
     * 工单扩展字段8
     */
    @ApiModelProperty(value = "工单扩展字段8")
    private String workOrderExtendFieldEight;
    /**
     * 工单扩展字段9
     */
    @ApiModelProperty(value = "工单扩展字段9")
    private String workOrderExtendFieldNine;
    /**
     * 工单扩展字段10
     */
    @ApiModelProperty(value = "工单扩展字段10")
    private String workOrderExtendFieldTen;
    /**
     * 工单物料扩展字段1
     */
    @ApiModelProperty(value = "工单物料扩展字段1")
    private String workOrderMaterialExtendFieldOne;
    /**
     * 工单物料扩展字段2
     */
    @ApiModelProperty(value = "工单物料扩展字段2")
    private String workOrderMaterialExtendFieldTwo;
    /**
     * 工单物料扩展字段3
     */
    @ApiModelProperty(value = "工单物料扩展字段3")
    private String workOrderMaterialExtendFieldThree;
    /**
     * 工单物料扩展字段4
     */
    @ApiModelProperty(value = "工单物料扩展字段4")
    private String workOrderMaterialExtendFieldFour;
    /**
     * 工单物料扩展字段5
     */
    @ApiModelProperty(value = "工单物料扩展字段5")
    private String workOrderMaterialExtendFieldFive;
    /**
     * 工单物料扩展字段6
     */
    @ApiModelProperty(value = "工单物料扩展字段6")
    private String workOrderMaterialExtendFieldSix;
    /**
     * 工单物料扩展字段7
     */
    @ApiModelProperty(value = "工单物料扩展字段7")
    private String workOrderMaterialExtendFieldSeven;
    /**
     * 工单物料扩展字段8
     */
    @ApiModelProperty(value = "工单物料扩展字段8")
    private String workOrderMaterialExtendFieldEight;
    /**
     * 工单物料扩展字段9
     */
    @ApiModelProperty(value = "工单物料扩展字段9")
    private String workOrderMaterialExtendFieldNine;
    /**
     * 工单物料扩展字段10
     */
    @ApiModelProperty(value = "工单物料扩展字段10")
    private String workOrderMaterialExtendFieldTen;

    /**
     * 投产检查结果（true--通过  false--不通过  null--空）
     */
    @ApiModelProperty(value = "投产检查结果（true--通过  false--不通过  null--空）")
    private Boolean investCheckResult;


    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    private String businessUnitName;

    /**
     * 投产检查结果名称
     */
    @ApiModelProperty(value = "投产检查结果名称")
    private String investCheckResultName;

    /**
     * 维修数量
     */
    @ApiModelProperty("维修数量")
    private Integer maintainCount;

    /**
     * 是否允许自动新增报工记录
     * 条件：符合`完工自动报工`的配置条件下，完工时检查有无报工记录，如无报工记录，自动补充等于计划数的报工记录
     */
    @ApiModelProperty("是否允许自动新增报工记录")
    private Boolean isAutoReport;

    /**
     * 扩展字段中文名
     */
    private String workOrderExtendFieldOneName;
    private String workOrderExtendFieldTwoName;
    private String workOrderExtendFieldThreeName;
    private String workOrderExtendFieldFourName;
    private String workOrderExtendFieldFiveName;
    private String workOrderExtendFieldSixName;
    private String workOrderExtendFieldSevenName;
    private String workOrderExtendFieldEightName;
    private String workOrderExtendFieldNineName;
    private String workOrderExtendFieldTenName;
    private String workOrderMaterialExtendFieldOneName;
    private String workOrderMaterialExtendFieldTwoName;
    private String workOrderMaterialExtendFieldThreeName;
    private String workOrderMaterialExtendFieldFourName;
    private String workOrderMaterialExtendFieldFiveName;
    private String workOrderMaterialExtendFieldSixName;
    private String workOrderMaterialExtendFieldSevenName;
    private String workOrderMaterialExtendFieldEightName;
    private String workOrderMaterialExtendFieldNineName;
    private String workOrderMaterialExtendFieldTenName;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date fakerTime;

    /**
     * 单件理论工时
     */
    private Double theoryHour;
    /**
     * 产出理论工时
     */
    private Double produceTheoryHour;
    /**
     * 计划理论工时
     */
    private Double planTheoryHour;

    /**
     * 是否委外
     */
    private Boolean isSubcontract;

    /**
     * 原工单号
     */
    @ApiModelProperty("原工单号")
    private String originalWorkOrderNumber;

    private Boolean autoCreatePlan;

}
