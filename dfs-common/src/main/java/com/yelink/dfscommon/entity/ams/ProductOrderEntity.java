package com.yelink.dfscommon.entity.ams;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.dto.dfs.CraftRouteDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.dfs.BomRawMaterialEntity;
import com.yelink.dfscommon.entity.dfs.CraftProcedureEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 生产订单实体
 * <AUTHOR>
 * @Date 2022-08-22 15:15
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_product_order")
public class ProductOrderEntity extends Model<ProductOrderEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * 生产订单id
     */
    @ApiModelProperty(value = "生产订单id")
    @TableId(value = "product_order_id", type = IdType.AUTO)
    private Integer productOrderId;

    /**
     * 生产订单编号
     */
    @ApiModelProperty(value = "生产订单编号")
    @TableField(value = "product_order_number")
    private String productOrderNumber;

    @ApiModelProperty("单据类型")
    private String orderType;

    @ApiModelProperty("业务类型")
    private String type;

    /**
     * 生产订单状态  1-创建，2-发放，3- 完成 4-关闭 5-取消
     */
    @ApiModelProperty(value = "生产订单状态  1-创建，2-发放，3- 完成 4-关闭 5-取消")
    @TableField(value = "state")
    private Integer state;

    /**
     * 生产订单状态名字
     */
    @ApiModelProperty("生产订单状态名字")
    @TableField(exist = false)
    private String stateName;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @TableField(value = "customer_code")
    private String customerCode;

    /**
     * 关联的上级生产订单号
     */
    @ApiModelProperty(value = "关联的上级生产订单号")
    @TableField(value = "related_order_num")
    private String relatedOrderNum;

    /**
     * 关联的根订单
     */
    @ApiModelProperty(value = "关联的根订单")
    @TableField(value = "related_root_order_num")
    private String relatedRootOrderNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * 指定审批人
     */
    @ApiModelProperty("指定审批人")
    @TableField(value = "approver")
    private String approver;

    /**
     * 指定审批人名字
     */
    @ApiModelProperty("指定审批人名字")
    @TableField(exist = false)
    private String approverName;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    @TableField(value = "actual_approver")
    private String actualApprover;

    /**
     * 实际审批人签名地址
     */
    @ApiModelProperty("实际审批人签名地址")
    @TableField(exist = false)
    private String actualApproverSignatureUrl;

    /**
     * 实际审批人名字
     */
    @ApiModelProperty("实际审批人名字")
    @TableField(exist = false)
    private String actualApproverName;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    @TableField(value = "approval_status")
    private Integer approvalStatus;

    /**
     * 审批状态名称
     */
    @ApiModelProperty("审批状态名称")
    @TableField(exist = false)
    private String approvalStatusName;

    /**
     * 审批建议
     */
    @ApiModelProperty("审批建议")
    @TableField(value = "approval_suggestion")
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @TableField(value = "approval_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(value = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 导入时间
     */
    @ApiModelProperty("导入时间")
    @TableField(value = "import_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date importTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建人名字
     */
    @ApiModelProperty("创建人名字")
    @TableField(exist = false)
    private String createName;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "update_by")
    private String updateBy;
    /**
     * 修改人名字
     */
    @ApiModelProperty("修改人名字")
    @TableField(exist = false)
    private String updateName;

    /**
     * 生产订单关联的物料
     */
    @ApiModelProperty("生产订单关联的物料")
    @TableField(exist = false)
    @UnitFormatContainer
    private List<ProductOrderMaterialEntity> productOrderMaterials;

    /**
     * 关联订单物料,用于列表展示
     */
    @ApiModelProperty("关联订单物料,用于列表展示")
    @TableField(exist = false)
    @UnitFormatContainer
    private ProductOrderMaterialEntity productOrderMaterial;

    /**
     * 关联的销售订单id
     */
    @ApiModelProperty("关联的销售订单id")
    @TableField(exist = false)
    private Integer saleOrderId;

    /**
     * 新增生产订单时关联的销售订单
     */
    @ApiModelProperty("新增生产订单时关联的销售订单")
    @TableField(exist = false)
    private String saleOrderCode;

    /**
     * 关联销售订单创建时间
     */
    @ApiModelProperty("关联销售订单创建时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderCreateTime;

    /**
     * 销售数量
     */
    @ApiModelProperty("销售数量")
    @TableField(exist = false)
    private Double salesQuantity;

    /**
     * 客户名称
     * 关联的销售订单带出
     */
    @ApiModelProperty("客户名称 关联的销售订单带出")
    @TableField(value = "customer_name")
    private String customerName;
    /**
     * 客户联系方式
     * 关联的销售订单带出
     */
    @ApiModelProperty("客户联系方式 关联的销售订单带出")
    @TableField(exist = false)
    private String customerMobile;
    /**
     * 客户地址
     * 关联的销售订单带出
     */
    @ApiModelProperty("客户地址 关联的销售订单带出")
    @TableField(exist = false)
    private String customerAddr;


    /**
     * 工艺工序，生产订单关联的生产工单关联的工艺
     */
    @ApiModelProperty("工艺工序")
    @TableField(exist = false)
    List<CraftProcedureEntity> craftProcedureEntities;


    /**
     * 工艺工序，生产订单关联物料关联的工艺
     */
    @ApiModelProperty("工艺工序")
    @TableField(exist = false)
    List<CraftProcedureEntity> procedureEntities;
    /**
     * 工艺路线图
     */
    @ApiModelProperty("工艺路线图")
    @TableField(exist = false)
    private CraftRouteDTO craftRouteDTO;

    /**
     * 所关联的工单列表
     */
    @ApiModelProperty("所关联的工单列表")
    @TableField(exist = false)
    private List<WorkOrderEntity> workOrderEntities;

    /**
     * 子订单
     */
    @ApiModelProperty("子订单")
    @TableField(exist = false)
    private List<ProductOrderEntity> subEntities;

    /**
     * 物料Bom信息
     */
    @ApiModelProperty("物料Bom信息")
    @TableField(exist = false)
    private List<BomRawMaterialEntity> bomRawMaterialEntities;

    /**
     * 生产备注
     */
    @ApiModelProperty("生产备注信息")
    @TableField(exist = false)
    private List<ProductOrderReportRemarkEntity> productOrderReportRemarkEntities;

    /**
     * 工序id
     */
    @ApiModelProperty("工序id")
    @TableField(exist = false)
    private Integer craftProcedureId;

    /**
     * 成品率
     */
    @ApiModelProperty("成品率")
    @TableField(exist = false)
    private Double yield;

    /**
     * 是否已打印
     */
    @ApiModelProperty("是否已打印")
    @TableField(value = "is_print")
    private Boolean isPrint;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    @TableField(exist = false)
    private List<AppendixEntity> appendixEntities;

    /**
     * 类型名称
     */
    @ApiModelProperty("类型名称")
    @TableField(exist = false)
    private String typeName;

    @ApiModelProperty("项目定义")
    @TableField(exist = false)
    private String projectDefineId;

    @ApiModelProperty("项目名称")
    @TableField(exist = false)
    private String projectDefineName;

    @ApiModelProperty("合同定义")
    @TableField(exist = false)
    private String contractId;

    @ApiModelProperty("合同名称")
    @TableField(exist = false)
    private String contractName;

    /**
     * 存在项目合同 0 不存在 1 存在
     */
    @ApiModelProperty("存在项目合同")
    @TableField(value = "project_contract")
    private Boolean projectContract;


    /**
     * 生产订单拓展字段1
     */
    @ApiModelProperty(value = "生产订单拓展字段1")
    @TableField(value = "product_order_extend_field_one")
    private String productOrderExtendFieldOne;
    /**
     * 生产订单拓展字段2
     */
    @ApiModelProperty(value = "生产订单拓展字段2")
    @TableField(value = "product_order_extend_field_two")
    private String productOrderExtendFieldTwo;
    /**
     * 生产订单拓展字段3
     */
    @ApiModelProperty(value = "生产订单拓展字段3")
    @TableField(value = "product_order_extend_field_three")
    private String productOrderExtendFieldThree;
    /**
     * 生产订单拓展字段4
     */
    @ApiModelProperty(value = "生产订单拓展字段4")
    @TableField(value = "product_order_extend_field_four")
    private String productOrderExtendFieldFour;
    /**
     * 生产订单拓展字段5
     */
    @ApiModelProperty(value = "生产订单拓展字段5")
    @TableField(value = "product_order_extend_field_five")
    private String productOrderExtendFieldFive;

    /**
     * 生产订单拓展字段6
     */
    @ApiModelProperty(value = "生产订单拓展字段6")
    @TableField(value = "product_order_extend_field_six")
    private String productOrderExtendFieldSix;
    /**
     * 生产订单拓展字段7
     */
    @ApiModelProperty(value = "生产订单拓展字段7")
    @TableField(value = "product_order_extend_field_seven")
    private String productOrderExtendFieldSeven;
    /**
     * 生产订单拓展字段8
     */
    @ApiModelProperty(value = "生产订单拓展字段8")
    @TableField(value = "product_order_extend_field_eight")
    private String productOrderExtendFieldEight;
    /**
     * 生产订单拓展字段9
     */
    @ApiModelProperty(value = "生产订单拓展字段9")
    @TableField(value = "product_order_extend_field_nine")
    private String productOrderExtendFieldNine;
    /**
     * 生产订单拓展字段10
     */
    @ApiModelProperty(value = "生产订单拓展字段10")
    @TableField(value = "product_order_extend_field_ten")
    private String productOrderExtendFieldTen;

    /**
     * 使用到的编码规则id
     *
     * @param
     * @return
     */
    @TableField(exist = false)
    private Integer numberRuleId;

    @TableField(exist = false)
    private String productOrderExtendFieldOneName;
    @TableField(exist = false)
    private String productOrderExtendFieldTwoName;
    @TableField(exist = false)
    private String productOrderExtendFieldThreeName;
    @TableField(exist = false)
    private String productOrderExtendFieldFourName;
    @TableField(exist = false)
    private String productOrderExtendFieldFiveName;
    @TableField(exist = false)
    private String productOrderExtendFieldSixName;
    @TableField(exist = false)
    private String productOrderExtendFieldSevenName;
    @TableField(exist = false)
    private String productOrderExtendFieldEightName;
    @TableField(exist = false)
    private String productOrderExtendFieldNineName;
    @TableField(exist = false)
    private String productOrderExtendFieldTenName;

    /**
     * 是否是委外订单
     */
    @TableField(exist = false)
    private Boolean isSubcontractOrder = false;

    @Override
    public Serializable pkVal() {
        return this.productOrderId;
    }

    public interface Insert {
    }

    public interface Update {
    }

}
