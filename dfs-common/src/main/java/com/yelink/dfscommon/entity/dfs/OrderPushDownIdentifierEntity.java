package com.yelink.dfscommon.entity.dfs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 单据下推标识表
 *
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("dfs_order_push_down_identifier")
public class OrderPushDownIdentifierEntity extends Model<OrderPushDownIdentifierEntity> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 源单据类型
     */
    @ApiModelProperty("源单据类型")
    @TableField(value = "order_type")
    private String orderType;

    /**
     * 单据物料行id
     */
    @ApiModelProperty("单据物料行id")
    @TableField(value = "order_material_id")
    private String orderMaterialId;

    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @TableField(value = "batch_number")
    private String batchNumber;

    /**
     * 下推标识状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)
     */
    @ApiModelProperty("下推标识状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)")
    @TableField(value = "state")
    private String state;

    /**
     * 目标单据类型
     */
    @ApiModelProperty("目标单据类型")
    @TableField(value = "target_order_type")
    private String targetOrderType;

}
