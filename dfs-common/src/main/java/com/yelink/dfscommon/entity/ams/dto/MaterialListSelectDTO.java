package com.yelink.dfscommon.entity.ams.dto;

import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.dfs.order.OrderStateEnum;
import com.yelink.dfscommon.dto.CommSelectPageDTO;
import com.yelink.dfscommon.dto.MaterialEntitySelectDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuSelectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 生产订单用料清单查询条件
 *
 * <AUTHOR>
 * @Date 2022/8/24 15:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialListSelectDTO extends CommSelectPageDTO {

    /**
     * 展示类型: 单据、物料
     * ShowTypeEnum
     */
    @ApiModelProperty("展示类型: 单据、物料")
    private String showType;

    /**
     * 生产工单单用料清单编号
     */
    @ApiModelProperty("生产工单单用料清单编号")
    private String materialListCode;

    /**
     * 关联单据编号
     */
    @ApiModelProperty("关联单据编号")
    private String relateNumber;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createName;

    /**
     * 状态
     * {@link OrderStateEnum}
     */
    @ApiModelProperty("状态")
    private String states;

    /**
     * 审批状态
     * {@link ApprovalStatusEnum}
     */
    @ApiModelProperty("审批状态")
    private String approvalStatus;

    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型")
    private String orderType;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 物料字段
     */
    private MaterialEntitySelectDTO materialFields;
    /**
     * 物料特征参数
     */
    @ApiModelProperty("物料特征参数")
    private List<MaterialSkuSelectDTO> materialSkus;

    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    private String businessUnitName;

    /**
     * 业务主体编码:
     */
    @ApiModelProperty("业务主体编码")
    private List<String> businessUnitCodeList;


    /**
     * 生产工单用料清单扩展字段1
     */
    @ApiModelProperty("生产工单用料清单扩展字段1编码")
    private String materialListExtendFieldOne;
    /**
     * 生产工单用料清单扩展字段2
     */
    @ApiModelProperty("生产工单用料清单扩展字段2编码")
    private String materialListExtendFieldTwo;
    /**
     * 生产工单用料清单扩展字段3
     */
    @ApiModelProperty("生产工单用料清单扩展字段3编码")
    private String materialListExtendFieldThree;
    /**
     * 生产工单用料清单扩展字段4
     */
    @ApiModelProperty("生产工单用料清单扩展字段4编码")
    private String materialListExtendFieldFour;
    /**
     * 生产工单用料清单扩展字段5
     */
    @ApiModelProperty("生产工单用料清单扩展字段5编码")
    private String materialListExtendFieldFive;

    /**
     * 生产工单用料清单扩展字段6
     */
    @ApiModelProperty("生产工单用料清单扩展字段6编码")
    private String materialListExtendFieldSix;
    /**
     * 生产工单用料清单扩展字段7
     */
    @ApiModelProperty("生产工单用料清单扩展字段7编码")
    private String materialListExtendFieldSeven;
    /**
     * 生产工单用料清单扩展字段8
     */
    @ApiModelProperty("生产工单用料清单扩展字段8编码")
    private String materialListExtendFieldEight;
    /**
     * 生产工单用料清单扩展字段9
     */
    @ApiModelProperty("生产工单用料清单扩展字段9编码")
    private String materialListExtendFieldNine;
    /**
     * 生产工单用料清单扩展字段10
     */
    @ApiModelProperty("生产工单用料清单扩展字段10编码")
    private String materialListExtendFieldTen;


    /**
     * 生产工单用料清单物料扩展字段1
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段1")
    private String materialListMaterialExtendFieldOne;
    /**
     * 生产工单用料清单物料扩展字段2
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段2")
    private String materialListMaterialExtendFieldTwo;
    /**
     * 生产工单用料清单物料扩展字段3
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段3")
    private String materialListMaterialExtendFieldThree;
    /**
     * 生产工单用料清单物料扩展字段4
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段4")
    private String materialListMaterialExtendFieldFour;
    /**
     * 生产工单用料清单物料扩展字段5
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段5")
    private String materialListMaterialExtendFieldFive;

    /**
     * 生产工单用料清单物料扩展字段6
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段6")
    private String materialListMaterialExtendFieldSix;
    /**
     * 生产工单用料清单物料扩展字段7
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段7")
    private String materialListMaterialExtendFieldSeven;
    /**
     * 生产工单用料清单物料扩展字段8
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段8")
    private String materialListMaterialExtendFieldEight;
    /**
     * 生产工单用料清单物料扩展字段9
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段9")
    private String materialListMaterialExtendFieldNine;
    /**
     * 生产工单用料清单物料扩展字段10
     */
    @ApiModelProperty("生产工单用料清单物料扩展字段10")
    private String materialListMaterialExtendFieldTen;

    /**
     * 单据id
     */
    @ApiModelProperty(hidden = true)
    private List<Integer> materialListId;
    /**
     * 物料行id
     */
    @ApiModelProperty(hidden = true)
    private List<Integer> relatedMaterialIds;

    /**
     * 导出模板的id
     */
    @ApiModelProperty(hidden = true)
    private Integer templateId;

    /**
     * 下推状态
     */
    @ApiModelProperty("下推状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)")
    private String pushDownState;

    /**
     * 目标单据类型
     */
    @ApiModelProperty("目标单据类型，配合下推状态使用")
    private String targetOrderType;

}
