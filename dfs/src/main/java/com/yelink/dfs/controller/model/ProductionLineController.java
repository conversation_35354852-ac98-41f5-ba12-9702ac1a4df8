package com.yelink.dfs.controller.model;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.ReportType;
import com.yelink.dfs.constant.reportline.OrderReportTypeEnum;
import com.yelink.dfs.constant.reportline.ReportLineTypeEnum;
import com.yelink.dfs.constant.reportline.ReportOptionalFieldsEnum;
import com.yelink.dfs.constant.reportline.ReportOrderFieldsEnum;
import com.yelink.dfs.constant.reportline.ReportWorkOrderFieldsEnum;
import com.yelink.dfs.constant.target.MethodConst;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.common.CommonType;
import com.yelink.dfs.entity.common.config.dto.ReportHideZeroDTO;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.ExtendFieldDTO;
import com.yelink.dfs.entity.target.record.ReportDayCountEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.target.record.ReportLineProcessAssemblyEntity;
import com.yelink.dfs.entity.target.record.dto.WorkOrderReportDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.open.v1.model.dto.ProductionLinePageDTO;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.target.UnqualifiedImgService;
import com.yelink.dfs.service.target.record.ReportCountService;
import com.yelink.dfs.service.target.record.ReportDayCountService;
import com.yelink.dfs.service.target.record.ReportLineProcessAssemblyService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.unit.config.UnitFormatMethod;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.ReportDTO;
import com.yelink.dfscommon.entity.dfs.CheckReportDTO;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.WrapperUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description: 生产线接口
 * @Author: zhuangweiqin
 * @Date: 2020/12/3
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/lines")
public class ProductionLineController extends BaseController {

    private ProductionLineService productionLineService;
    private ReportCountService reportCountService;
    private ReportLineService reportLineService;
    private ReportDayCountService reportDayCountService;
    private BarCodeService barCodeService;
    private DictService dictService;
    private UnqualifiedImgService unqualifiedImgService;
    private RedisTemplate redisTemplate;
    private GridService gridService;
    private ImportDataRecordService importDataRecordService;
    private ReportLineProcessAssemblyService reportLineProcessAssemblyService;
    private BusinessConfigService businessConfigService;
    private SkuService skuService;

    /**
     * 添加产线,并添加产线分支
     *
     * @param entity 产线实体
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "工厂配置", type = OperationType.ADD, desc = "新增了产线编码为#{productionLineCode}的产线")
    public ResponseData add(@RequestBody @Valid ProductionLineEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateDate(new Date());
        entity.setCreateBy(username);
        productionLineService.addEntity(entity, username);
        return success(entity);
    }

    /**
     * 更新产线，并修改产线分支
     *
     * @param entity 产线实体
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "工厂配置", type = OperationType.UPDATE, desc = "修改了产线编码为#{productionLineCode}的产线")
    public ResponseData update(@RequestBody ProductionLineEntity entity) {
        entity.setUpdateBy(getUsername());
        entity.setUpdateDate(new Date());
        productionLineService.updateEntity(entity);
        return success(entity);
    }

    /**
     * 删除产线
     *
     * @param id 产线id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "工厂配置", type = OperationType.DELETE, desc = "删除了产线编码为#{productionLineCode},产线名称为#{name}的产线")
    public ResponseData delete(@PathVariable Integer id) {
        ProductionLineEntity lineEntity = productionLineService.deleteLine(id);
        return success(lineEntity);
    }

    /**
     * 制造单元导入:下载默认导入模板
     */
    @GetMapping("/down/default/template")
    public void downDefaultTemplate(HttpServletResponse response) throws Exception {
        productionLineService.downloadDefaultTemplate("classpath:template/productionLineImportTemplate.xlsx", response, "制造单元导入模板" + Constant.XLSX);
    }

    /**
     * 制造单元导入
     */
    @PostMapping("/import")
    public ResponseData importData(MultipartFile file) throws Exception {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        //异步数据导入
        String importProgressKey = RedisKeyPrefix.PRODUCTION_LINE_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        productionLineService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return ResponseData.success();
    }

    /**
     * 制造单元导入:查询导入进度
     */
    @GetMapping("/import/progress")
    public ResponseData getImportProgress() {
        return success(productionLineService.importProgress());
    }

    /**
     * 制造单元导入-查询导入记录列表
     */
    @RequestMapping("/import/record")
    public ResponseData recordList(@RequestParam(required = false) String fileName,
                                   @RequestParam(required = false) String startTime,
                                   @RequestParam(required = false) String endTime,
                                   @RequestParam(required = false, defaultValue = "1") Integer current,
                                   @RequestParam(required = false, defaultValue = "10") Integer size) {
        return success(importDataRecordService.getList(ImportTypeEnum.PRODUCTION_LINE_IMPORT.getType(), fileName, startTime, endTime, current, size));
    }

    /**
     * 查询产线列表(APP端接口)** 接口放开 未做数据隔离
     *
     * @param name        产线名称
     * @param code        产线编号
     * @param currentPage 当前页
     * @param size        当前页条数
     * @return
     */
    @GetMapping("/list/app")
    public ResponseData listToApp(@RequestParam(value = "name", required = false) String name,
                                  @RequestParam(value = "code", required = false) String code,
                                  @RequestParam(value = "current", required = false) Integer currentPage,
                                  @RequestParam(value = "size", required = false) Integer size) {
        ProductionLinePageDTO build = ProductionLinePageDTO.builder()
                .name(name)
                .code(code)
                .current(currentPage)
                .size(size)
                .build();
        return success(productionLineService.list(build));
    }

    /**
     * 查询未做数据隔离的产线列表（用于权限配置）
     *
     * @param
     * @return
     */
    @GetMapping("/list/all")
    public ResponseData listToWebAll(@RequestParam(value = "name", required = false) String name,
                                     @RequestParam(value = "code", required = false) String code,
                                     @RequestParam(value = "current", required = false) Integer currentPage,
                                     @RequestParam(value = "size", required = false) Integer size) {
        ProductionLinePageDTO build = ProductionLinePageDTO.builder()
                .name(name)
                .code(code)
                .current(currentPage)
                .size(size)
                .build();
        return success(productionLineService.list(build));
    }

    /**
     * 查询产线列表(Web端接口)  数据隔离
     *
     * @param name        产线名称
     * @param code        产线编号
     * @param modelName   产线模型名称
     * @param currentPage 当前页
     * @param size        当前页条数
     * @return
     */
    @GetMapping("/list")
    public ResponseData listToWeb(@RequestParam(value = "name", required = false) String name,
                                  @RequestParam(value = "code", required = false) String code,
                                  @RequestParam(value = "fullCode", required = false) String fullCode,
                                  @RequestParam(value = "modelName", required = false) String modelName,
                                  @RequestParam(value = "workCenterId", required = false) Integer workCenterId,
                                  @RequestParam(value = "workCenterName", required = false) String workCenterName,
                                  @RequestParam(value = "current", required = false) Integer currentPage,
                                  @RequestParam(value = "size", required = false) Integer size) {
        ProductionLinePageDTO build = ProductionLinePageDTO.builder()
                .name(name)
                .code(code)
                .fullCode(fullCode)
                .modelName(modelName)
                .workCenterId(workCenterId)
                .workCenterName(workCenterName)
                .current(currentPage)
                .size(size)
                .build();
        return success(productionLineService.list(build));
    }

    /**
     * 产线报工--获取产线列表（工单模式--1.18.1保留）
     *
     * @param name
     * @param code
     * @param modelName
     * @param currentPage
     * @param size
     * @return
     */
    @GetMapping("/list/for/lineReport")
    public ResponseData listForLineReport(@RequestParam(value = "name", required = false) String name,
                                          @RequestParam(value = "code", required = false) String code,
                                          @RequestParam(value = "modelName", required = false) String modelName,
                                          @RequestParam(value = "workCenterId", required = false) Integer workCenterId,
                                          @RequestParam(value = "isOperation", required = false) Boolean isOperation,
                                          @RequestParam(value = "workCenterType", required = false) String workCenterType,
                                          @RequestParam(value = "current", required = false) Integer currentPage,
                                          @RequestParam(value = "size", required = false) Integer size) {
        return success(productionLineService.listForLineReport(workCenterId, name, code, modelName, isOperation, workCenterType, currentPage, size, getUsername()));
    }

    /**
     * 查询车间下产线列表——（pad端+web端）
     *
     * @param gid     车间id
     * @param current 当前页
     * @param size    当前页条数
     * @return
     */
    @GetMapping("/list/grid")
    public ResponseData lineByGid(@RequestParam(value = "gid") Integer gid,
                                  @RequestParam(value = "current", defaultValue = "1") Integer current,
                                  @RequestParam(value = "size", defaultValue = "10") Integer size) {
        Page<ProductionLineEntity> list = productionLineService.lineByGid(gid, current, size);
        return success(list);
    }

    /**
     * 查询车间下产线列表
     *
     * @param gids
     * @return
     */
    @GetMapping("/list/by/grids")
    public ResponseData getListByGids(@RequestParam(value = "gids", required = false) String gids) {
        List<String> gidList = StringUtils.isEmpty(gids) ? null : Arrays.asList(gids.split(Constant.SEP));
        List<ProductionLineEntity> list = productionLineService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(gidList), ProductionLineEntity::getGid, gidList)
                .list();
        return success(list);
    }

    /**
     * 查询产线详情
     *
     * @param id 产线id
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData detail(@PathVariable("id") Integer id) {
        ProductionLineEntity entity = productionLineService.detail(id);
        return success(entity);
    }

    /**
     * 查询某条产线下的所有员工
     *
     * @param productLineCode 产线编码
     * @return
     */
    @GetMapping("/employee")
    public ResponseData getLineEmployees(@RequestParam(value = "productLineCode") String productLineCode) {
        List<SysUserEntity> employees = productionLineService.getLineEmployees(productLineCode);
        return success(employees);
    }


    /**
     * 产线报工--输入工单产量前显示采集器数量
     *
     * @param workOrderNumber
     * @return
     */
    @GetMapping("/input/auto/count")
    public ResponseData getAutoCount(@RequestParam(value = "workOrderNumber") String workOrderNumber,
                                     @RequestParam(value = "reportLineId", required = false) Integer reportLineId,
                                     @RequestParam(value = "lineId", required = false) Integer lineId,
                                     @RequestParam(value = "deviceId", required = false) Integer deviceId,
                                     @RequestParam(value = "teamId", required = false) Integer teamId) {
        return success(reportCountService.getAutoCountAndDate(workOrderNumber, reportLineId, lineId, deviceId, teamId));
    }


    /**
     * 产线报工--输入工单产量
     *
     * @param entity 工单产量
     * @return
     */
    @PostMapping("/input/count")
    @OperLog(module = "产线报工", type = OperationType.ADD, desc = "新增了单号为#{workOrder}的报工记录,完成数量:#{finishCount}，不良数量:#{unqualified}")
    public ResponseData countOutput(@RequestBody ReportLineEntity entity) {
        // 加锁，防止多次点击
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(MethodConst.PRODUCTION_LINE_REPORT + Constant.UNDERLINE + entity.getWorkOrder(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return success();
        }
        try {
            String username = getUsername();
            if (StringUtils.isNotBlank(username)) {
                entity.setUserName(username);
            }
            reportCountService.countOutput(entity);
        } catch (Exception e) {
            log.error("报工失败", e);
        } finally {
            redisTemplate.delete(MethodConst.PRODUCTION_LINE_REPORT + Constant.UNDERLINE + entity.getWorkOrder());
        }
        return success(entity);
    }

    /**
     * 产线报工--修改报工数量
     *
     * @param entity
     * @return
     */
    @PutMapping("/input/update/count")
    @OperLog(module = "产线报工", type = OperationType.UPDATE, desc = "更新了单号为#{workOrder}为的报工记录")
    public ResponseData updateCount(@RequestBody ReportLineEntity entity) {
        // 加锁，防止多次点击
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(MethodConst.PRODUCTION_LINE_REPORT_UPDATE + Constant.UNDERLINE + entity.getWorkOrder(), new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            return success();
        }
        try {
            reportCountService.updateCount(entity);
        } catch (Exception e) {
            log.error("报工失败", e);
        } finally {
            redisTemplate.delete(MethodConst.PRODUCTION_LINE_REPORT_UPDATE + Constant.UNDERLINE + entity.getWorkOrder());
        }
        return success(entity);
    }

    /**
     * 报工非必填字段枚举列表
     */
    @GetMapping("/optional/fields")
    public ResponseData getOptionalFieldList() {
        List<CommonType> list = Arrays.stream(ReportOptionalFieldsEnum.values())
                .map(fieldsEnum -> CommonType.builder()
                        .code(fieldsEnum.getType())
                        .name(fieldsEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }


    /**
     * 报工工单可配置字段枚举列表
     */
    @GetMapping("/work/order/fields")
    public ResponseData getWorkOrderFieldList() {
        List<CommonType> list = Arrays.stream(ReportWorkOrderFieldsEnum.values())
                .map(fieldsEnum -> CommonType.builder()
                        .code(fieldsEnum.getType())
                        .name(fieldsEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }

    /**
     * 报工订单可配置字段枚举列表
     */
    @GetMapping("/order/fields")
    public ResponseData getOrderFieldList() {
        List<CommonType> list = Arrays.stream(ReportOrderFieldsEnum.values())
                .map(fieldsEnum -> CommonType.builder()
                        .code(fieldsEnum.getType())
                        .name(fieldsEnum.getName())
                        .build()).collect(Collectors.toList());
        return success(list);
    }


    /**
     * 产线报工--查询输入工单产量历史记录
     *
     * @return
     */
    @GetMapping("/history/count")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getInputHistory(@RequestParam(value = "workOrder") String workOrder,
                                        @RequestParam(value = "batch", required = false) String batch,
                                        @RequestParam(value = "shiftId", required = false) Integer shiftId,
                                        @RequestParam(value = "startDate", required = false) Long startDate,
                                        @RequestParam(value = "endDate", required = false) Long endDate,
                                        @RequestParam(value = "current", required = false) Integer current,
                                        @RequestParam(value = "size", required = false) Integer size) {
        LambdaQueryWrapper<ReportLineEntity> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(ReportLineEntity::getWorkOrder, workOrder)
                //.eq(ReportLineEntity::getType, ReportType.REPORT)
                .orderByDesc(ReportLineEntity::getCreateTime)
                .orderByDesc(ReportLineEntity::getId);

        WrapperUtil.eq(countWrapper, ReportLineEntity::getShiftId, shiftId);
        WrapperUtil.like(countWrapper, ReportLineEntity::getBatch, batch);
        if (startDate != null && endDate != null) {
            countWrapper.between(ReportLineEntity::getReportDate, new Date(startDate - 28800000), new Date(endDate - 28800000));
        }
        // 是否隐藏数量为0的记录
        ReportHideZeroDTO configDTO = businessConfigService.getValueDto(
                FullPathCodeDTO.builder().fullPathCode(ConfigConstant.WORK_ORDER_REPORT_HIDE_AUTO_ZERO_CONFIG).build(),
                ReportHideZeroDTO.class
        );
        if (Boolean.TRUE.equals(configDTO.getEnable())) {
            List<Integer> filterIds = reportLineService.lambdaQuery().select(ReportLineEntity::getId).eq(ReportLineEntity::getType, ReportType.AUTO.getType()).eq(ReportLineEntity::getFinishCount, 0)
                    .list().stream().map(ReportLineEntity::getId).collect(Collectors.toList());
            countWrapper.notIn(ReportLineEntity::getId, filterIds);
        }

        Page<ReportLineEntity> page;
        if (current == null || size == null) {
            //pad查询最近一个月记录
            //countWrapper.ge(ReportLineEntity::getCreateTime, DateUtils.addMonths(new Date(), -1));
            List<ReportLineEntity> list = reportLineService.list(countWrapper);
            for (ReportLineEntity reportLineEntity : list) {
                String[] urls = unqualifiedImgService.getImgByReportLineId(reportLineEntity.getId());
                if (urls != null) {
                    reportLineEntity.setPicUrls(urls);
                }
                if (ReportLineTypeEnum.AUTO.getType().equals(reportLineEntity.getType())) {
                    reportLineEntity.setUserNickname(ReportLineTypeEnum.AUTO.getName());
                }
            }
            // 报工记录按天小计
            Map<Date, List<ReportLineEntity>> map = list.stream().collect(Collectors.groupingBy(ReportLineEntity::getReportDate));
            for (Map.Entry<Date, List<ReportLineEntity>> entry : map.entrySet()) {
                List<ReportLineEntity> reportCountEntities = entry.getValue();
                double finishCountSum = reportCountEntities.stream().filter(entity -> entity.getFinishCount() != null)
                        .mapToDouble(ReportLineEntity::getFinishCount).sum();
//                double effectiveHoursSum = reportCountEntities.stream().filter(entity -> entity.getEffectiveHours() != null)
//                        .mapToDouble(ReportLineEntity::getEffectiveHours).sum();
                BigDecimal effectiveHoursSum = reportCountEntities.stream().filter(entity -> entity.getEffectiveHours() != null)
                        .map(ReportLineEntity::getEffectiveHours).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add);
                double unqualifiedSum = reportCountEntities.stream().filter(entity -> entity.getUnqualified() != null)
                        .mapToDouble(ReportLineEntity::getUnqualified).sum();
                ReportLineEntity build = ReportLineEntity.builder()
                        .finishCount(finishCountSum)
                        .unqualified(unqualifiedSum)
                        .effectiveHours(effectiveHoursSum.doubleValue())
                        .countType(1)
                        .reportDate(entry.getKey())
                        .build();
                list.add(build);
            }
            List<ReportLineEntity> collect = list.stream().sorted(
                    Comparator.comparing(ReportLineEntity::getReportDate, Comparator.reverseOrder())
                            .thenComparing(ReportLineEntity::getType, Comparator.nullsLast(Comparator.naturalOrder()))
            ).collect(Collectors.toList());
            // 报工记录汇总
            double allFinishCountSum = list.stream().filter(entity -> entity.getFinishCount() != null).filter(entity -> entity.getCountType() != 0)
                    .mapToDouble(ReportLineEntity::getFinishCount).sum();
            double allUnqualifiedSum = list.stream().filter(entity -> entity.getUnqualified() != null).filter(entity -> entity.getCountType() != 0)
                    .mapToDouble(ReportLineEntity::getUnqualified).sum();
            double allEffectiveHoursSum = list.stream().filter(entity -> entity.getEffectiveHours() != null).filter(entity -> entity.getCountType() != 0)
                    .mapToDouble(ReportLineEntity::getEffectiveHours).sum();
            ReportLineEntity build = ReportLineEntity.builder()
                    .finishCount(allFinishCountSum)
                    .unqualified(allUnqualifiedSum)
                    .effectiveHours(allEffectiveHoursSum)
                    .countType(2)
                    .build();
            collect.add(build);

            page = new Page<>(1, list.size(), list.size());
            page.setRecords(collect);
        } else {
            page = reportLineService.page(new Page<>(current, size), countWrapper);
        }
        List<ReportLineEntity> records = page.getRecords();
        List<Integer> skuIds = records.stream().map(ReportLineEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toList());
        List<String> auditors = records.stream().map(ReportLineEntity::getAuditor).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, String> auditorNickMap = sysUserService.getUserNameNickMap2(auditors);
        Map<Integer, SkuEntity> skuIdMap = skuService.getIdMap(skuIds);
        for (ReportLineEntity reportLineEntity : records) {
            String[] urls = unqualifiedImgService.getImgByReportLineId(reportLineEntity.getId());
            if (urls != null) {
                reportLineEntity.setPicUrls(urls);
            }
            //工装
            List<ReportLineProcessAssemblyEntity> reportLineProcessAssemblyEntities = reportLineProcessAssemblyService.lambdaQuery()
                    .eq(ReportLineProcessAssemblyEntity::getReportLineId, reportLineEntity.getId())
                    .list();
            reportLineEntity.setProcessAssemblyList(reportLineProcessAssemblyEntities.stream().map(ReportLineProcessAssemblyEntity::getCode).collect(Collectors.toList()));
            reportLineEntity.setOrderReportName(OrderReportTypeEnum.getNameByCode(reportLineEntity.getOrderReportType()));
            reportLineEntity.setSkuEntity(skuIdMap.get(reportLineEntity.getSkuId()));
            reportLineEntity.setAuditorName(auditorNickMap.get(reportLineEntity.getAuditor()));
        }
        return success(page);

    }


    /**
     * 工单报工--获取报工记录列表
     * 去掉按天小计 报工记录汇总
     *
     * @return
     */
    @GetMapping("/report/history/count")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getReportLineHistory(@RequestParam(value = "workOrder") String workOrder,
                                             @RequestParam(value = "batch", required = false) String batch,
                                             @RequestParam(value = "shiftId", required = false) Integer shiftId,
                                             @RequestParam(value = "startDate", required = false) Long startDate,
                                             @RequestParam(value = "endDate", required = false) Long endDate,
                                             @RequestParam(value = "current", required = false) Integer current,
                                             @RequestParam(value = "size", required = false) Integer size) {
        LambdaQueryWrapper<ReportLineEntity> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(ReportLineEntity::getWorkOrder, workOrder)
//                .eq(ReportLineEntity::getType, ReportType.REPORT.getType())
                .orderByDesc(ReportLineEntity::getCreateTime)
                .orderByDesc(ReportLineEntity::getId);
        WrapperUtil.eq(countWrapper, ReportLineEntity::getShiftId, shiftId);
        WrapperUtil.like(countWrapper, ReportLineEntity::getBatch, batch);
        if (startDate != null && endDate != null) {
            countWrapper.between(ReportLineEntity::getReportDate, new Date(startDate), new Date(endDate));
        }
        Page<ReportLineEntity> page;
        if (current == null || size == null) {
            List<ReportLineEntity> list = reportLineService.list(countWrapper);
            page = new Page<>(1, list.size(), list.size());
            page.setRecords(list);
        } else {
            page = reportLineService.page(new Page<>(current, size), countWrapper);
        }
        List<ReportLineEntity> records = page.getRecords();
        for (ReportLineEntity record : records) {
            //拿到批次信息
            if (StringUtils.isNotBlank(record.getBatch())) {
                record.setBarCodeEntity(barCodeService.getBarCodeByCode(record.getBatch()));
            }
        }
        return success(page);
    }


    /**
     * 获取工单报工记录详情
     *
     * @param id 报工记录id
     * @return
     */
    @GetMapping("/report/line/detail")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getReportLineDetail(@RequestParam(value = "id") String id) {
        return success(reportLineService.getReportLineDetail(id));
    }

    /**
     * 投产前确认接口（判断制造单元是否占用）
     *
     * @param lineId 产线id
     * @return
     */
    @GetMapping("/confirm/input/report")
    public ResponseData confirmReportAction(@RequestParam(value = "lineId") Integer lineId) {
        return success(reportCountService.confirmReportAction(lineId));
    }

    /**
     * 产线报工--选择工单开始停止
     * <p>
     * workOrder
     * action    0-投产  1-完成 2-挂起
     *
     * @return
     */
    @PostMapping("/input/report")
    @OperLog(module = "工单状态变更", type = OperationType.UPDATE, desc = "更新了单号为#{workOrderNumber}的状态")
    public ResponseData reportAction(ReportDTO dto) {
        dto.setUsername(StringUtils.isNotBlank(dto.getUsername()) ? dto.getUsername() : getUsername());
        return success(productionLineService.reportAction(dto));
    }

    /**
     * 批量更改工单状态
     *
     * @param workOrderReportDTO
     * @return
     */
    @PostMapping("/batch/update/state")
    public ResponseData hangOccupiedWorkOrder(@RequestBody WorkOrderReportDTO workOrderReportDTO) {
        productionLineService.hangOccupiedWorkOrder(workOrderReportDTO, getUsername());
        return success();
    }

    /**
     * 产线报工--选择工单开始停止
     * <p>
     * workOrder 工单号
     * action    0-投产  1-完成 2-挂起
     * lineId    产线id
     * time      投产时间
     *
     * @return
     */
    @PostMapping("/input/report/line/time")
    public ResponseData reportActionWithLineAndTime(@RequestBody ReportDTO reportDTO) {
        return success(productionLineService.reportAction(reportDTO));
    }

    /**
     * 点击工单完成时选择新建工单
     *
     * @param workOrderEntity
     * @return
     */
    @PostMapping("/work/order/new")
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "新增了工单号为#{workOrderNumber}的工单")
    public ResponseData addOrderNew(@RequestBody @Validated({WorkOrderEntity.Insert.class}) WorkOrderEntity workOrderEntity, BindingResult bindingResult) throws Exception {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        workOrderEntity.setCreateBy(getUsername());
        workOrderEntity.setCreateDate(new Date());
        workOrderEntity.setUpdateBy(getUsername());
        workOrderEntity.setUpdateDate(new Date());
        productionLineService.addOrderNew(workOrderEntity);
        return success();
    }


    /**
     * 查询厂区下产线列表——（pad端）
     *
     * @return
     */
    @GetMapping("/all/list")
    public ResponseData lines(@RequestParam(required = false) Integer gid,
                              @RequestParam(required = false) Integer workCenterId) {
        List<ProductionLineEntity> lines = productionLineService.lambdaQuery()
                .eq(Objects.nonNull(gid), ProductionLineEntity::getGid, gid)
                .eq(Objects.nonNull(workCenterId), ProductionLineEntity::getWorkCenterId, workCenterId)
                .list();
        Map<Integer, String> gidNameMap = gridService.list().stream().collect(Collectors.toMap(GridEntity::getGid, GridEntity::getGname));
        lines.forEach(res -> res.setGname(gidNameMap.get(res.getGid())));
        return success(lines);
    }


    /**
     * app生产日报-产量数据汇总
     *
     * @param
     * @return
     */
    @GetMapping("/product/count/sum")
    public ResponseData getCountSum() {
        return success(reportCountService.getCountSum());
    }

    /**
     * app生产日报-车间产量数据汇总 近7天
     *
     * @param gid
     * @return
     */
    @GetMapping("/grid/count/sum/{gid}")
    public ResponseData getGridCountSum(@PathVariable Integer gid) {
        return success(reportCountService.getGridCountSum(gid));
    }

    /**
     * app生产日报-产线产量数据汇总 近30天
     *
     * @param gid
     * @return
     */
    @GetMapping("/line/count/sum/{gid}")
    public ResponseData getLineCountSum(@PathVariable Integer gid) {
        return success(reportCountService.getLineCountSum(gid));
    }

    /**
     * app生产日报-获取车间列表
     *
     * @return
     */
    @GetMapping("/grid/list")
    public ResponseData getGridList() {
        return success(reportCountService.getGridList());
    }


    /**
     * 产线报工--查询入库历史记录
     *
     * @return
     */
    @GetMapping("/input/store/history")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getInputStoreHistory(@RequestParam(value = "workOrder") String workOrder,
                                             @RequestParam(value = "batch", required = false) String batch,
                                             @RequestParam(value = "shiftId", required = false) Integer shiftId,
                                             @RequestParam(value = "startDate", required = false) Long startDate,
                                             @RequestParam(value = "endDate", required = false) Long endDate,
                                             @RequestParam(value = "current", required = false) Integer current,
                                             @RequestParam(value = "size", required = false) Integer size) {
        return success(reportCountService.selectStoreHistory(workOrder, batch, shiftId, startDate, endDate, current, size));
    }


    /**
     * 获取工单绑定产线的设备编号列表
     *
     * @param workOrder 工单号
     * @return
     */
    @GetMapping("/workOrder/device/list")
    public ResponseData getWorkOrderDeviceList(@RequestParam(value = "workOrder") String workOrder) {
        List<DeviceEntity> list = productionLineService.getWorkOrderDeviceList(workOrder);
        return success(list);
    }

    /**
     * 产线报工--获取每日报工数
     * 根据总数计算每日报工数
     *
     * @param reportDTO
     * @return
     */
    @PostMapping("/report/cal/day/count")
    public ResponseData calReportDayCount(@RequestBody ReportLineEntity reportDTO) {
        return success(reportDayCountService.calReportDayCount(reportDTO.getReportDayCountList(), reportDTO.getFinishCount()));
    }

    /**
     * 产线报工--更新每日报工数
     * 每日报工列表中点击“提交”时触发
     *
     * @param entities
     * @return
     */
    @PutMapping("/report/cal/day/count")
    public ResponseData updateReportDayCount(@RequestBody List<ReportDayCountEntity> entities) {
        return success(reportCountService.updateReportDayCount(entities));
    }

    /**
     * 拿到工单有报工的时间列表
     *
     * @param workOrderEntity
     * @return
     */
    @PostMapping("/report/date/list")
    public ResponseData getDateListInReport(@RequestBody WorkOrderEntity workOrderEntity) {
        if (StringUtils.isBlank(workOrderEntity.getWorkOrderNumber())) {
            throw new ResponseException("请传入工单号workOrderNumber");
        }
        Date date = dictService.getRecordDate(new Date());
        LambdaQueryWrapper<ReportLineEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ReportLineEntity::getWorkOrder, workOrderEntity.getWorkOrderNumber());
        List<ReportLineEntity> list = reportLineService.list(lambdaQueryWrapper);
        Set<String> set = new HashSet<>();
        set.add(DateUtil.format(date, DateUtil.DATE_FORMAT));
        set.add(DateUtil.format(new Date(), DateUtil.DATE_FORMAT));
        if (CollectionUtils.isEmpty(list)) {
            return success(set);
        }
        Double roughWeightTotal = 0.0, tareWeightTotal = 0.0, netWeightTotal = 0.0;
        Map<String, Object> resultMap = new HashMap<>(4);
        for (ReportLineEntity reportLineEntity : list) {
            netWeightTotal += reportLineEntity.getFinishCount();
            BarCodeEntity barCodeEntity = barCodeService.getBarCodeByCode(reportLineEntity.getBatch());
            if (barCodeEntity != null) {
                List<ExtendFieldDTO> extendFieldDTOS = JSON.parseArray(barCodeEntity.getExtendFields(), ExtendFieldDTO.class);
                if (CollectionUtils.isEmpty(extendFieldDTOS)) {
                    continue;
                }
                Map<String, String> collect = extendFieldDTOS.stream().collect(Collectors.toMap(ExtendFieldDTO::getFieldName, ExtendFieldDTO::getValue));
                roughWeightTotal += collect.get("roughWeight") == null ? 0.0 : Double.parseDouble(collect.get("roughWeight"));
                tareWeightTotal += collect.get("tareWeight") == null ? 0.0 : Double.parseDouble(collect.get("tareWeight"));
            }
            set.add(DateUtil.format(reportLineEntity.getReportDate(), DateUtil.DATE_FORMAT));
        }
        resultMap.put("netWeightTotal", netWeightTotal);
        resultMap.put("roughWeightTotal", roughWeightTotal);
        resultMap.put("tareWeightTotal", tareWeightTotal);
        resultMap.put("timeList", set);
        return success(resultMap);
    }

    /**
     * 根据工作中心获取产线
     *
     * @param workCenterId
     * @return
     */
    @GetMapping("/line/work_center_id")
    public ResponseData getByWorkCenter(@RequestParam(value = "workCenterId") Integer workCenterId) {
        return success(productionLineService.getByWorkCenter(workCenterId));
    }


    /**
     * 检查报工
     *
     * @param checkReportDTO
     * @return
     */
    @PostMapping("/check/report")
    public ResponseData checkReport(@RequestBody CheckReportDTO checkReportDTO) {
        return success(reportCountService.checkReport(checkReportDTO));
    }

    /**
     * 检查完工
     *
     * @param checkReportDTO
     * @return
     */
    @PostMapping("/check/finish")
    public ResponseData checkFinish(@RequestBody CheckReportDTO checkReportDTO) {
        return success(reportCountService.checkFinish(checkReportDTO));
    }

    @GetMapping("/orderReportType/list")
    public ResponseData dealStateList() {
        return success(com.yelink.dfscommon.entity.CommonType.covertToList(OrderReportTypeEnum.class));
    }
}
