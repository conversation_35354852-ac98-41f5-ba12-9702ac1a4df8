package com.yelink.dfs.controller.order;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.device.DevicesStateEnum;
import com.yelink.dfs.constant.model.FacilitiesTypeEnum;
import com.yelink.dfs.constant.order.AssignmentStateEnum;
import com.yelink.dfs.constant.order.CompletionRateEnum;
import com.yelink.dfs.constant.order.DeliveryApplicationStateEnum;
import com.yelink.dfs.constant.order.OrderMergeStateEnum;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.constant.order.TakeOutApplicationStateEnum;
import com.yelink.dfs.constant.target.ManualTargetConst;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitSelectDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitTypeSelectDTO;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.defect.dto.DefectRecordQueryDTO;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.iot.WorkOrderBarcodeEntity;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.maintain.dto.MaintainRecordQueryDTO;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.dto.RelevanceResourceDTO;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.TakeOutApplicationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderFlowEntity;
import com.yelink.dfs.entity.order.WorkOrderInputEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.BatchUpdateWorkOrderDTO;
import com.yelink.dfs.entity.order.dto.DesignInfoDTO;
import com.yelink.dfs.entity.order.dto.FieldInfoDTO;
import com.yelink.dfs.entity.order.dto.ProcedureWorkOrderSelectDTO;
import com.yelink.dfs.entity.order.dto.ProductInfoDTO;
import com.yelink.dfs.entity.order.dto.PurchaseInfoDTO;
import com.yelink.dfs.entity.order.dto.QualityInfoDTO;
import com.yelink.dfs.entity.order.dto.StorageInfoDTO;
import com.yelink.dfs.entity.order.dto.TakeOutApplicationSelectDTO;
import com.yelink.dfs.entity.order.dto.UpdateTipsDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderCreateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderFacDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderInfoDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderInspectionPackageSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderQuantityVerifyDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.entity.order.vo.DeviceVO;
import com.yelink.dfs.entity.order.vo.JudgeOrderVO;
import com.yelink.dfs.entity.order.vo.OrderTraceGroupVO;
import com.yelink.dfs.entity.order.vo.PassRateVO;
import com.yelink.dfs.entity.order.vo.PrintSourceOrderVO;
import com.yelink.dfs.entity.order.vo.WorkCountPerDayVO;
import com.yelink.dfs.entity.order.vo.WorkOrderProcedureInspectResultVO;
import com.yelink.dfs.entity.order.vo.WorkOrderSimpleVO;
import com.yelink.dfs.entity.order.vo.WorkOrderTraceBarcodeVO;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.dto.CraftFileDTO;
import com.yelink.dfs.entity.product.dto.CraftFileNewDTO;
import com.yelink.dfs.entity.screen.ConfigYzjScreenEntity;
import com.yelink.dfs.entity.screen.dto.ScreenDeviceDTO;
import com.yelink.dfs.entity.stock.DeliveryApplicationEntity;
import com.yelink.dfs.entity.target.TargetModelEntity;
import com.yelink.dfs.entity.target.dto.RecordManualCollectionDTO;
import com.yelink.dfs.entity.target.record.RecordDeviceDayRunEntity;
import com.yelink.dfs.entity.target.record.RecordLineBeatEntity;
import com.yelink.dfs.entity.target.record.RecordManualCollectionEntity;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseRequestInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderBasicUnitInvestSelectDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumberDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumbersDTO;
import com.yelink.dfs.open.v2.order.dto.WorkOrderInputUpdateDTO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.code.ProductFlowCodeRecordService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.iot.WorkOrderBarcodeService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.TakeOutApplicationService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderFlowService;
import com.yelink.dfs.service.order.WorkOrderInputService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderProcedureInspectResultService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.screen.YangtzeRiverService;
import com.yelink.dfs.service.stock.DeliveryApplicationService;
import com.yelink.dfs.service.target.TargetModelService;
import com.yelink.dfs.service.target.record.RecordDeviceDayRunService;
import com.yelink.dfs.service.target.record.RecordLineBeatService;
import com.yelink.dfs.service.target.record.RecordManualCollectionService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.api.wms.StockRelateOrderInterface;
import com.yelink.dfscommon.common.unit.config.UnitFormatMethod;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ExcelExportFormEnum;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.PurchaseStateEnum;
import com.yelink.dfscommon.constant.ams.RequestStateEnum;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.wms.StockInputOrOutputTypeEnum;
import com.yelink.dfscommon.constant.wms.StockOrderStateEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.BatchGenerateCodeReqDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.order.ProductOrderParamDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseOpenSelectDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseRequestOpenSelectDTO;
import com.yelink.dfscommon.dto.dfs.OrderMaterialListDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderOperatorDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderOperatorSelectDTO;
import com.yelink.dfscommon.dto.dfs.push.AbstractPushDTO;
import com.yelink.dfscommon.dto.dfs.push.CraftRoutePushWorkOrderVO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.PurchaseEntity;
import com.yelink.dfscommon.entity.ams.PurchaseRequestEntity;
import com.yelink.dfscommon.entity.ams.dto.PurchaseDetailDTO;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.DictEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import com.yelink.dfscommon.entity.wms.StockRelateOrderEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.PageData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 工单
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/workorders")
public class WorkOrderController extends BaseController {
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private MaterialService materialService;
    @Resource
    private CraftService craftService;
    @Resource
    private TakeOutApplicationService takeOutApplicationService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private StockInAndOutInterface stockInAndOutInterface;
    @Resource
    private StockRelateOrderInterface stockRelateOrderInterface;
    @Resource
    private DeliveryApplicationService deliveryApplicationService;
    @Resource
    private RecordWorkOrderUnqualifiedService workOrderUnqualifiedService;
    @Resource
    private MaintainRecordService maintainRecordService;
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    private RecordDeviceDayRunService recordDeviceDayRunService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private TargetModelService targetModelService;
    @Resource
    private DictService dictService;
    @Resource
    private YangtzeRiverService yangtzeRiverService;
    @Resource
    private RecordManualCollectionService recordManualCollectionService;
    @Resource
    private WorkOrderPlanService workOrderPlanService;
    @Resource
    private ProductionLineService lineService;
    @Resource
    private AlarmService alarmService;
    @Resource
    private RecordLineBeatService recordLineBeatService;
    @Resource
    private WorkOrderBarcodeService workOrderBarcodeService;
    @Resource
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    private WorkOrderFlowService workOrderFlowService;
    @Resource
    private WorkOrderInputService workOrderInputService;
    @Resource
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    @Resource
    private final BarCodeService barCodeService;
    @Resource
    private WorkPropertise workPropertise;
    @Resource
    private ProductFlowCodeRecordService productFlowCodeRecordService;
    @Resource
    private ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    private ExtProductOrderInterface extProductOrderInterface;
    @Resource
    private ExtPurchaseInterface extPurchaseInterface;
    @Resource
    private ExtPurchaseRequestInterface extPurchaseRequestInterface;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private FastDfsClientService fastDfsClientService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private WorkOrderExtendService workOrderExtendService;
    @Resource
    private WorkOrderProcedureInspectResultService procedureInspectResultService;
    @Resource
    @Lazy
    private CommonService commonService;
    @Resource
    private OperationLogService operationLogService;

    /**
     * 查询工单列表（可模糊、条件查询）
     *
     * @param workOrderSelectDTO
     * @return
     */

    @GetMapping("/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData orderList(WorkOrderSelectDTO workOrderSelectDTO) {
        String username = getUsername();
        Page<WorkOrderEntity> list = workOrderService.getList(workOrderSelectDTO, username);
        return success(list);
    }

    /**
     * 查询工单列表（可模糊、条件查询）--精简版
     *
     * @param workOrderSelectDTO
     * @return
     */
    @PostMapping("/list/simple")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData orderListSimple(@RequestBody WorkOrderSelectDTO workOrderSelectDTO) {
        String username = getUsername();
        Page<WorkOrderEntity> list = workOrderService.getWorkOrderEntityPage(workOrderSelectDTO, username);
        return success(list);
    }

    @PostMapping("/list/select")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData orderListSelect(@RequestBody WorkOrderSelectDTO workOrderSelectDTO) {
        List<WorkOrderSimpleVO> list = workOrderService.getWorkOrderEntityList(workOrderSelectDTO);
        return success(list);
    }

    /**
     * 通过工单号模糊查询工单号
     *
     * @param workOrderDetailDTO
     * @return
     */
    @PostMapping("/number/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getNumberListByWorkOrderNumber(@RequestBody WorkOrderDetailDTO workOrderDetailDTO) {
        List<PrintSourceOrderVO> list = workOrderService.getNumberListByWorkOrderNumber(workOrderDetailDTO.getWorkOrderNumber());
        return success(list);
    }

    /**
     * 生产工单 批量编辑前校验
     *
     * @param workOrderSelectDTO
     * @return
     */
    @PostMapping("/batch/edit/verify")
    public ResponseData orderStatesVerify(@RequestBody WorkOrderSelectDTO workOrderSelectDTO) {
        return success(workOrderService.batchEditVerify(workOrderSelectDTO));
    }

    /**
     * 通过工位id查询投产状态的工单
     */
    @PostMapping("/list/investment/work")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData listInvestmentWork(@RequestBody WorkOrderInspectionPackageSelectDTO selectDTO) {
        Assert.notNull(selectDTO.getCurrent(), RespCodeEnum.PARAM_CURRENT_EXCEPTION.getMsgDes());
        Assert.notNull(selectDTO.getSize(), RespCodeEnum.PARAM_SIZE_EXCEPTION.getMsgDes());
        Assert.notNull(selectDTO.getFacId(), "工位不能为空");

        Page<WorkOrderEntity> page = workOrderService.listInvestmentWork(selectDTO);
        return ResponseData.success(page);
    }

    /**
     * 根据产线和多个状态查询工单列表
     *
     * @param lineId
     * @param stateList
     * @return
     */
    @GetMapping("/by/line/state")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getWorkOrderListByLineAndState(@RequestParam(value = "lineId", required = false) Integer lineId,
                                                       @RequestParam(value = "stateList") List<Integer> stateList) {
        List<WorkOrderEntity> list = workOrderService.getWorkOrderByLineAndState(lineId, stateList);
        return success(list);
    }

    /**
     * 根据产线和多个状态查询工单列表(分页)
     *
     * @param lineId
     * @param stateList
     * @return
     */
    @GetMapping("/by/line/state/page")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getWorkOrderListByLineAndStatePage(@RequestParam(value = "lineId", required = false) Integer lineId,
                                                           @RequestParam(value = "stateList") List<Integer> stateList,
                                                           @RequestParam(value = "workOrderNumber") String workOrderNumber,
                                                           @RequestParam(value = "current") Integer current,
                                                           @RequestParam(value = "size") Integer size) {
        Page<WorkOrderEntity> list = workOrderService.getWorkOrderByLineAndStatePage(lineId, stateList, workOrderNumber, current, size);
        return success(list);
    }

    /**
     * 根据工序id查询工单列表
     *
     * @param procedureId 工艺工序id
     * @param current
     * @param size
     * @returni
     */
    @GetMapping("/list/by/procedure")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getWorkOrderListByProcedure(@RequestParam(value = "procedureId") Integer procedureId,
                                                    @RequestParam(value = "current") Integer current,
                                                    @RequestParam(value = "size") Integer size) {
        Page<WorkOrderEntity> page = workOrderService.getWorkOrderListByProcedure(procedureId, current, size, getUsername());
        return success(page);
    }

    /**
     * 根据工作中心id查询工单列表
     *
     * @param workCenterId    工作中心id
     * @param teamId          班组id
     * @param workOrderNumber 工单号（模糊搜索）
     * @param customerName    客户名
     * @param materialName    物料名
     * @param materialCode    物料编码
     * @param startDate       计划开始时间
     * @param endDate         计划结束时间
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/list/by/model")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getWorkOrderListByWorkCenterId(@RequestParam(value = "workCenterId", required = false) Integer workCenterId,
                                                       @RequestParam(value = "teamId", required = false) Integer teamId,
                                                       @RequestParam(value = "lineCode", required = false) String lineCode,
                                                       @RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                                                       @RequestParam(value = "customerName", required = false) String customerName,
                                                       @RequestParam(value = "materialName", required = false) String materialName,
                                                       @RequestParam(value = "materialCode", required = false) String materialCode,
                                                       @RequestParam(value = "startDate", required = false) String startDate,
                                                       @RequestParam(value = "endDate", required = false) String endDate,
                                                       @RequestParam(value = "state", required = false) String state,
                                                       @RequestParam(value = "current", required = false) Integer current,
                                                       @RequestParam(value = "size", required = false) Integer size) {
        Page<WorkOrderEntity> page = workOrderService.getWorkOrderListByWorkCenterId(workCenterId, teamId, lineCode, workOrderNumber, customerName, materialName,
                materialCode, startDate, endDate, state, current, size, getUsername());
        Map<String, String> procedureNameMap = workOrderProcedureRelationService.getProcedureNameMap(page.getRecords());
        for (WorkOrderEntity record : page.getRecords()) {
//            List<String> list = workOrderProcedureRelationService.getCraftProcedureNamesByWorkOrderNumber(record.getWorkOrderNumber());
//            String procedure = String.join(Constant.SEP, list);
            record.setProcedureName(procedureNameMap.get(record.getWorkOrderNumber()));
        }
        return success(page);
    }


    /**
     * 根据工艺工序id查询工单列表
     *
     * @param craftProcedureId 工艺工序id
     * @param orderNumber      订单号
     * @return
     */
    @GetMapping("/craft/procedure/order/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getCraftProcedureOrderList(@RequestParam(value = "craftProcedureId") Integer craftProcedureId,
                                                   @RequestParam(value = "orderNumber") String orderNumber) {
        String username = getUsername();
        List<WorkOrderEntity> list = workOrderProcedureRelationService.getWorkOrderListByCraftProcedureId(craftProcedureId, orderNumber, username);
        return success(list);
    }

    /**
     * 根据工序id查询工单列表
     *
     * @param dto 入参
     * @return 工单列表
     */
    @PostMapping("/procedure/order/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getProcedureOrderList(@RequestBody ProcedureWorkOrderSelectDTO dto) {
        List<WorkOrderEntity> list = workOrderProcedureRelationService.getProcedureOrderList(dto);
        return success(list);
    }

    /**
     * 根据工单查询工序名称
     *
     * @param workOrderNumber 工单号
     * @return
     */
    @GetMapping("/craft/procedure/name")
    public ResponseData getCraftProcedureNameList(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        List<String> list = workOrderProcedureRelationService.getCraftProcedureNamesByWorkOrderNumber(workOrderNumber);
        Object procedure = String.join(Constant.SEP, list);
        return success(procedure);
    }


    /**
     * 获取指定状态的工单列表（非创建，关闭，取消状态的列表）
     *
     * @return
     */
    @GetMapping("/state/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getGrantList() {
        List<WorkOrderEntity> list = workOrderService.getGrantList();
        return success(list);
    }

    /**
     * 获取指定工位下非创建，关闭，取消状态的工单号（非创建，关闭，取消状态的列表）
     *
     * @return
     */
    @GetMapping("/state/number/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getWorkOrderNumberByState(@RequestParam(value = "fid") Integer fid,
                                                  @RequestParam(value = "workOrderNumber", required = false) String workOrderNumber) {
        List<String> workOrderNumberList = workOrderService.getWorkOrderNumberByState(fid, workOrderNumber);
        return success(workOrderNumberList);
    }

    /**
     * 获取指定状态的工单列表（非创建，关闭，取消、完成状态的列表）
     *
     * @return
     */

    @GetMapping("/appoint/state/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getAppointList() {
        List<WorkOrderEntity> list = workOrderService.getAppointList();
        return success(list);
    }

    /**
     * 获取投产、完成、挂起状态的列表（老化管理用）
     *
     * @return
     */
    @GetMapping("/state/for/aging")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getListForAging(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        List<WorkOrderEntity> list = workOrderService.getListForAging(workOrderNumber);
        return success(list);
    }


    /**
     * 创建工单
     *
     * @param workOrderEntity
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "新增了工单号为#{workOrderNumber}的工单")
    @UnitFormatMethod
    public ResponseData addOrder(@RequestBody @Validated({WorkOrderEntity.Insert.class}) WorkOrderEntity workOrderEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        workOrderEntity.setCreateBy(getUsername());
        return success(workOrderService.add(workOrderEntity).getWorkOrderId());
    }


    /**
     * 工单拆分 - 创建/生效状态
     * * *
     *
     * @param workOrderCreateDTOS
     * @param bindingResult
     * @return
     */
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "拆分了工单号为#{workOrderNumber}的工单")
    @PostMapping("/create/from/self")
    public ResponseData createFromWorkOrder(@RequestBody @Validated List<WorkOrderCreateDTO> workOrderCreateDTOS, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        workOrderService.createFromWorkOrder(workOrderCreateDTOS, getUsername());
        return success();
    }


    /**
     * 创建生效的工单
     *
     * @param workOrderEntity
     * @return
     */
    @PostMapping("/insert/released")
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "新增了工单号为#{workOrderNumber}的工单")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData addReleasedWorkOrder(@RequestBody @Validated({WorkOrderEntity.Insert.class}) WorkOrderEntity workOrderEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        workOrderEntity.setCreateBy(username);
        workOrderEntity.setUpdateBy(username);
        WorkOrderEntity entity = workOrderService.addReleasedWorkOrder(workOrderEntity, username);
        return success(entity.getWorkOrderId());
    }

    /**
     * 批量新增工单（新建父工单，自动生成子工单）
     * 这个接口好像没用了，前端也没有使用到的地方。。。
     *
     * @param workOrderEntity
     * @param bindingResult
     * @return
     */
    @PostMapping("/batch/add")
    @Transactional(rollbackFor = Exception.class)
    @OperLog(module = "工单管理", type = OperationType.ADD, desc = "新增了工单号为#{workOrderNumber}的工单")
    public ResponseData batchAdd(@RequestBody @Validated({WorkOrderEntity.Insert.class}) WorkOrderEntity workOrderEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        WorkOrderEntity entity = workOrderService.batchAdd(workOrderEntity, username);
        if (entity != null) {
            return success(entity);
        }
        return fail();
    }

    /**
     * 修改工单信息
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "工单管理", type = OperationType.UPDATE, desc = "修改了工单号为#{workOrderNumber}的工单")
    @UnitFormatMethod
    public ResponseData update(@RequestBody @Validated({WorkOrderEntity.Update.class}) WorkOrderEntity entity,
                               BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateDate(new Date());
        return success(workOrderService.updateByWorkId(entity, username));
    }

    /**
     * 产线报工 修改工单基础信息
     *
     * @param entity
     * @return
     */
    @PutMapping("/update/baseInfo")
    @OperLog(module = "工单管理", type = OperationType.UPDATE, desc = "修改了工单号为#{workOrderNumber}的工单")
    @UnitFormatMethod
    public ResponseData updateBaseInfo(@RequestBody WorkOrderEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateDate(new Date());
        return success(workOrderService.updateBaseInfo(entity, username));
    }

    /**
     * 工单备料情况上报
     * workOrderId
     * prepared
     * assigned
     *
     * @return
     */
    @PutMapping("/update/preparation")
    @OperLog(module = "工单管理", type = OperationType.UPDATE, desc = "修改了工单号为#{workOrderNumber}的工单")
    @UnitFormatMethod
    public ResponseData updatePreparation(@RequestBody WorkOrderEntity entity) {
        String username = getUsername();
        entity.setUpdateBy(username);
        entity.setUpdateDate(new Date());
        workOrderService.updateById(entity);
        WorkOrderEntity workOrderEntity = workOrderService.getById(entity);
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(entity.getWorkOrderId()));
        }
        return success(workOrderEntity);
    }

    /**
     * 查询备料工单列表
     *
     * @param workOrderNumber 工单编号
     * @param workOrderName   工单名称
     * @param current
     * @param size
     * @returnsh
     */
    @GetMapping("/preparation/list")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData preparationOrderList(@RequestParam(value = "workOrderNumber", required = false) String workOrderNumber,
                                             @RequestParam(value = "workOrderName", required = false) String workOrderName,
                                             @RequestParam(value = "current", required = false) Integer current,
                                             @RequestParam(value = "size", required = false) Integer size) {
        String username = getUsername();
        Page<WorkOrderEntity> list = workOrderService.preparationOrderList(workOrderNumber, workOrderName, current, size, username);
        return success(list);
    }

    /**
     * 删除工单前的判断
     * 工单只要在生产中（投产/挂起），完成数量finishCount>0 || 投入数量inputCount>0 || （存在扫码记录，需要先删除流水码，才能删除工单）时，
     * 提示"该工单在生产中，是否删除"，其他情况允许直接删除
     *
     * @param workOrderDetailDTO 工单号
     * @return
     */
    @PostMapping("/judge/before/delete")
    public ResponseData judgeBeforeDelete(@RequestBody WorkOrderDetailDTO workOrderDetailDTO) {
        workOrderService.judgeBeforeDelete(workOrderDetailDTO.getWorkOrderNumber());
        return success();
    }

    /**
     * 删除工单信息
     *
     * @param workOrderId
     * @return
     */
    @DeleteMapping("/delete/{workOrderId}")
    @OperLog(module = "工单管理", type = OperationType.DELETE, desc = "删除了工单号为#{workOrderNumber}的工单")
    public ResponseData delete(@PathVariable Integer workOrderId) {
        WorkOrderEntity workOrderEntity = workOrderService.deleteById(workOrderId, getUsername());
        return success(workOrderEntity);
    }

    /**
     * 批量删除生产工单
     *
     * @param
     * @return
     */
    @PostMapping("/batch/delete")
    public ResponseData batchDelete(@RequestBody WorkOrderNumbersDTO workOrderNumbersDTO) {
        String code = RandomUtil.randomString(10);
        workOrderExtendService.batchDelete(workOrderNumbersDTO, code, operationLogService.manualBuild());
        return success(code, null);
    }

    /**
     * 通过工单Id查询工单信息
     *
     * @param workOrderId
     * @return
     */
    @GetMapping("/select/{workOrderId}")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData selectById(@PathVariable("workOrderId") Integer workOrderId) {
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        return this.selectByNumber(workOrderEntity.getWorkOrderNumber());
    }

    /**
     * 通过工单号查询工单信息
     *
     * @param workOrderNumber
     * @return
     */
    @GetMapping("/select/by/number")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData selectByNumber(@RequestParam String workOrderNumber) {
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderNumber);
        WorkOrderEntity entity = workOrderService.getWorkOrderById(detailDTO);
        return success(entity);
    }

    /**
     * 获取产线实体类列表
     *
     * @return
     */
    @GetMapping("/get/line")
    public ResponseData getLine() {
        return success(workOrderService.getProductionLine());
    }

    /**
     * 获取产品实体类列表
     *
     * @return
     */
    @GetMapping("/get/product")
    public ResponseData getProduct() {
        return success(workOrderService.getProduct());
    }


    /**
     * 获取工序实体类
     */
    @GetMapping("/get/procedure")
    public ResponseData getProcedure() {
        return success(workOrderService.getProcedure());
    }

    /**
     * 获取工艺实体类
     */
    @GetMapping("/get/craft")
    public ResponseData getCraft() {
        List<CraftEntity> list = craftService.lambdaQuery().eq(CraftEntity::getIsTemplate, Constants.FALSE).list();
        return success(list);
    }


    /**
     * 获取所有工单状态
     *
     * @return
     */
    @GetMapping("/all/state")
    public ResponseData getWorkOrderState() {
        return success(workOrderService.getWorkOrderState());
    }

//    /**
//     * 获取工单类型列表(dfs3.13版本后方法废弃，使用新接口获取工单的单据类型)
//     * 新接口及传参为： /v1/open/order_types/detail/category?categoryCode=workOrder
//     *
//     * @return
//     */
//    @GetMapping("/type")
//    @Deprecated
//    public ResponseData getWorkOrderType() {
//        WorkOrderTypeEnum[] values = WorkOrderTypeEnum.values();
//        List<CommonType> types = new ArrayList<>();
//        for (WorkOrderTypeEnum typeEnum : values) {
//            types.add(CommonType.builder().code(typeEnum.getCode()).name(typeEnum.getName()).build());
//        }
//        return success(types);
//    }

    /**
     * 获取工单派工状态枚举
     *
     * @return
     */
    @GetMapping("/assignment/state")
    public ResponseData getAssignmentState() {
        List<CommonType> list = new ArrayList<>();
        AssignmentStateEnum[] values = AssignmentStateEnum.values();
        for (AssignmentStateEnum value : values) {
            list.add(CommonType.builder().code(value.getType()).name(value.getTypeName()).build());
        }
        return ResponseData.success(list);
    }

    /**
     * 获取所有工单执行状态以及对应的code
     *
     * @return
     */
    @GetMapping("/all/execution/state")
    public ResponseData getAllWorkOrderExecutionState() {
        return success(workOrderService.getAllWorkOrderExecutionState());
    }


    /**
     * 获取状态修改的字段
     *
     * @param workOrderId
     * @param newState
     * @return
     */
    @GetMapping("/get/field")
    public ResponseData getFieldList(@RequestParam(value = "workOrderId") String workOrderId,
                                     @RequestParam(value = "newState") Integer newState) {
        List<FieldInfoDTO> list = workOrderService.getFieldList(workOrderId, newState);
        return success(list);
    }

    /**
     * 状态修改
     * 我要用的路径【/update/state】被占用了...
     * 方法名也被占用了
     *
     * @param isBatch
     * @param workOrderEntity
     * @return
     */
    @PutMapping("/update/status")
    @OperLog(module = "工单管理", type = OperationType.UPDATE, desc = "批量修改了父工单号为#{workOrderNumber}的订单的状态")
    public ResponseData updateStateOnly(@RequestParam(value = "isBatch", defaultValue = "0") Integer isBatch,
                                        @RequestBody WorkOrderEntity workOrderEntity) {
        String username = getUsername();
        UpdateTipsDTO updateTipsDTO = workOrderService.updateStateOnly(workOrderEntity, isBatch, username);
        updateTipsDTO.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
        return success(updateTipsDTO);
    }

    /**
     * 生产工单批量编辑
     *
     * @param
     * @return
     */
    @PutMapping("/batch/update")
    public ResponseData batchUpdate(@RequestBody @Validated BatchUpdateWorkOrderDTO entity) {
        String username = getUsername();
        // 随机生成一个code,用于标识本次操作
        String code = RandomUtil.randomString(10);
        workOrderService.batchUpdateWorkOrder(entity, username, code);
        return success(code, null);
    }

    /**
     * 批量编辑处理进度
     *
     * @return
     */
    @GetMapping("/batch/update/progress")
    public ResponseData batchUpdateProgress(@RequestParam String code) {
        return success(commonService.getProgress(com.yelink.dfs.constant.RedisKeyPrefix.WORK_ORDER_BATCH_EDIT_TASK + code));
    }

    /**
     * 批量修改生产工单计划开始结束时间
     *
     * @param
     * @return
     */
    @PutMapping("/update/planeTime")
    public ResponseData batchUpdatePlaneTime(@RequestBody BatchUpdateWorkOrderDTO entity) {
        String username = getUsername();
        List<String> list = workOrderService.batchUpdatePlaneTime(entity, username);
        if (CollectionUtils.isEmpty(list)) {
            return success();
        } else {
            return fail("更新计划时间失败" + String.join(Constant.SEP, list));
        }

    }

    /**
     * 根据id获取子工单列表
     *
     * @param
     * @return
     */
    @GetMapping("/list/{id}")
    public ResponseData getSubListById(@PathVariable(value = "id") Integer id) {
        List<WorkOrderEntity> list = workOrderService.getSubListById(id);
        return success(list);
    }

    /**
     * 获取追溯信息--工单信息（包含工单详情、生产订单(生效、完成、关闭态)）
     *
     * @param
     * @return
     */
    @GetMapping("/trace/work/order/info/{id}")
    public ResponseData getWorkOrderInfoTrace(@PathVariable(value = "id") Integer workOrderId) {
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderEntity.getWorkOrderNumber());
        workOrderEntity = workOrderService.getWorkOrderById(detailDTO);
        List<ProductOrderEntity> orderEntities = getProductOrderEntities(workOrderEntity.getWorkOrderNumber());

        WorkOrderInfoDTO dto = WorkOrderInfoDTO.builder()
                .orderEntities(orderEntities)
                .workOrderEntity(workOrderEntity)
                .build();
        return success(dto);
    }

    private List<ProductOrderEntity> getProductOrderEntities(String workOrderNumber) {
        ProductOrderSelectOpenDTO build = ProductOrderSelectOpenDTO.builder()
                .workOrderNumber(workOrderNumber)
                .states(OrderStateEnum.RELEASED.getCode() + Constant.SEP + OrderStateEnum.FINISHED.getCode() + Constant.SEP + OrderStateEnum.CLOSED.getCode())
                .build();
        PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(build);
        List<ProductOrderEntity> records = pageResult.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<ProductOrderMaterialEntity> productOrderMaterialEntities = records.stream().map(ProductOrderEntity::getProductOrderMaterial).collect(Collectors.toList());
        Map<Integer, List<ProductOrderEntity>> map = records.stream().collect(Collectors.groupingBy(ProductOrderEntity::getProductOrderId));
        List<ProductOrderEntity> orderEntities = new ArrayList<>();
        // 判断生产订单是否为合单，如果为合单，则合与被合的订单都进行展示
        for (ProductOrderMaterialEntity productOrderMaterialEntity : productOrderMaterialEntities) {
            orderEntities.addAll(map.get(productOrderMaterialEntity.getProductOrderId()));
            if (productOrderMaterialEntity.getMergedState().equals(OrderMergeStateEnum.MERGE.getCode())) {
                PageResult<ProductOrderEntity> isMergedOrders = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().mergeOrderNumber(productOrderMaterialEntity.getMergeOrderNumber()).build());
                orderEntities.addAll(isMergedOrders.getRecords());
            }
            if (productOrderMaterialEntity.getMergedState().equals(OrderMergeStateEnum.IS_MERGED.getCode())) {
                // 获取被合单（不包含自身）
                PageResult<ProductOrderEntity> isMergedOrders = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().mergeOrderNumber(productOrderMaterialEntity.getMergeOrderNumber()).build());
                // 合单
                ProductOrderEntity mergeOrder = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(productOrderMaterialEntity.getMergeOrderNumber()).build());
                // 设置状态
                mergeOrder.getProductOrderMaterial().setState(mergeOrder.getState());

                orderEntities.addAll(isMergedOrders.getRecords());
                orderEntities.add(mergeOrder);
            }
        }
        return orderEntities;
    }

    /**
     * 获取追溯信息--采购信息（包含采购需求单、采购清单）
     */
    @GetMapping("/trace/purchase/info/{id}")
    public ResponseData getPurchaseInfoTrace(@PathVariable(value = "id") Integer workOrderId) {
        List<PurchaseEntity> purchaseList = new ArrayList<>();
        List<PurchaseEntity> purchaseMaterialList = new ArrayList<>();
        List<PurchaseRequestEntity> requestMaterialList = new ArrayList<>();
        // 查询工单关联的销售订单及生产订单
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        List<ProductOrderEntity> productOrderEntities = getProductOrderEntities(workOrderEntity.getWorkOrderNumber());
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).workOrderNumber(workOrderEntity.getWorkOrderNumber()).build());

        // 获取采购需求单列表
        // 查询顺序 工单 --> 绑定（生效、完成、关闭）的销售订单和生产订单 --> 通过订单查询采购需求单（生效、完成、关闭） --> 通过采购需求单查询采购订单（生效、完成、关闭）
        List<Integer> purchaseRequestIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(productOrderEntities)) {
            for (ProductOrderEntity productOrderEntity : productOrderEntities) {
                PageResult<PurchaseRequestEntity> requestInterfacePage = extPurchaseRequestInterface.getPage(PurchaseRequestOpenSelectDTO.builder().orderNum(productOrderEntity.getProductOrderNumber()).build());
                if (CollectionUtils.isEmpty(requestInterfacePage.getRecords())) {
                    continue;
                }
                purchaseRequestIds.addAll(requestInterfacePage.getRecords().stream().map(PurchaseRequestEntity::getId).collect(Collectors.toList()));
            }
        }
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            for (SaleOrderVO saleOrderVO : pageResult.getRecords()) {
                PageResult<PurchaseRequestEntity> requestInterfacePage = extPurchaseRequestInterface.getPage(PurchaseRequestOpenSelectDTO.builder().orderNum(saleOrderVO.getSaleOrderMaterial().getSaleOrderNumber()).build());
                if (CollectionUtils.isEmpty(requestInterfacePage.getRecords())) {
                    continue;
                }
                purchaseRequestIds.addAll(requestInterfacePage.getRecords().stream().map(PurchaseRequestEntity::getId).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(purchaseRequestIds)) {
            return success(PurchaseInfoDTO.builder().build());
        }

        // 查询采购需求-订单关联表 获取采购需求单列表
        PurchaseRequestOpenSelectDTO purchaseRequestSelectDTO = PurchaseRequestOpenSelectDTO.builder()
                .states(Arrays.asList(RequestStateEnum.RELEASED.getCode(), RequestStateEnum.FINISHED.getCode(), RequestStateEnum.CLOSED.getCode()))
                .purchaseRequestIds(purchaseRequestIds)
                .build();
        PageResult<PurchaseRequestEntity> purchaseRequestPage = extPurchaseRequestInterface.getPage(purchaseRequestSelectDTO);

        if (!CollectionUtils.isEmpty(purchaseRequestPage.getRecords())) {
            // 获取采购订单列表
            purchaseRequestPage.getRecords().forEach(purchaseRequestEntity -> {
                PurchaseOpenSelectDTO purchaseSelectDTO = PurchaseOpenSelectDTO.builder()
                        .state(PurchaseStateEnum.RELEASED.getCode() + Constant.SEP + PurchaseStateEnum.FINISHED.getCode() + Constant.SEP + PurchaseStateEnum.CLOSED.getCode())
                        .requestCode(purchaseRequestEntity.getRequestNum())
                        .build();
                PageResult<PurchaseEntity> page = extPurchaseInterface.getPage(purchaseSelectDTO);
                purchaseList.addAll(page.getRecords());
            });
            // 获取采购订单的详情及物料信息
            purchaseList.forEach(purchaseEntity -> {
                PurchaseEntity purchaseByCode = extPurchaseInterface.getPurchaseByCode(PurchaseDetailDTO.builder().purchaseCode(purchaseEntity.getPurchaseCode()).build());
                purchaseMaterialList.add(purchaseByCode);
            });
            // 获取采购需求单的详情及物料信息
            purchaseRequestPage.getRecords().forEach(purchaseRequestEntity -> {
                PurchaseRequestEntity requestEntity = extPurchaseRequestInterface.detailById(purchaseRequestEntity.getId());
                requestMaterialList.add(requestEntity);
            });
        }

        PurchaseInfoDTO dto = PurchaseInfoDTO.builder()
                .purchaseEntities(purchaseMaterialList)
                .requestEntities(requestMaterialList)
                .build();
        return success(dto);
    }

    /**
     * 获取追溯信息--设计追溯信息（包含BOM、工艺）
     */
    @GetMapping("/trace/design/info/{id}")
    public ResponseData getDesignInfoTrace(@PathVariable(value = "id") Integer workOrderId) {
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        MaterialEntity materialEntity = materialService.selectByMaterialCode(workOrderEntity.getMaterialCode());
        List<CraftEntity> craftEntityList = craftService.getEntitiesByCraftId(workOrderEntity.getCraftId());
        DesignInfoDTO dto = DesignInfoDTO.builder()
                .materialEntity(materialEntity)
                .craftEntities(craftEntityList)
                .build();
        return success(dto);
    }

    /**
     * 获取追溯信息--仓储信息（包含仓库领料单、成品出库单）
     */
    @GetMapping("/trace/storage/info/{id}")
    public ResponseData getStorageInfoTrace(@PathVariable(value = "id") Integer workOrderId) {
        List<DeliveryApplicationEntity> deliveryApplicationList = new ArrayList<>();
        List<StockRelateOrderEntity> stockRelateOrderList = new ArrayList<>();
        List<StockInAndOutEntity> inAndOutList = new ArrayList<>();
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        Page<TakeOutApplicationEntity> unFilterTakeOutApplicationList = takeOutApplicationService.getTakeOutApplicationList(TakeOutApplicationSelectDTO.builder().workOrderNumber(workOrderEntity.getWorkOrderNumber()).build());
        // 只能获取发放/完成/关闭  状态的数据
        List<TakeOutApplicationEntity> filterTakeOutApplicationEntities = unFilterTakeOutApplicationList.getRecords().stream()
                .filter(takeOutApplicationEntity -> !takeOutApplicationEntity.getState().equals(TakeOutApplicationStateEnum.CREATED.getCode()))
                .filter(takeOutApplicationEntity -> !takeOutApplicationEntity.getState().equals(TakeOutApplicationStateEnum.CANCELED.getCode()))
                .collect(Collectors.toList());
        // 获取关联的销售订单列表
        PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).workOrderNumber(workOrderEntity.getWorkOrderNumber()).build());

        // 通过订单获取关联的出货申请单
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(orderEntity -> {
                LambdaQueryWrapper<DeliveryApplicationEntity> deliveryApplicationWrapper = new LambdaQueryWrapper<>();
                deliveryApplicationWrapper.eq(DeliveryApplicationEntity::getSaleOrderNumber, orderEntity.getSaleOrderNumber())
                        .and(wrapper -> wrapper.eq(DeliveryApplicationEntity::getState, DeliveryApplicationStateEnum.RELEASED.getCode()).or()
                                .eq(DeliveryApplicationEntity::getState, DeliveryApplicationStateEnum.FINISHED.getCode()).or()
                                .eq(DeliveryApplicationEntity::getState, DeliveryApplicationStateEnum.CLOSED.getCode()));
                List<DeliveryApplicationEntity> deliveryApplicationEntities = deliveryApplicationService.list(deliveryApplicationWrapper);
                deliveryApplicationList.addAll(deliveryApplicationEntities);
            });
        }
        // 通过出货申请单获取  出入库单与其它单据关联表
        deliveryApplicationList.forEach(deliveryApplicationEntity -> {
            List<StockRelateOrderEntity> stockRelateOrderEntities = JacksonUtil.getResponseArray(stockRelateOrderInterface.getListByRelateNumber(deliveryApplicationEntity.getApplicationNum(), null), StockRelateOrderEntity.class);
            stockRelateOrderList.addAll(stockRelateOrderEntities);
        });
        // 通过关联表获取成品出库列表
        stockRelateOrderList.forEach(stockRelateOrderEntity -> {
            com.yelink.dfscommon.dto.wms.StockInAndOutSelectDTO stockSelectDTO = com.yelink.dfscommon.dto.wms.StockInAndOutSelectDTO.builder()
                    .orderNumber(stockRelateOrderEntity.getInventoryOrderNumber())
                    .inOrOutType(StockInputOrOutputTypeEnum.OUTPUT_DELIVERY.getTypeCode())
                    .build();
            List<StockInAndOutEntity> unFilterOutputList = JacksonUtil.getResponseArray(stockInAndOutInterface.getOutputList(stockSelectDTO, null), StockInAndOutEntity.class);

            List<StockInAndOutEntity> filterStockInAndOutList = unFilterOutputList.stream()
                    .filter(stockInAndOutEntity -> !stockInAndOutEntity.getState().equals(StockOrderStateEnum.CREATED.getCode()))
                    .filter(stockInAndOutEntity -> !stockInAndOutEntity.getState().equals(StockOrderStateEnum.CANCELED.getCode()))
                    .collect(Collectors.toList());
            inAndOutList.addAll(filterStockInAndOutList);
        });
        StorageInfoDTO dto = StorageInfoDTO.builder()
                .takeOutApplicationList(filterTakeOutApplicationEntities)
                .inAndOutList(inAndOutList)
                .build();
        return success(dto);
    }

    /**
     * 获取追溯信息--质量信息（包含工位质检数据、合格率（手动上报手动采集指标的记录））
     */
    @GetMapping("/trace/quality/info/{id}")
    public ResponseData getQualityInfoTrace(@PathVariable(value = "id") Integer workOrderId) {
        List<RecordWorkOrderUnqualifiedEntity> orderUnqualifiedEntities = new ArrayList<>();
        Map<Date, List<PassRateVO>> map = new LinkedHashMap<>();
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        // 获取在该工位上的质检数据
        LambdaQueryWrapper<FacilitiesEntity> facWrapper = new LambdaQueryWrapper<>();
        facWrapper.eq(FacilitiesEntity::getProductionLineId, workOrderEntity.getLineId())
                .eq(FacilitiesEntity::getTypeOneCode, FacilitiesTypeEnum.CUBICLE.getCode());
        List<FacilitiesEntity> facilitiesEntities = facilitiesService.list(facWrapper);
        if (!CollectionUtils.isEmpty(facilitiesEntities)) {
            DefectRecordQueryDTO selectBuild = DefectRecordQueryDTO.builder()
                    .current(1)
                    .size(Integer.MAX_VALUE)
                    .fid(facilitiesEntities.stream().map(FacilitiesEntity::getFid).collect(Collectors.toList()))
                    .workOrderNumbers(Stream.of(workOrderEntity.getWorkOrderNumber()).collect(Collectors.toList()))
                    .build();
            orderUnqualifiedEntities = workOrderUnqualifiedService.getList(selectBuild).getRecords();
        }
        // 工单的工位质检记录
        DefectRecordQueryDTO selectBuild = DefectRecordQueryDTO.builder()
                .current(1)
                .size(Integer.MAX_VALUE)
                .workOrderNumbers(Stream.of(workOrderEntity.getWorkOrderNumber()).collect(Collectors.toList()))
                .build();
        List<RecordWorkOrderUnqualifiedEntity> workOrderUnqualifiedRecords = workOrderUnqualifiedService.getList(selectBuild).getRecords();
        // 工单的维修记录（质检返工）
        MaintainRecordQueryDTO build = MaintainRecordQueryDTO.builder()
                .current(1)
                .size(Integer.MAX_VALUE)
                .workOrder(workOrderEntity.getWorkOrderNumber())
                .build();
        List<MaintainRecordEntity> maintainRecords = maintainRecordService.getList(build).getRecords();

        // 获取手动采集指标合格率
        getManualTargetPassRate(map, workOrderEntity);
        QualityInfoDTO dto = QualityInfoDTO.builder()
                .orderUnqualifiedEntities(orderUnqualifiedEntities)
                .passRateMap(map)
                .unqualifiedRecords(workOrderUnqualifiedRecords)
                .maintainRecords(maintainRecords)
                .build();
        return success(dto);
    }

    /**
     * 获取手动采集指标合格率
     */
    private void getManualTargetPassRate(Map<Date, List<PassRateVO>> map, WorkOrderEntity workOrderEntity) {
        // 获取手动采集指标的合格率
        LambdaQueryWrapper<RecordManualCollectionEntity> manualCollectWrapper = new LambdaQueryWrapper<>();
        manualCollectWrapper.eq(RecordManualCollectionEntity::getBatch, workOrderEntity.getWorkOrderNumber());
        List<RecordManualCollectionEntity> collectionEntities = recordManualCollectionService.list(manualCollectWrapper);
        Map<Date, List<RecordManualCollectionEntity>> collect = collectionEntities.stream().collect(Collectors.groupingBy(RecordManualCollectionEntity::getReportTime));
        for (Map.Entry<Date, List<RecordManualCollectionEntity>> entry : collect.entrySet()) {
            // 生产数量
            Double productionQuantity = 0.0;
            // 合格品数qualified
            Double qualified = 0.0;
            List<PassRateVO> vos = new ArrayList<>();
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                // 解析json数据
                for (RecordManualCollectionEntity manualCollectionEntity : entry.getValue()) {
                    JSONArray jsonArray = JSONArray.parseArray(manualCollectionEntity.getReportContent());
                    List<RecordManualCollectionDTO> dtos = JSON.parseArray(jsonArray.toJSONString(), RecordManualCollectionDTO.class);
                    for (RecordManualCollectionDTO dto : dtos) {
                        if (dto.getEname().equals(ManualTargetConst.PRODUCTION_QUANTITY)) {
                            productionQuantity += dto.getValue();
                        }
                        if (dto.getEname().equals(ManualTargetConst.QUALIFIED)) {
                            qualified += dto.getValue();
                        }
                    }
                    // 获取设备信息
                    DeviceEntity deviceEntity = deviceService.getById(manualCollectionEntity.getDeviceId());
                    // 合格率
                    Double passRate = productionQuantity == 0 ? 0 : (qualified / productionQuantity);
                    PassRateVO rateVO = PassRateVO.builder()
                            .deviceId(manualCollectionEntity.getDeviceId())
                            .deviceName(deviceEntity.getDeviceName())
                            .passRate(passRate)
                            .build();
                    vos.add(rateVO);
                }
            }
            map.put(entry.getKey(), vos);
        }
    }

    /**
     * 获取追溯信息--生产追溯信息（包含设备运行记录、设备参数、产能节拍）
     */
    @GetMapping("/trace/product/info/{id}")
    public ResponseData getProductInfoTrace(@PathVariable(value = "id") Integer workOrderId) {
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        if (Objects.isNull(workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        // 获取设备运行记录
        List<RecordDeviceDayRunEntity> dayRunEntities = getDeviceDayRunEntities(workOrderEntity);
        List<DeviceVO> deviceVos = new ArrayList<>();
        // 获取设备自动采集指标参数、故障告警
        getDeviceTargetParam(workOrderEntity, deviceVos);
        List<RecordLineBeatEntity> lineBeat = new ArrayList<>();
        double capacity;
        // 获取产能
        if (workOrderEntity.getActualStartDate() == null) {
            capacity = 0.0;
        } else {
            // 获取5min级别的节拍
            String startDate = DateUtil.format(workOrderEntity.getActualStartDate(), DateUtil.DATETIME_FORMAT);
            String endDate = DateUtil.format(workOrderEntity.getActualEndDate() == null ? new Date() :
                    workOrderEntity.getActualStartDate(), DateUtil.DATETIME_FORMAT);
            lineBeat = recordLineBeatService.getLineBeat(workOrderEntity.getLineId(), startDate, endDate);
            long span = DateUtil.getTime(workOrderEntity.getActualStartDate(), workOrderEntity.getActualEndDate() == null ? new Date() :
                    workOrderEntity.getActualEndDate());
            double hourSpan = MathUtil.divideDouble(span, 60 * 60 * 1000, 2);
            capacity = MathUtil.divideDouble(workOrderEntity.getFinishCount() == null ? 0 :
                    workOrderEntity.getFinishCount(), hourSpan, 2);
        }
        //// 获取工单成品码
        //List<WorkOrderBarcodeEntity> barcodeEntities = getBarcodeEntities(workOrderEntity);

        ProductInfoDTO dto = ProductInfoDTO.builder()
                .deviceDayRunEntities(dayRunEntities)
                .deviceList(deviceVos)
                .lineBeat(lineBeat)
                .capacity(capacity)
                .startDate(workOrderEntity.getStartDate())
                .endDate(workOrderEntity.getEndDate())
                //.barcodeEntities(barcodeEntities)
                .build();
        return success(dto);
    }

    /**
     * 工单追溯-生产追溯信息-工单成品码
     */
    @GetMapping("/trace/barcode")
    public ResponseData getWorkOrderTraceBarcode(@RequestParam String workOrderNumber,
                                                 @RequestParam Integer current,
                                                 @RequestParam Integer size,
                                                 @RequestParam(required = false) String barcode) {
        PageData<WorkOrderTraceBarcodeVO> retPageData;

        // 兼容之前逻辑：获取工单成品码
        PageData<WorkOrderBarcodeEntity> page = workOrderBarcodeService.getBarcodeEntities(current, size, workOrderNumber, barcode);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            // 之前逻辑没有找到数据,就走新逻辑
            retPageData = productFlowCodeRecordService.listWorkOrderTraceBarcode(current, size, workOrderNumber, barcode);
        } else {
            List<WorkOrderTraceBarcodeVO> list = new ArrayList<>();
            for (WorkOrderBarcodeEntity record : page.getRecords()) {
                list.add(WorkOrderTraceBarcodeVO.builder()
                        .barcode(record.getBarcode())
                        .inputFid(record.getInputFid())
                        .inputFname(record.getInputFname())
                        .inputTime(record.getInputTime())
                        .produceFid(record.getProduceFid())
                        .produceFname(record.getProduceFname())
                        .produceTime(record.getProduceTime())
                        .build());
            }
            retPageData = new PageData<>(current, size, page.getTotal(), list);
        }
        return ResponseData.success(retPageData);
    }

    /**
     * 获取设备自动采集指标参数、故障告警
     */
    private void getDeviceTargetParam(WorkOrderEntity workOrderEntity, List<DeviceVO> deviceVos) {
        if (workOrderEntity.getLineId() == null) {
            return;
        }
        List<DeviceEntity> deviceList = new ArrayList<>();
        // 获取所属产线
        LambdaQueryWrapper<ProductionLineEntity> lineWrapper = new LambdaQueryWrapper<>();
        lineWrapper.eq(ProductionLineEntity::getProductionLineCode, workOrderEntity.getLineCode());
        ProductionLineEntity lineEntity = lineService.getOne(lineWrapper);
        // 通过产线获取设备列表(工位维度)
        LambdaQueryWrapper<FacilitiesEntity> facWrapper = new LambdaQueryWrapper<>();
        facWrapper.eq(FacilitiesEntity::getProductionLineId, lineEntity.getProductionLineId());
        List<FacilitiesEntity> facilitiesEntities = facilitiesService.list(facWrapper);
        facilitiesEntities.forEach(facilitiesEntity -> {
            LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
            deviceWrapper.eq(DeviceEntity::getFid, facilitiesEntity.getFid())
                    .isNotNull(DeviceEntity::getFid);
            List<DeviceEntity> deviceEntities = deviceService.list(deviceWrapper);
            deviceList.addAll(deviceEntities);
        });
        // 通过产线获取设备列表(产线维度)
        LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
        deviceWrapper.eq(DeviceEntity::getProductionLineId, lineEntity)
                .isNotNull(DeviceEntity::getProductionLineId);
        List<DeviceEntity> deviceEntities = deviceService.list(deviceWrapper);
        deviceList.addAll(deviceEntities);

        deviceList.forEach(deviceEntity -> {
            Page<TargetModelEntity> targetModelList = targetModelService.getList(deviceEntity.getModelId(),
                    deviceEntity.getDeviceId(), null, null);
            Page<AlarmEntity> faultAlarmList = alarmService.getFaultAlarmList(DateUtil.format(workOrderEntity.getStartDate(), DateUtil.DATETIME_FORMAT), DateUtil.format(workOrderEntity.getEndDate(),
                    DateUtil.DATETIME_FORMAT), null, null, null, deviceEntity.getDeviceId(), null, null, false);
            DeviceVO deviceVO = DeviceVO.builder()
                    .deviceId(deviceEntity.getDeviceId())
                    .deviceCode(deviceEntity.getDeviceCode())
                    .deviceName(deviceEntity.getDeviceName())
                    .targetModelList(targetModelList)
                    .alarmList(faultAlarmList)
                    .build();
            deviceVos.add(deviceVO);
        });
    }

    /**
     * 获取设备运行记录
     */
    private List<RecordDeviceDayRunEntity> getDeviceDayRunEntities(WorkOrderEntity workOrderEntity) {
        LambdaQueryWrapper<RecordDeviceDayRunEntity> recordWrapper = new LambdaQueryWrapper<>();
        recordWrapper.eq(RecordDeviceDayRunEntity::getOrderNumber, workOrderEntity.getWorkOrderNumber());
        List<RecordDeviceDayRunEntity> dayRunEntities = recordDeviceDayRunService.list(recordWrapper);
        // 获取设备运行记录
        dayRunEntities.forEach(recordDeviceDayRunEntity -> {
            recordDeviceDayRunEntity.setStateName(recordDeviceDayRunEntity.getState() == null ? null : DevicesStateEnum.getNameByCode(recordDeviceDayRunEntity.getState()));
            // 获取设备
            DeviceEntity deviceEntity = deviceService.getEntityById(recordDeviceDayRunEntity.getDeviceId());
            recordDeviceDayRunEntity.setDeviceEntity(deviceEntity);
            // 获取设备自动指标
            LambdaQueryWrapper<TargetModelEntity> targetModelWrapper = new LambdaQueryWrapper<>();
            targetModelWrapper.eq(TargetModelEntity::getModelId, deviceEntity.getModelId());
            List<TargetModelEntity> autoTargetList = targetModelService.list(targetModelWrapper);
            List<String> autoTargets = autoTargetList.stream().map(TargetModelEntity::getTargetName).collect(Collectors.toList());
            // 获取设备手动采集指标
            LambdaQueryWrapper<DictEntity> dictWrapper = new LambdaQueryWrapper<>();
            dictWrapper.eq(DictEntity::getDes, deviceEntity.getModelId());
            List<DictEntity> manualTargetList = dictService.list(dictWrapper);
            List<String> manualTargets = manualTargetList.stream().map(DictEntity::getCode).collect(Collectors.toList());
            String autoTargetStr = StringUtils.join(autoTargets, ",");
            String manualTargetStr = StringUtils.join(manualTargets, ",");
            String targetStr = autoTargetStr + "," + manualTargetStr;
            ConfigYzjScreenEntity yzjScreenEntity = ConfigYzjScreenEntity.builder()
                    .modelId(deviceEntity.getModelId())
                    .deviceId(deviceEntity.getDeviceId())
                    .deviceName(deviceEntity.getDeviceName())
                    .targetName(targetStr)
                    .build();
            // 获取记录时间
            SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtil.DATE_FORMAT);
            SimpleDateFormat timeFormat = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
            String date = dateFormat.format(recordDeviceDayRunEntity.getRecordDate());
            String startStr = date + " " + recordDeviceDayRunEntity.getStartTime() + ":00";
            try {
                Date startDate = timeFormat.parse(startStr);
                List<ScreenDeviceDTO> deviceDtos = yangtzeRiverService.getTarget(yzjScreenEntity, startDate);
                recordDeviceDayRunEntity.setDeviceDtos(deviceDtos);
            } catch (ParseException e) {
                log.error("异常消息：", e);
            }
        });
        return dayRunEntities;
    }

    /**
     * 创建工单计划
     *
     * @param workOrderPlanEntities
     */
    @PostMapping("/plan/edit")
    public ResponseData addOrderPlan(@RequestBody List<WorkOrderPlanEntity> workOrderPlanEntities) {
        for (WorkOrderPlanEntity workOrderPlanEntity : workOrderPlanEntities) {
            //判断是否存在该记录，不存在插入，存在修改
            LambdaQueryWrapper<WorkOrderPlanEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderPlanEntity.getWorkOrderNumber());
            wrapper.eq(WorkOrderPlanEntity::getTime, workOrderPlanEntity.getTime());
            WorkOrderPlanEntity workOrderPlanEntityTmp = workOrderPlanService.getOne(wrapper);
            if (workOrderPlanEntityTmp == null) {
                workOrderPlanEntity.setCreateBy(getUsername());
                workOrderPlanEntity.setCreateDate(new Date());
                workOrderPlanService.save(workOrderPlanEntity);
            } else {
                workOrderPlanEntity.setId(workOrderPlanEntityTmp.getId());
                workOrderPlanEntity.setUpdateBy(getUsername());
                workOrderPlanEntity.setUpdateDate(new Date());
                workOrderPlanService.updateById(workOrderPlanEntity);
            }
        }

        return success();
    }

    /**
     * 创建工单计划
     *
     * @return
     */
    @GetMapping("/plan/list/{workOrderNumber}")
    public ResponseData editOrderPlan(@PathVariable(value = "workOrderNumber") String workOrderNumber) {
        //找到工单
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            return null;
        }
        LambdaQueryWrapper<WorkOrderPlanEntity> workOrderPlanWrapper = new LambdaQueryWrapper<>();
        workOrderPlanWrapper.eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderNumber);
        List<WorkOrderPlanEntity> resultList = workOrderPlanService.list(workOrderPlanWrapper);
        return success(resultList);
    }

    /**
     * 拿到当天工单计划
     *
     * @return
     */
    @GetMapping("/plan/today/{workOrderNumber}")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getTodayOrderPlan(@PathVariable(value = "workOrderNumber") String workOrderNumber) {
        Date dateTemp = dictService.getDayOutputBeginTime(new Date());
        Date date = DateUtil.formatToDate(dateTemp, DateUtil.DATETIME_FORMAT_ZERO);
        WorkOrderPlanEntity workOrderPlanEntity = insertOrEditPlan(workOrderNumber, date);
        return success(workOrderPlanEntity);
    }

    /**
     * 拿到工单当天的计划数量，没有返回空
     *
     * @param workOrderNumber
     * @param date
     * @return
     */
    private WorkOrderPlanEntity insertOrEditPlan(String workOrderNumber, Date date) {
        LambdaQueryWrapper<WorkOrderPlanEntity> workOrderPlanWrapper = new LambdaQueryWrapper<>();
        workOrderPlanWrapper.eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderNumber);
        workOrderPlanWrapper.eq(WorkOrderPlanEntity::getTime, date);
        WorkOrderPlanEntity workOrderPlanEntity = workOrderPlanService.getOne(workOrderPlanWrapper);
        if (workOrderPlanEntity == null) {
            workOrderPlanEntity = WorkOrderPlanEntity.builder().workOrderNumber(workOrderNumber).time(date).build();
        }
        return workOrderPlanEntity;
    }

    /**
     * 查看工单图纸
     *
     * @param
     * @return
     */
    @GetMapping("/view/drawing/{materialCode}")
    public ResponseData viewWordOrderDrawing(@PathVariable(value = "materialCode") String materialCode) {
        String url = workOrderService.viewDrawing(materialCode);
        return success(url);
    }


    /**
     * 保存编辑前提示计划数量与每日计划数量总和是否一致
     *
     * @param
     * @return
     */
    @GetMapping("/check/plan")
    public ResponseData checkPlan(@RequestParam(value = "workOrderNumber") String workOrderNumber, @RequestParam(value = "planQuantity") Double planQuantity) {
        String hint = workOrderService.checkPlan(workOrderNumber, planQuantity);
        return success((Object) hint);
    }


    /**
     * 生产工单列表导出 - 下载默认模板
     * 查询最新的10条工单数据导出数据源excel
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/default/export/template")
    public void listDefaultExportTemplate(HttpServletResponse response) throws IOException {
        WorkOrderSelectDTO workOrderSelectDTO = new WorkOrderSelectDTO();
        workOrderSelectDTO.setCurrent(1);
        workOrderSelectDTO.setSize(10);
        IPage<WorkOrderEntity> page = workOrderService.getWorkOrderEntityPage(workOrderSelectDTO, getUsername());
        List<WorkOrderExportDTO> list = workOrderService.convertToWorkOrderExportDTO(page.getRecords());
//         EasyExcelUtil.export(response, "生产工单默认导出模板", "数据源", list, WorkOrderExportDTO.class);
        EasyExcelUtil.export(response, "生产工单默认导出模板", "数据源", list, WorkOrderExportDTO.class, ExcelExportFormEnum.WORK_ORDER.getFullPathCode());
    }


    /**
     * 生产工单列表导出 - 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/upload/list/export/template")
    public ResponseData uploadListExportTemplate(MultipartFile file) throws IOException {
        workOrderService.uploadListExportTemplate(file, getUsername());
        return success();
    }


    /**
     * 生产工单列表导出 - 生产工单模板自定义模板下载
     * 分页查询并填写数据源sheet的内容
     *
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/download/list/export/template")
    public ResponseData downloadListExportTemplate(HttpServletResponse response, Integer id) throws IOException {
        //获取最新的十条数据源
        WorkOrderSelectDTO workOrderSelectDTO = new WorkOrderSelectDTO();
        workOrderSelectDTO.setCurrent(1);
        workOrderSelectDTO.setSize(10);
        IPage<WorkOrderEntity> page = workOrderService.getWorkOrderEntityPage(workOrderSelectDTO, getUsername());
        List<WorkOrderExportDTO> list = workOrderService.convertToWorkOrderExportDTO(page.getRecords());
        //获取模板文件
        ModelUploadFileEntity uploadFile = modelUploadFileService.getById(id);
        byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
        //下载模板
        EasyExcelUtil.writeToExcelTemplate(response, list, "数据源", uploadFile.getFileName(), bytes, WorkOrderExportDTO.class, "workOrder.detail");
        return success();
    }


    /**
     * 生产工单导出  :异步
     *
     * @param workOrderSelectDTO
     * @return
     * @throws IOException
     */
    @PostMapping("/syc/exports")
    public ResponseData export(@RequestBody WorkOrderSelectDTO workOrderSelectDTO) {
        Long result = workOrderService.exportTask(workOrderSelectDTO, getUsername());
        return success(result);
    }


    /**
     * 分页查询当前导出任务列表
     *
     * @param size
     * @param current
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData taskPage(@RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer size,
                                 @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer current) {
        IPage<ExcelTask> iPage = workOrderService.taskPage(current, size);
        return success(iPage);
    }


    /**
     * 查询 导出进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = workOrderService.taskById(taskId);
        return success(excelTask);
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        workOrderService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        workOrderService.approveBatch(dto);
        return success();
    }

    /**
     * 获取指定产线下边未完成的工单列表（生效、投产、挂起状态）
     *
     * @return
     */
    @GetMapping("/incomplete/list/{lineId}")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getIncompleteList(@PathVariable String lineId) {
        List<WorkOrderEntity> list = workOrderService.getIncompleteList(lineId);
        return success(list);
    }

    /**
     * 获取工单列表（生效、投产、挂起状态）
     */
    @GetMapping("/quality/list")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData getQualityList(@RequestParam(required = false) String workOrderNumber) {
        List<WorkOrderEntity> list = workOrderService.getQualityList(workOrderNumber);
        return success(list);
    }

    /**
     * 通过工单号获取该工单信息
     *
     * @return
     */
    @GetMapping("/by/number")
    public ResponseData getWorkOrderByNumber(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        WorkOrderEntity entity = workOrderService.getWorkOrderByNumber(workOrderNumber);
        return success(entity);
    }

    /**
     * 通过工单号获取工单的附件(支持附件名称模糊查询)
     *
     * @param workOrderNumber
     * @param fileName
     * @return
     */
    @GetMapping("/work/order/file")
    public ResponseData getWorkOrderFileByWorkNumber(@RequestParam(value = "workOrderNumber") String workOrderNumber,
                                                     @RequestParam(value = "fileName", required = false) String fileName) {
        List<CraftFileDTO> workOrderFileList = workOrderService.getWorkOrderFileByWorkNumber(workOrderNumber, fileName);
        return success(workOrderFileList);
    }


    /**
     * 获取工单物料的可选工序列表
     *
     * @param materialCode
     * @return
     */
    @GetMapping("/craft/procedure/list")
    public ResponseData getProcedureList(@RequestParam(value = "craftId", required = false) Integer craftId,
                                         @RequestParam(value = "materialCode") String materialCode) {
        return success(workOrderService.getCraftProcedureList(craftId, materialCode));
    }

    /**
     * 获取工厂所有的工作中心（根据选择的物料来判断制工作中心是否存在工序）
     *
     * @param
     * @return
     */
    @GetMapping("/all/line/procedure")
    public ResponseData getAllLineModelWithProcedure(@RequestParam(value = "materialCode") String materialCode,
                                                     @RequestParam(value = "craftId", required = false) Integer craftId,
                                                     @RequestParam(value = "businessType", required = false) String businessType
    ) {
        return success(workOrderService.getAllWorkCenterWithMaterialCode(materialCode, craftId, businessType));
    }


    /**
     * 查询生效、投产、挂起、工单列表（模糊查询 下拉选）
     *
     * @param workOrderSelectDTO
     * @return
     */

    @GetMapping("/select/list")
    public ResponseData orderTheStateList(WorkOrderSelectDTO workOrderSelectDTO) {
        List<WorkOrderEntity> list = workOrderService.getTheStateList(workOrderSelectDTO, getUsername());
        return success(list);
    }

    /**
     * 导入管理-上传自定义excel模板
     */
    @PostMapping("/import/template")
    @OperLog(module = "导入日志", type = OperationType.ADD, desc = "导入生产工单自定义模板")
    public ResponseData importExcelTemplate(MultipartFile file) {
        workOrderService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 导入管理-下载默认模板：下载程序内默认的模板，下载后可对模板进行导入数据sheet和目标数据的公式映射关系进行修改
     */
    @GetMapping("/export/default/template")
    public void exportExcelDefaultTemplate(HttpServletResponse response) throws Exception {
        workOrderService.downloadDefaultTemplate("classpath:template/workOrderTemplate.xlsx", response, "生产工单默认导入模板" + Constant.XLSX);
    }

    /**
     * 导入管理-下载数据导入模板
     */
    @GetMapping("/export/template")
    public void exportExcelTemplate(HttpServletResponse response) throws Exception {
        //找到导入的模板
        byte[] bytes = workOrderService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "生产工单数据导入模板" + Constant.XLSX);
    }

    /**
     * 导入管理：下载自定义导入模板
     *
     * @param response
     */
    @GetMapping("/export/custom/template")
    public void exportCustomExcel(HttpServletResponse response) throws IOException {
        //找到导入的模板
        InputStream inputStream = workOrderService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "生产工单自定义数据导入模板" + Constant.XLSX);
    }


    /**
     * 导入管理-生产工单导入
     *
     * @param file
     * @return
     */
    @PostMapping("/analysis/import")
    public ResponseData analysisImport(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = RedisKeyPrefix.WORK_ORDER_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        workOrderService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 导入管理-获取工单导入的进度
     *
     * @return
     */
    @GetMapping("/import/progress")
    public ResponseData getMaterialCompletenessProgress(@RequestParam(value = "key") String key) {
        Object obj = redisTemplate.opsForValue().get(key);
        return success(obj);
    }

    /**
     * 导入管理-查询导入记录列表
     *
     * @param fileName
     * @param startTime
     * @param endTime
     * @param current
     * @param size
     * @return
     */
    @RequestMapping("/import/record")
    public ResponseData recordList(@RequestParam(value = "fileName", required = false) String fileName,
                                   @RequestParam(value = "startTime", required = false) String startTime,
                                   @RequestParam(value = "endTime", required = false) String endTime,
                                   @RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                   @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        return success(workOrderService.recordList(current, size, fileName, startTime, endTime));
    }

    /**
     * 生产日计划导入-下载默认模板
     */
    @GetMapping("/plan/default/template")
    public void downloadWorkPlanDefaultTemplate(HttpServletResponse response) throws Exception {
        workOrderPlanService.downloadDefaultTemplate("classpath:template/workOrderPlanTemplate.xlsx", response, "生产日计划导入默认模板" + Constant.XLSX);
    }

    /**
     * 生产日计划导入-上传自定义转换模板
     */
    @PostMapping("/plan/import/template")
    public ResponseData importWorkPlanExcelTemplate(MultipartFile file) {
        workOrderPlanService.uploadCustomTemplate(file, this.getUsername());
        return success();
    }

    /**
     * 生产日计划导入-下载自定义转换模板
     */
    @GetMapping("/plan/custom/template")
    public void downloadWorkPlanCustomExcel(HttpServletResponse response) throws IOException {
        //找到导入的模板
        InputStream inputStream = workOrderPlanService.downloadCustomImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "生产日计划自定义数据导入模板" + Constant.XLSX);
    }

    /**
     * 生产日计划导入-下载数据导入模板
     */
    @GetMapping("/plan/import/template")
    public void downloadWorkPlanImportTemplate(HttpServletResponse response) throws Exception {
        //找到导入的模板
        byte[] bytes = workOrderPlanService.downloadImportTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "生产日计划数据导入模板" + Constant.XLSX);
    }

    /**
     * 生产日计划导入-导入数据
     */
    @PostMapping("/plan/import")
    public ResponseData workPlanImport(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        String importProgressKey = com.yelink.dfs.constant.RedisKeyPrefix.WORK_ORDER_PLAN_IMPORT_PROGRESS + DateUtil.format(new Date(), DateUtil.yyyyMMddHHmmss_FORMAT) + "_" + RandomUtil.randomString(2);
        workOrderPlanService.sycImportData(file.getOriginalFilename(), file.getInputStream(), getUsername(), importProgressKey);
        return success(importProgressKey);
    }

    /**
     * 生产日计划导入-获取导入的进度
     *
     * @return
     */
    @GetMapping("/plan/import/progress")
    public ResponseData getWorkPlanImportProgress() {
        return success(workOrderPlanService.importProgress());
    }

    /**
     * 生产日计划导入-查看日志
     */
    @RequestMapping("/plan/import/record")
    public ResponseData workPlanRecordList(@RequestParam(value = "fileName", required = false) String fileName,
                                           @RequestParam(value = "startTime", required = false) String startTime,
                                           @RequestParam(value = "endTime", required = false) String endTime,
                                           @RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                           @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        return success(importDataRecordService.getList(ImportTypeEnum.WORK_ORDER_PLAN_IMPORT.getType(), fileName, startTime, endTime, current, size));
    }

    /**
     * 查询作业工单下的附件
     *
     * @return
     */
    @GetMapping("/select/appendix/workorder")
    public ResponseData selectOperationFiles(@RequestParam(value = "workOrderNumber") String workOrderNumber,
                                             @RequestParam(value = "name", required = false) String name,
                                             @RequestParam(value = "procedureId", required = false) Integer procedureId,
                                             @RequestParam(value = "type", required = false) String type) {
        List<CraftFileNewDTO> fileNewDTOS = workOrderService.getAllFiles(workOrderNumber, name, type, procedureId);
        return success(fileNewDTOS);
    }


    /**
     * 查询工位下的工单详情(包括工位信息、工位绑定的设备号)
     *
     * @param workOrderNumber
     * @param facId
     * @return
     */
    @GetMapping("/select/workorder/fac")
    public ResponseData selectWorkOrderFac(@RequestParam(value = "workOrderNumber") String workOrderNumber,
                                           @RequestParam(value = "facId") Integer facId,
                                           @RequestParam(value = "craftProcedureId", required = false) Integer craftProcedureId) {
        WorkOrderFacDTO workOrderFacDTO = workOrderService.selectWorkOrderFac(workOrderNumber, facId, craftProcedureId);
        return success(workOrderFacDTO);
    }

    /**
     * 查询工单详情(基础信息)
     *
     * @param workOrderNumber
     * @return
     */
    @GetMapping("/select/simple/workorder/code")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public ResponseData selectSimpleWorkOrderByCode(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        return success(workOrderService.getSimpleWorkOrderByNumber(workOrderNumber));
    }

    /**
     * 修改工单产线信息
     *
     * @param entity 工单entity
     * @return
     */
    @PutMapping("/update/line")
    public ResponseData updateLineById(@RequestBody WorkOrderEntity entity) {
        String username = getUsername();
        return success(workOrderService.updateLineById(entity, username));
    }

    /**
     * 查看工单的生产基本单元投产记录
     *
     * @param selectDTO
     * @return
     */
    @PostMapping("/input_record/list")
    public ResponseData getWorkOrderInputRecordList(@RequestBody WorkOrderBasicUnitInvestSelectDTO selectDTO) {
        return success(workOrderExtendService.getWorkOrderInputRecords(selectDTO));
    }

    /**
     * 小程序-获取工单工作中心下所有可选的生产基本单元列表
     *
     * @param workOrderNumberDTO 工单号
     * @return
     */
    @PostMapping("/get/all/product_basic_unit")
    public ResponseData getWorkOrderAllProductBasicUnit(@RequestBody WorkOrderNumberDTO workOrderNumberDTO) {
        return success(workOrderExtendService.getWorkOrderAllProductBasicUnit(workOrderNumberDTO));
    }


    /**
     * 查询工单下的工序列表
     *
     * @param workOrderNumber 工单号
     * @return
     */
    @GetMapping("/select/procedure/list")
    public ResponseData selectWorkOrderProcedure(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        List<WorkOrderProcedureRelationEntity> relationEntity = workOrderService.selectWorkOrderProcedureList(workOrderNumber);
        return success(relationEntity);
    }

    /**
     * 校验工单号（是否存在、工单是否为创建态）
     */
    @GetMapping("/check/work/order")
    public ResponseData checkWorkOrder(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        return success(workOrderService.checkWorkOrder(workOrderNumber));
    }

    /**
     * 生产工单新增前的判断（求和只计算最后一道工序的工单和不绑定工序的工单）：
     * 1、销售订单关联的生产工单的数量之和(包括当前的生产工单) > 销售订单的计划数量，给前端提示
     * 2、如果生产订单关联的生产工单的数量之和(包括当前的生产工单) > 生产订单的计划数量，给前端提示
     *
     * @param entity 生产工单
     * @return
     */
    @PostMapping("/insert/judge")
    public ResponseData judgeBeforeInsert(@RequestBody WorkOrderEntity entity) {
        return success(workOrderService.judgeBeforeInsert(entity));
    }

    /**
     * 生成工单流转记录
     *
     * @param entity 流转记录
     * @return
     */
    @PostMapping("/insert/flow")
    public ResponseData workOrderFlow(@RequestBody WorkOrderFlowEntity entity) {
        return success(workOrderFlowService.saveWorkOrderFlow(entity, getUsername()));
    }


    /**
     * 获取工单流转记录
     *
     * @param workOrderNumber 工单号
     * @return
     */
    @GetMapping("/flow/list")
    public ResponseData getWorkOrderFlowList(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        return success(workOrderFlowService.getWorkOrderFlowList(workOrderNumber));
    }

    /**
     * 生成工单投入记录
     *
     * @param entity 流转记录
     * @return
     */
    @PostMapping("/insert/input")
    public ResponseData workOrderInput(@RequestBody WorkOrderInputEntity entity) {
        return success(workOrderInputService.saveWorkOrderInput(entity, getUsername()));
    }

    @PutMapping("/update/input")
    public ResponseData updateWorkOrderInput(@RequestBody WorkOrderInputEntity entity) {
        return success(workOrderInputService.updateWorkOrderInput(
                WorkOrderInputUpdateDTO.builder()
                        .id(entity.getId())
                        .input(entity.getInput())
                        .operation(getUsername())
                         .build()
                )
        );
    }

    /**
     * 获取工单投入记录
     *
     * @param workOrderNumber 工单号
     * @return
     */
    @GetMapping("/input/list")
    public ResponseData getWorkOrderInputList(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        return success(workOrderInputService.getWorkOrderInputList(workOrderNumber));
    }

    /**
     * 扫码前判断工单是否存在
     * 状态过滤
     */
    @GetMapping("/judge/exist")
    public ResponseData judgeWorkOrderExist(@RequestParam(value = "workOrderNumber") String workOrderNumber) {
        boolean b = workOrderService.getWorkOrderByNumberForAppScan(workOrderNumber) != null;
        return success(b);
    }

    /**
     * 生产作业小程序：
     * 扫码--判断条码 是生产设备 还是生产工单
     *
     * @param barCode 条形码
     * @return 条码类型
     */
    @GetMapping("/judge/code/model")
    public ResponseData judgeBarCodeModel(@RequestParam(value = "barCode") String barCode,
                                          @RequestParam(value = "scanType") String scanType) {
        return success(workOrderService.judgeBarCodeType(barCode, scanType));
    }

    /**
     * 获取多级BOM(如有多个，找状态生效，且创建时间最近的)
     *
     * @param
     * @return
     */
    @GetMapping("/multi/level/bom")
    public ResponseData getMultiLevelBom(@RequestParam(value = "materialCode") String materialCode,
                                         @RequestParam(value = "workOrderNum") String workOrderNum) {
        return success(workOrderService.getMultiLevelBom(materialCode, workOrderNum));
    }

    /**
     * 获取工单某日产量
     *
     * @param entity
     * @return
     */
    @PostMapping("/get/day/count")
    public ResponseData getWorkOrderDayCount(@RequestBody RecordWorkOrderDayCountEntity entity) {
        if (StringUtils.isBlank(entity.getWorkOrderNumber())) {
            throw new ResponseException("工单号必传");
        }
        if (entity.getTime() == null) {
            throw new ResponseException("时间必传");
        }
        return success(recordWorkOrderDayCountService.getWorkOrderDayCount(entity.getWorkOrderNumber(), DateUtil.formatToDate(entity.getTime(), DateUtil.DATETIME_FORMAT_ZERO)));
    }

    /**
     * 查询工单每日产量
     *
     * @param entity
     * @return
     */
    @PostMapping("/day/count/work_order_number")
    public ResponseData getWorkOrderDayCountList(@RequestBody RecordWorkOrderDayCountEntity entity) {
        if (StringUtils.isBlank(entity.getWorkOrderNumber())) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_NUMBER_NOT_NULL);
        }
        return success(recordWorkOrderDayCountService.getWorkOrderDayCountList(entity.getWorkOrderNumber()));
    }

    /**
     * 获取生产工单关联的生产订单详情
     *
     * @param workOrderId 工单id
     */
    @GetMapping("/select/product/order/{workOrderId}")
    public ResponseData selectByWorkOrder(@PathVariable Integer workOrderId,
                                          @RequestParam(value = "craftProcedureId", required = false) Integer craftProcedureId) {
        ProductOrderEntity productOrder = workOrderService.getProductOrderByWorkOrder(workOrderId, craftProcedureId);
        return success(productOrder);
    }

    /**
     * 生产工单 生产订单扫码跳转
     *
     * @param orderNumber 工单 or 订单号
     * @return
     */
    @GetMapping("/scan/judge")
    public ResponseData judgeWorkOrderOrProductOrder(@RequestParam(value = "orderNumber") String orderNumber) {
        JudgeOrderVO judgeOrderVO = workOrderService.judgeWorkOrderOrProductOrder(orderNumber, getUsername());
        return success(judgeOrderVO);
    }

    /**
     * 扫码批次号
     * 通过批次号查询关联的工单
     */
    @GetMapping("/getByBarcode")
    public ResponseData getWorkOrderByBarcode(@RequestParam String barcode) {
        BarCodeEntity barCodeEntity = barCodeService.lambdaQuery()
                .eq(BarCodeEntity::getBarCode, barcode)
                .one();
        if (Objects.isNull(barCodeEntity) || StringUtils.isEmpty(barCodeEntity.getRelateNumber())) {
            log.error("系统无此批次或该批次没有关联工单号{}", barcode);
            throw new ResponseException(RespCodeEnum.SYSTEM_NOT_BAR_CODE);
        }
        String relateNumber = barCodeEntity.getRelateNumber();
        WorkOrderEntity workOrder = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getWorkOrderNumber, relateNumber)
                .one();
        if (Objects.isNull(workOrder)) {
            log.error("该批次关联工单不存在,barcode:{},relateNumber:{}", barcode, relateNumber);
            throw new ResponseException("该批次关联工单不存在");
        }
        // 填充物料信息
        MaterialEntity material = materialService.getEntityByCodeAndSkuId(workOrder.getMaterialCode(), workOrder.getSkuId());
        if (Objects.nonNull(material)) {
            workOrder.setMaterialId(material.getId());
            workOrder.setMaterialFields(material);
        }
        return success(workOrder);
    }

    /**
     * 完成率枚举
     */
    @GetMapping("/completion/rate")
    public ResponseData getCompletionRateEnum() {
        List<CommonType> list = new ArrayList<>();
        CompletionRateEnum[] values = CompletionRateEnum.values();
        for (CompletionRateEnum value : values) {
            list.add(CommonType.builder().code(value.getType()).name(value.getTypeName()).build());
        }
        return success(list);
    }

    /**
     * 优先级的等级以及对应的code
     *
     * @return
     */
    @GetMapping("/all/grades")
    public ResponseData getAllGrade() {
        return success(workPropertise.getPriority());
    }


    /**
     * 车间每日产品产量汇总报表 - 分页
     */
    @PostMapping("/get/count/perday")
    public ResponseData getWorkCountPerDay(@RequestBody WorkCountPerDayVO vo) {
        return success(recordWorkOrderDayCountService.getWorkCountPerDay(vo));
    }

    /**
     * 车间每日产品产量汇总报表 - 导出
     */
    @PostMapping("/export/count/perday")
    public void exportWorkCountPerDay(HttpServletResponse response, @RequestBody WorkCountPerDayVO vo) throws IOException {
        recordWorkOrderDayCountService.exportWorkCountPerDay(response, vo);
    }

    /**
     * 工单标签打印前校验 标签是否已打印
     *
     * @param
     * @return
     */
    @PostMapping("/judge/print")
    public ResponseData judgeBeforePrint(@RequestBody WorkOrderSelectDTO selectDTO) {
        workOrderService.judgeBeforePrint(selectDTO);
        return success();
    }


    /**
     * 工单标签打印
     *
     * @return
     */
    @OperLog(module = "生产工单标签", type = OperationType.PRINT, desc = "打印了编码为#{printCodes}的生产工单")
    @PostMapping("/print")
    public ResponseData print(@RequestBody WorkOrderSelectDTO selectDTO) {
        log.info("{}调用了打印接口：{}", getUsername(), JacksonUtil.toJSONString(selectDTO));
        PrintDTO print = workOrderService.print(selectDTO);
        return success(print);
    }


    /**
     * 刷新工单每日报工表，用于内部校正数据，无前端调用
     *
     * @return
     */
    @PostMapping("/reprot/fresh/day/count")
    public ResponseData reportWorkDayCount(@RequestParam String workOrderNumber, @RequestParam(required = false) Date date) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        recordWorkOrderDayCountService.updateWorkOrderDayCountFlowCode(workOrderEntity, date);
        recordWorkOrderDayCountService.updateWorkOrderDayInputFlowCode(workOrderEntity, date);
        recordWorkOrderDayCountService.updateWorkOrderDayUnqualifiedFlowCode(workOrderEntity, date);
        return success();
    }

    /**
     * 工单流水码生成列表
     *
     * @return
     */
    @PostMapping("/generate/code/list")
    public ResponseData generateCodeList(@RequestBody BatchGenerateCodeReqDTO dto) {
        return ResponseData.success(workOrderService.generateCodeList(dto));
    }

    /**
     * 获取工单关联资源类型及关联资源列表
     *
     * @param workOrder 工单号
     * @return
     */
    @GetMapping("/relevance/resource/list")
    public ResponseData getWorkOrderDeviceList(@RequestParam(value = "workOrder") String workOrder) {
        RelevanceResourceDTO list = workOrderService.getWorkOrderRelevanceResourceList(workOrder);
        return success(list);
    }

    /**
     * 追溯生产工单的生产订单，销售订单
     *
     * @param orderNumber 单号
     * @return
     */
    @PostMapping("/trace/product/sale/orders")
    public ResponseData getTraceOrderByWorkOrderNumber(@RequestParam(value = "orderType") String orderType,
                                                       @RequestParam(value = "orderNumber") String orderNumber) {
        OrderTraceGroupVO traceGroupVO = workOrderExtendService.traceOrderByOrderNumber(orderType, orderNumber);
        return success(traceGroupVO);
    }

    /**
     * 保存/更新生产工单前进行数量校验
     *
     * @param quantityVerifyDTOS 生产订单号，生产订单计划数、本次单据新增的物料数量
     * @return
     */
    @PostMapping("/quantity/verify")
    public ResponseData quantityVerify(@RequestBody List<WorkOrderQuantityVerifyDTO> quantityVerifyDTOS) {
        return ResponseData.success(workOrderService.quantityVerify(quantityVerifyDTOS));
    }

    /**
     * 生产工单投产检验
     *
     * @param workOrderNumbers 需要检查的工单编号
     * @return
     */
    @PostMapping("/invest/check")
    public ResponseData investCheck(@RequestBody List<String> workOrderNumbers) {
        workOrderService.investCheck(workOrderNumbers);
        return ResponseData.success();
    }

    /**
     * 批量投产检查处理进度
     *
     * @return
     */
    @GetMapping("/batch/invest/check/progress")
    public ResponseData batchInvestCheckProgress() {
        String s = workOrderService.getBatchInvestCheckProgress();
        return success(s);
    }

    /**
     * 生产工单投产前进行投产检验
     *
     * @param workOrderIds 需要检验的工单
     * @return
     */
    @PostMapping("/invest/verify")
    public ResponseData investVerify(@RequestBody List<Integer> workOrderIds) {
        return ResponseData.success(workOrderService.investVerify(workOrderIds));
    }

    /**
     * 查询基本生产单元
     *
     * @param selectDTO
     * @return
     */
    @PostMapping("/basic/unit")
    public ResponseData getBasicUnit(@RequestBody(required = false) ProductionBasicUnitSelectDTO selectDTO) {
        return ResponseData.success(workOrderExtendService.getBasicUnit(selectDTO));
    }

    /**
     * 判断用户是否对工单有权限
     *
     * @param
     * @return
     */
    @GetMapping("/isolation")
    public ResponseData workOrderIsolation(@RequestParam String workOrderNumber) {
        return ResponseData.success(workOrderService.workOrderIsolation(this.getUsername(), workOrderNumber));
    }

    /**
     * 最新一条状态更新的工单
     *
     * @param lineId 产线id
     * @return 工单
     */
    @GetMapping("/byLine/lastStateChangeOrder")
    public ResponseData lastStateChangeOrder(@RequestParam String lineId) {
        return ResponseData.success(workOrderService.lastStateChangeOrder(lineId));
    }

    /**
     * 查询工单的工序检验项结果列表
     *
     * @param
     * @return
     */
    @PostMapping("/procedure/inspect/result/list")
    public ResponseData getProcedureInspectResultList(@RequestBody WorkOrderDetailDTO detailDTO) {
        return ResponseData.success(procedureInspectResultService.getProcedureInspectResultList(detailDTO.getWorkOrderNumber(), getUsername()));
    }

    /**
     * 更新工单的工序检验项结果列表
     *
     * @param
     * @return
     */
    @PutMapping("/procedure/inspect/result/update")
    public ResponseData getProcedureInspectResultList(@RequestBody WorkOrderProcedureInspectResultVO dto) {
        procedureInspectResultService.updateProcedureInspectResultList(dto, getUsername());
        return ResponseData.success();
    }

    /**
     * 下推进度
     * 生产工单用料清单（批量）/ 按工艺路线下推生产工单
     */
    @GetMapping("/push/process")
    public ResponseData pushDownProcess(@RequestParam String code) {
        return ResponseData.success(commonService.getProgress(RedisKeyPrefix.PUSH_DOWN_PROGRESS + code));
    }

    /**
     * 查询该用户是否允许反改工单状态
     */
    @GetMapping("/state/change")
    public ResponseData workOrderStateChange() {
        String username = getUsername();
        return ResponseData.success(workOrderExtendService.workOrderStateChange(username));
    }

    /**
     * 根据订单id获取订单是否关联工单
     *
     * @param orderIds
     * @return
     */
    @PostMapping("/exist/relate/work/order")
    public ResponseData existRelateOrder(@RequestParam List<Integer> orderIds) {
        String string = workOrderService.existRelateOrder(orderIds);
        return ResponseData.success(string, null);
    }

    /**
     * 源单据：获取生产工单物料行列表
     *
     * @return
     */
    @PostMapping("/material_list")
    public ResponseData getPushDownMaterialList(@RequestBody OrderMaterialListDTO dto) {
        return ResponseData.success(workOrderExtendService.getPushDownMaterialList(dto));
    }

    /**
     * 目标单据：获取下推生产工单的物料行列表
     */
    @PostMapping("/craft/push/material/list")
    public ResponseData getTargetPushMaterialList(@RequestBody AbstractPushDTO pushDown) {
        List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> list = workOrderExtendService.getTargetPushMaterialList(pushDown);
        return ResponseData.success(list);
    }

    /**
     * 目标单据：生成生产工单
     */
    @PostMapping("/craft/push/work/order")
    public ResponseData pushWorkOrder(@RequestBody CraftRoutePushWorkOrderVO pushDown) {
        String code = RandomUtil.randomString(10);
        workOrderExtendService.pushWorkOrder(pushDown, getUsername(), code);
        return ResponseData.success(code, null);
    }

    /**
     * 查询工单绑定的工艺信息
     */
    @PostMapping("/bind/craft/info")
    public ResponseData getBindCraftInfo(@RequestBody WorkOrderNumbersDTO workOrderNumbersDTO) {
        return ResponseData.success(workOrderExtendService.getBindCraftInfo(workOrderNumbersDTO));
    }

    /**
     * 查询基本生产单元类型
     *
     * @param
     * @return
     */
    @PostMapping("/basic/unit/type")
    public ResponseData getBasicUnitType(@RequestBody(required = false) ProductionBasicUnitTypeSelectDTO selectDTO) {
        return ResponseData.success(workOrderExtendService.getBasicUnitType(selectDTO));
    }

    /**
     * 工单关联绑定操作员
     *
     * @param
     * @return
     */
    @PostMapping("/bind/operator")
    public ResponseData bindOperator(@RequestBody WorkOrderOperatorDTO operatorDTO) {
        workOrderExtendService.bindOperator(operatorDTO);
        return ResponseData.success();
    }

    /**
     * 查询工单已经关联绑定的操作员
     *
     * @param
     * @return
     */
    @PostMapping("/select/bind/operator")
    public ResponseData selectBindOperator(@RequestBody WorkOrderOperatorSelectDTO operatorDTO) {
        return ResponseData.success(workOrderExtendService.selectBindOperator(operatorDTO));
    }

    /**
     * 根据生产订单获取工单的单据类型
     *
     * @param
     * @return
     */
    @PostMapping("/order_type/by/product_order")
    public ResponseData getOrderTypeByProductOrder(@RequestBody ProductOrderParamDTO paramDTO) {
        return ResponseData.success(workOrderExtendService.getOrderTypeByProductOrder(paramDTO));
    }

}
