package com.yelink.dfs.controller.common.config;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.PushDownFullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierTreeDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 单据下推标识管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "单据下推标识管理")
@RestController
@AllArgsConstructor
@RequestMapping("/config/push/down/identifier")
public class OrderPushDownIdentifierController extends BaseController {

    private final OrderPushDownIdentifierService orderPushDownIdentifierService;

    /**
     * 获取下推标识树形结构
     */
    @ApiOperation("获取下推标识树形结构")
    @PostMapping("/tree")
    public ResponseData getTree(@RequestBody PushDownFullPathCodeDTO dto) {
        List<PushDownIdentifierTreeDTO> tree = orderPushDownIdentifierService.getPushDownIdentifierTree(dto);
        return success(tree);
    }

}
