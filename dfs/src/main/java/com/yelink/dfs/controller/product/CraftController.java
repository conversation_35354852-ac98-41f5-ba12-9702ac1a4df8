package com.yelink.dfs.controller.product;

import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.constant.product.ControlItemEnum;
import com.yelink.dfs.constant.product.CraftStateEnum;
import com.yelink.dfs.constant.product.CraftTypeEnum;
import com.yelink.dfs.constant.product.InspectComparatorEnum;
import com.yelink.dfs.constant.product.InspectDefectJudgeEnum;
import com.yelink.dfs.constant.product.ProcedureFlowTypeEnum;
import com.yelink.dfs.constant.product.ProcedureInspectTypeEnum;
import com.yelink.dfs.constant.product.TimeoutThresholdTypeEnum;
import com.yelink.dfs.entity.code.ProductFlowCodeEntity;
import com.yelink.dfs.entity.code.dto.ScannerDTO;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.common.ModelUploadFileEntity;
import com.yelink.dfs.entity.common.VersionChangeCraftProcedureDTO;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftMaterialRelationEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.CraftProcedureInspectControllerEntity;
import com.yelink.dfs.entity.product.ProcedureControllerConfigEntity;
import com.yelink.dfs.entity.product.ProcedureDeviceTargetReferenceEntity;
import com.yelink.dfs.entity.product.ProcedureDeviceTypeEntity;
import com.yelink.dfs.entity.product.ProcedureInspectionEntity;
import com.yelink.dfs.entity.product.ProcedureMaterialEntity;
import com.yelink.dfs.entity.product.ProcedureMaterialUsedEntity;
import com.yelink.dfs.entity.product.ProcedurePostEntity;
import com.yelink.dfs.entity.product.ProcedureProcessAssemblyEntity;
import com.yelink.dfs.entity.product.ProcedureRelationWorkHoursEntity;
import com.yelink.dfs.entity.product.dto.CraftBindMaterialDTO;
import com.yelink.dfs.entity.product.dto.CraftCopyDTO;
import com.yelink.dfs.entity.product.dto.CraftFileDTO;
import com.yelink.dfs.entity.product.dto.CraftFileNewDTO;
import com.yelink.dfs.entity.product.dto.CraftProcedureWorkCenterGroupDTO;
import com.yelink.dfs.entity.product.dto.CraftRouteDTO;
import com.yelink.dfs.entity.product.dto.CraftSelectDTO;
import com.yelink.dfs.entity.product.dto.ImpactAnalysisSelectDTO;
import com.yelink.dfs.entity.product.dto.ProcedureFlowTypeDTO;
import com.yelink.dfs.entity.product.dto.ProcessParameterConfigDTO;
import com.yelink.dfs.entity.product.dto.SimpleCraftExportDTO;
import com.yelink.dfs.entity.product.dto.SimpleCraftTempExportDTO;
import com.yelink.dfs.entity.product.vo.CraftRouteVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.impl.product.CraftExportHandler;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureAssembleExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureControlExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureDeviceExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureExportDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureHumanResourcesExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureMaterialExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureMaterialUsedExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureTempExportDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureTempMaterialExcelDTO;
import com.yelink.dfs.service.impl.product.dto.CraftProcedureWorkDurationExcelDTO;
import com.yelink.dfs.service.product.CraftMaterialRelationService;
import com.yelink.dfs.service.impl.product.vo.CraftProcedureInspectExportVO;
import com.yelink.dfs.service.product.CraftProcedureInspectControllerService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.ProcedureControllerConfigService;
import com.yelink.dfs.service.product.ProcedureDeviceTargetReferenceService;
import com.yelink.dfs.service.product.ProcedureDeviceTypeService;
import com.yelink.dfs.service.product.ProcedureInspectionService;
import com.yelink.dfs.service.product.ProcedureMaterialService;
import com.yelink.dfs.service.product.ProcedureMaterialUsedService;
import com.yelink.dfs.service.product.ProcedureRelationWorkHoursService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.constant.CommonEnum;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.WhetherEnum;
import com.yelink.dfscommon.constant.dfs.CraftRelatedConditionEnum;
import com.yelink.dfscommon.constant.dfs.code.DataTypeEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.SheetExportDataDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.common.BatchChangeStateDTO;
import com.yelink.dfscommon.entity.common.ZipFileDTO;
import com.yelink.dfscommon.entity.dfs.productFlowCode.ProcedureInspectionConfigEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelTemplateImportUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.ZipAndRarUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description: 工艺接口
 * @Author: zengzhengfu
 * @Date: 2021/3/8
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/crafts")
public class CraftController extends BaseController {

    private CraftService craftService;
    private RedisTemplate redisTemplate;
    private CraftProcedureService craftProcedureService;
    private ProcedureMaterialUsedService procedureMaterialUsedService;
    private ProcedureDeviceTypeService deviceTypeService;
    private ProcedureDeviceTypeService procedureDeviceTypeService;
    private ProcedureDeviceTargetReferenceService procedureDeviceTargetReferenceService;
    private ProcedureControllerConfigService procedureControllerConfigService;
    private ProcedureRelationWorkHoursService procedureRelationWorkHoursService;
    private final ProcedureInspectionService procedureInspectionService;
    private final ExcelService excelService;
    private ModelUploadFileService modelUploadFileService;
    private FastDfsClientService fastDfsClientService;
    private CraftProcedureInspectControllerService craftProcedureInspectControllerService;
    private ImportProgressService importProgressService;
    private ProcedureMaterialService procedureMaterialService;
    private CraftMaterialRelationService craftMaterialRelationService;

    private VersionChangeRecordService versionChangeRecordService;

    /**
     * 获取所属产品的工艺列表
     */
    @PostMapping("/list")
    public ResponseData list(@RequestBody CraftSelectDTO selectDTO) {
        Page<CraftEntity> list = craftService.getList(selectDTO);
        return success(list);
    }

    /**
     * 获取工艺详情
     *
     * @return
     */
    @GetMapping("/detail/{id}")
    public ResponseData list(@PathVariable(value = "id") Integer id) {
        return success(craftService.getEntityById(id));
    }

    /**
     * 获取所有工艺列表
     *
     * @param
     * @return
     */
    @GetMapping("/getAll")
    public ResponseData getAll(@RequestParam(required = false) Integer state) {
        List<CraftEntity> list = craftService.lambdaQuery()
                .eq(CraftEntity::getIsTemplate, Constants.FALSE)
                .eq(state != null, CraftEntity::getState, state)
                .list();
        return success(list);
    }

    @GetMapping("/getAllCraft")
    public ResponseData getAllCraft() {
        return success(craftService.list());
    }

    /**
     * 新增工艺
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了工艺编码为#{craftCode}的工艺")
    public ResponseData add(@RequestBody @Validated({CraftEntity.Insert.class}) CraftEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        craftService.saveEntity(entity);
        return ResponseData.success(entity);
    }

    /**
     * 新增工艺
     *
     * @param entity
     * @return
     */
    @PostMapping("/insert/released")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了工艺编码为#{craftCode}的工艺")
    public ResponseData saveReleasedEntity(@RequestBody @Validated({CraftEntity.Insert.class}) CraftEntity entity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        entity.setCreateBy(username);
        entity.setUpdateBy(username);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(entity.getCreateTime());
        craftService.saveReleasedEntity(entity);
        return ResponseData.success(entity);
    }

    /**
     * 修改工艺/工艺模板
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了工艺编码为#{craftCode}的工艺/工艺模板")
    public ResponseData update(@RequestBody @Validated({CraftEntity.Update.class}) CraftEntity entity) {
        entity.setUpdateTime(new Date());
        entity.setUpdateBy(getUsername());
        boolean update = craftService.updateEntityById(entity);
        if (update) {
            return success(entity);
        }
        return fail(RespCodeEnum.CRAFT_FAIL2UP);
    }

    /**
     * 删除工艺，同时删除关联的工序
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了工艺编码为#{craftCode}，名称为#{name}的工艺")
    public ResponseData delete(@PathVariable Integer id) {
        CraftEntity entity = craftService.removeEntityById(id);
        if (entity == null) {
            return fail(RespCodeEnum.CRAFT_FAIL2DEL);
        }
        return success(entity);
    }

    /**
     * 工艺导出
     */
    @Deprecated
    @PostMapping("/procedure/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) String craftIds,
                       @RequestBody(required = false) CraftSelectDTO selectDTO) {
        boolean isTemplate = Objects.nonNull(selectDTO.getIsTemplate()) && selectDTO.getIsTemplate();
        // 工艺工序
        List<CraftProcedureExportDTO> exports = craftService.listExportDTO(craftIds, selectDTO);

        //工序物料配置
        List<CraftProcedureMaterialExcelDTO> procedureMaterials = new ArrayList<>();
        //工序用料配置
        List<CraftProcedureMaterialUsedExcelDTO> procedureMaterialUseds = new ArrayList<>();
        //工序工装配置
        List<CraftProcedureAssembleExcelDTO> procedureAssembles = new ArrayList<>();
        //工序设备配置
        List<CraftProcedureDeviceExcelDTO> procedureDevices = new ArrayList<>();
        //工序人力配置
        List<CraftProcedureHumanResourcesExcelDTO> humanResources = new ArrayList<>();
        //工序工时配置
        List<CraftProcedureWorkDurationExcelDTO> procedureWorkDurations = new ArrayList<>();
        //工序检验配置
        List<CraftProcedureInspectExportVO> procedureInspects = new ArrayList<>();
        //工序控制配置
        List<CraftProcedureControlExcelDTO> procedureControls = new ArrayList<>();
        // 工艺模板关联物料配置
        List<CraftProcedureTempMaterialExcelDTO> craftTempMaterials = new ArrayList<>();

        if (!CollectionUtils.isEmpty(exports)) {
            for (CraftProcedureExportDTO craftProcedure : exports) {

                CraftProcedureEntity procedureConfig = craftProcedureService.getProcedureConfig(craftProcedure.getCraftProcedureId());
                // 工序物料配置
                if (!isTemplate) {
                    List<ProcedureMaterialEntity> procedureMaterialList = procedureConfig.getProcedureMaterialList();
                    if (!CollectionUtils.isEmpty(procedureMaterialList)) {
                        for (ProcedureMaterialEntity temp : procedureMaterialList) {
                            procedureMaterials.add(CraftProcedureMaterialExcelDTO.builder()
                                    .craftCode(craftProcedure.getCraftCode())
                                    .craftProcedureName(craftProcedure.getProcedureName())
                                    .materialCode(temp.getMaterialCode())
                                    .materialName(temp.getMaterialName())
                                    .build());
                        }
                    }
                }

                // 工序用料配置
                List<ProcedureMaterialUsedEntity> procedureMaterialUsedList = procedureConfig.getProcedureMaterialUsedList();
                if (!CollectionUtils.isEmpty(procedureMaterialUsedList)) {
                    for (ProcedureMaterialUsedEntity temp : procedureMaterialUsedList) {
                        procedureMaterialUseds.add(CraftProcedureMaterialUsedExcelDTO.builder()
                                .craftCode(craftProcedure.getCraftCode())
                                .craftProcedureName(craftProcedure.getProcedureName())
                                .materialCode(temp.getMaterialCode())
                                .materialName(temp.getMaterialName())
                                .number(temp.getNumber())
                                .unit(temp.getUnit())
                                .extend(temp.getExtend())
                                .build());
                    }
                }
                // 工序工装配置
                List<ProcedureProcessAssemblyEntity> procedureProcessAssemblyList = procedureConfig.getProcedureProcessAssemblyEntities();
                if (!CollectionUtils.isEmpty(procedureProcessAssemblyList)) {
                    for (ProcedureProcessAssemblyEntity temp : procedureProcessAssemblyList) {
                        procedureAssembles.add(CraftProcedureAssembleExcelDTO.builder()
                                .craftCode(craftProcedure.getCraftCode())
                                .craftProcedureName(craftProcedure.getProcedureName())
                                .assembleCode(temp.getProcessAssemblyCode())
                                .count(temp.getNumber())
                                .build());
                    }
                }
                // 工序设备配置
                List<ProcedureDeviceTypeEntity> deviceTypeList = procedureConfig.getDeviceTypeList();
                if (!CollectionUtils.isEmpty(deviceTypeList)) {
                    for (ProcedureDeviceTypeEntity temp : deviceTypeList) {
                        procedureDevices.add(CraftProcedureDeviceExcelDTO.builder()
                                .craftCode(craftProcedure.getCraftCode())
                                .craftProcedureName(craftProcedure.getProcedureName())
                                .deviceTypeName(temp.getDeviceTypeName())
//                                .count(Double.valueOf(temp.getNumber()))
                                .build());
                    }
                }
                // 工序人力配置
                List<ProcedurePostEntity> procedurePostList = procedureConfig.getProcedurePostEntities();
                if (!CollectionUtils.isEmpty(procedurePostList)) {
                    for (ProcedurePostEntity temp : procedurePostList) {
                        humanResources.add(CraftProcedureHumanResourcesExcelDTO.builder()
                                .craftCode(craftProcedure.getCraftCode())
                                .craftProcedureName(craftProcedure.getProcedureName())
                                .positionCode(temp.getPostCode())
                                .positionLevel(temp.getPostLevel())
                                .count(Double.valueOf(temp.getNumber()))
                                .build());
                    }
                }
                // 工序工时配置
                List<ProcedureRelationWorkHoursEntity> procedureWorkHoursList = procedureConfig.getProcedureWorkHoursList();
                if (!CollectionUtils.isEmpty(procedureWorkHoursList)) {
                    for (ProcedureRelationWorkHoursEntity temp : procedureWorkHoursList) {
                        procedureWorkDurations.add(CraftProcedureWorkDurationExcelDTO.builder()
                                .craftCode(craftProcedure.getCraftCode())
                                .craftProcedureName(craftProcedure.getProcedureName())
                                .preparationTime(temp.getPreparationTime())
                                .preparationTimeUnit(temp.getPreparationTimeUnit())
                                .processingHours(temp.getProcessingHours())
                                .processingHoursUnit(temp.getProcessingHoursUnit())
                                .defaultTime(temp.getDefaultTime())
                                .defaultTimeUnit(temp.getDefaultTimeUnit())
                                .degreeOfDifficulty(temp.getDegreeOfDifficulty())
                                .isCalculateStr(temp.getIsByWork() ? "是" : "否")
                                .inputNum(temp.getInputNum())
                                .theoryHours(temp.getTheoryHours())
                                .theoryHoursUnit(temp.getTheoryHoursUnit())
                                .build());
                    }
                }
                // 工序检验配置
                List<ProcedureInspectionConfigEntity> procedureInspectionConfigEntityList = procedureConfig.getProcedureInspectionConfigEntityList();
                if (!CollectionUtils.isEmpty(procedureInspectionConfigEntityList)) {
                    for (ProcedureInspectionConfigEntity temp : procedureInspectionConfigEntityList) {
                        if (StringUtils.isNotBlank(temp.getInspectType())) {
                            String[] codes = temp.getInspectType().split(Constants.SEP);
                            List<String> names = new ArrayList<>();
                            for (String code : codes) {
                                names.add(ProcedureInspectTypeEnum.getNameByCode(code));
                            }
                            temp.setInspectTypeName(String.join(Constants.SEP, names));
                        }
                        ProcedureInspectionEntity inspectionEntity = procedureInspectionService.getById(temp.getProcedureInspectionId());
                        procedureInspects.add(CraftProcedureInspectExportVO.builder()
                                .craftCode(craftProcedure.getCraftCode())
                                .craftProcedureName(craftProcedure.getProcedureName())
                                .inspectCode(Objects.isNull(inspectionEntity) ? "" : inspectionEntity.getInspectionCode())
                                .inspectionName(Objects.isNull(inspectionEntity) ? "" : inspectionEntity.getInspectionName())
                                .inspectionStandard(temp.getInspectionStandard())
                                .inspectionInstrument(temp.getInspectionInstrument())
                                .description(temp.getDescription())
                                .dataType(DataTypeEnum.getNameByCode(temp.getDataType()))
                                .comparatorName(InspectComparatorEnum.getNameByCode(temp.getComparator()))
                                .standardValue(temp.getStandardValue())
                                .upperLimit(temp.getUpperLimit())
                                .downLimit(temp.getDownLimit())
                                .defaultValue(temp.getDefaultValue())
                                .inspectTypeNames(temp.getInspectTypeName())
                                .ngControllerStr(WhetherEnum.getNameByCode(temp.getNgController()))
                                .valueCheckStr(WhetherEnum.getNameByCode(temp.getValueCheck()))
                                .resultUpdateStr(getResultUpdateStr(temp.getResultUpdate()))
                                .isDefectStr(WhetherEnum.getNameByCode(temp.getIsDefect()))
                                .defectJudgeTypeStr(InspectDefectJudgeEnum.getNameByCode(temp.getDefectJudgeType()))
                                .unit(temp.getUnit())
                                .build());
                    }
                }
                // 工序控制配置
                ProcedureControllerConfigEntity configEntity = procedureConfig.getProcedureControllerConfigEntity();
                if (configEntity != null) {
                    List<String> tempProcedureInspects = new ArrayList<>();
                    if (configEntity.getJumpStationCheck()) {
                        tempProcedureInspects.add(ControlItemEnum.JUMP_STATION_CHECK.getName());
                    }
                    if (configEntity.getReformCheck()) {
                        tempProcedureInspects.add(ControlItemEnum.REFORM_CHECK.getName());
                    }
                    if (configEntity.getMaterialCheck()) {
                        tempProcedureInspects.add(ControlItemEnum.MATERIAL_CHECK.getName());
                    }
                    if (configEntity.getProductionCheck()) {
                        tempProcedureInspects.add(ControlItemEnum.PRODUCTION_CHECK.getName());
                    }

                    procedureControls.add(CraftProcedureControlExcelDTO.builder()
                            .craftCode(craftProcedure.getCraftCode())
                            .craftProcedureName(craftProcedure.getProcedureName())
                            .flowTimeoutThresholdConf(configEntity.getFlowTimeoutThresholdConfiguration())
                            .flowTimeoutThresholdConfUnitTypeName(TimeoutThresholdTypeEnum.getCodeByName(configEntity.getFlowTimeoutThresholdConfigurationUnitType()))
                            .productTimeoutThresholdConf(configEntity.getProductionTimeoutThresholdConfiguration())
                            .productTimeoutThresholdConfUnitTypeName(TimeoutThresholdTypeEnum.getCodeByName(configEntity.getProductionTimeoutThresholdConfigurationUnit()))
                            .maxFailCount(configEntity.getMaxFailCount())
                            .standardCirculationDurationTypeName(configEntity.getStandardCirculationDurationTypeName())
                            .standardCirculationDuration(configEntity.getStandardCirculationDuration())
                            .procedureInspect(String.join(Constant.SEP, tempProcedureInspects))
                            .build());
                }
            }
            // 查询工艺模板关联的物料列表
            if (isTemplate) {
                List<String> craftCodes = exports.stream().map(CraftProcedureExportDTO::getCraftCode).distinct().collect(Collectors.toList());
                craftTempMaterials = craftMaterialRelationService.lambdaQuery().in(CraftMaterialRelationEntity::getCraftCode, craftCodes)
                        .list().stream()
                        .map(o -> CraftProcedureTempMaterialExcelDTO.builder()
                                .craftCode(o.getCraftCode())
                                .materialCode(o.getMaterialCode())
                                .build()).collect(Collectors.toList());
            }
        }
        // 找到导出的模板
        String location;
        if (!isTemplate) {
            location = "classpath:template/craftProcedureExportTemplate.xlsx";
        } else {
            location = "classpath:template/craftProcedureTempExportTemplate.xlsx";
        }
        byte[] bytes = ExcelUtil.exportDefaultExcel(location);

        List<SheetExportDataDTO> sheetExportDataList = new ArrayList<>();
        if (!isTemplate) {
            sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工艺工序").data(exports).tClass(CraftProcedureExportDTO.class).build());
            sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序物料配置").data(procedureMaterials).tClass(CraftProcedureMaterialExcelDTO.class).build());
        } else {
            List<CraftProcedureTempExportDTO> tempExportDTOS = JacksonUtil.convertArray(exports, CraftProcedureTempExportDTO.class);
            sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工艺工序").data(tempExportDTOS).tClass(CraftProcedureTempExportDTO.class).build());
        }
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序用料配置").data(procedureMaterialUseds).tClass(CraftProcedureMaterialUsedExcelDTO.class).build());
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序工装配置").data(procedureAssembles).tClass(CraftProcedureAssembleExcelDTO.class).build());
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序设备配置").data(procedureDevices).tClass(CraftProcedureDeviceExcelDTO.class).build());
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序人力配置").data(humanResources).tClass(CraftProcedureHumanResourcesExcelDTO.class).build());
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序工时配置").data(procedureWorkDurations).tClass(CraftProcedureWorkDurationExcelDTO.class).build());
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序检验配置").data(procedureInspects).tClass(CraftProcedureInspectExportVO.class).build());
        sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工序控制配置").data(procedureControls).tClass(CraftProcedureControlExcelDTO.class).build());
        if (isTemplate) {
            sheetExportDataList.add(SheetExportDataDTO.builder().sheetName("工艺关联物料配置").data(craftTempMaterials).tClass(CraftProcedureTempMaterialExcelDTO.class).build());
        }
        // 填充sheet页
        EasyExcelUtil.writeToExcelTemplate(response, "工艺工序导出文件" + ExcelUtil.XLSX, bytes, sheetExportDataList);
    }
    public String getResultUpdateStr(Boolean resultUpdate) {
        if(resultUpdate == null) {
            return null;
        }
        return resultUpdate? Constant.ARTIFICIAL_JUDGE : Constant.AUTO_JUDGE;
    }

    /**
     * 工艺异步导出
     */
    @PostMapping("/syc/exports")
    public ResponseData export(@RequestBody CraftSelectDTO craftSelectDTO) {
        DataExportParam<CraftSelectDTO> dataExportParam = craftSelectDTO
                .setExportFileName(BusinessCodeEnum.CRAFT_EXPORT_RECORD.getCodeName())
                .setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.CRAFT_EXPORT_RECORD.name());
        dataExportParam.setCreateUserCode(getUsername());
        Long result = excelService.doExport(dataExportParam, CraftExportHandler.class);
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/export/task/page")
    public ResponseData exportExcel(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                    @RequestParam(required = false, defaultValue = "1") Integer currentPage
    ) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.CRAFT_EXPORT_RECORD.name());
        return success(excelService.listPage(excelTask, currentPage, pageSize));
    }


    /**
     * 查询 导入进度
     *
     * @return
     */
    @GetMapping("/export/task/{taskId}")
    public ResponseData exportExcel(@PathVariable Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.CRAFT_EXPORT_RECORD.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            log.info("任务id错误");
            return fail();
        }
        return success(records.get(0));
    }


    /**
     * 工艺模板导出(1.6版本没使用到)
     *
     * @return
     */
    @GetMapping("/excel/export")
    public void export(HttpServletResponse response) {
        try {
            craftService.export(response);
        } catch (Exception e) {
            log.error("导出excel异常", e);
        }
    }

    /**
     * 工艺批量导入
     *
     * @return
     */
    @PostMapping("/batch/import")
    public ResponseData batchImport(MultipartFile file) {
        try {
            craftService.batchImport(file, getUsername());
            return success();
        } catch (Exception e) {
            log.error("工艺导入异常", e);
            return fail(e.getMessage());
        }
    }

    /**
     * 获取BOM状态
     *
     * @return
     */
    @GetMapping("/state")
    public ResponseData getCraftState() {
        List<CommonState> list = new ArrayList<>();
        CraftStateEnum[] values = CraftStateEnum.values();
        for (CraftStateEnum value : values) {
            CommonState type = new CommonState();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 添加工艺路线的工序
     *
     * @param
     * @return
     */
    @PostMapping("/procedure/add")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了工艺编码为#{craftCode}-工序名称为#{procedureName}的工序")
    public ResponseData addProcedure(@RequestBody CraftProcedureEntity craftProcedureEntity, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        craftProcedureEntity.setCraftCode(craftService.getById(craftProcedureEntity.getCraftId()).getCraftCode());
        String username = getUsername();
        craftProcedureEntity.setCreateBy(username);
        craftProcedureEntity.setCreateTime(new Date());
        craftProcedureEntity.setUpdateBy(username);
        craftProcedureEntity.setUpdateTime(new Date());
        craftProcedureService.saveEntity(craftProcedureEntity, true);
        return success(craftProcedureEntity);
    }

    /**
     * 修改工艺路线的工序
     *
     * @param
     * @return
     */
    @PutMapping("/procedure/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了工艺编码为#{craftCode}-工序名称为#{procedureName}的工序")
    public ResponseData updateProcedure(@RequestBody CraftProcedureEntity craftProcedureEntity) {
        craftProcedureEntity.setUpdateTime(new Date());
        craftProcedureEntity.setUpdateBy(getUsername());
        craftProcedureEntity.setWhetherImport(false);
        craftProcedureEntity.setCraftCode(craftService.getById(craftProcedureEntity.getCraftId()).getCraftCode());
        craftProcedureService.updateEntityById(craftProcedureEntity);
        return success(craftProcedureEntity);
    }

    /**
     * 删除工艺路线的工序(同时删除关联的配置信息)
     *
     * @param
     * @return
     */
    @DeleteMapping("/procedure/delete/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了工艺编码为#{craftCode}-工序名称为#{procedureName}的工序")
    public ResponseData deleteProcedure(@PathVariable(value = "id") Integer id, @RequestParam(defaultValue = "false") Boolean autoFixNode) {
        CraftProcedureEntity entity = craftProcedureService.getById(id);
        entity.setCraftCode(craftService.getById(entity.getCraftId()).getCraftCode());
        craftProcedureService.removeEntityById(id, autoFixNode);
        return success(entity);
    }

    /**
     * 获取工艺路线的工序列表
     *
     * @param
     * @return
     */
    @GetMapping("/procedure/list/{craftId}")
    public ResponseData getProcedureList(@PathVariable(value = "craftId") Integer craftId) {
        List<CraftProcedureEntity> list = craftProcedureService.getProcedureList(craftId);
        return success(list);
    }

    /**
     * 添加工艺路线-工序绑定的工序用料
     *
     * @param
     * @return
     */
    @PostMapping("/procedure/material_used/add")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了工艺编码为#{craftCode}-工序名称为#{procedureName}-物料编码为#{materialCode}的工序用料")
    public ResponseData addProcedureMaterialUsed(@RequestBody ProcedureMaterialUsedEntity procedureMaterialUsedEntity) {
        // 此操作为了输出日志
        procedureMaterialUsedEntity.setCraftCode(craftService.getById(procedureMaterialUsedEntity.getCraftId()).getCraftCode());
        procedureMaterialUsedEntity.setProcedureName(craftProcedureService.getById(procedureMaterialUsedEntity.getProcedureId()).getProcedureName());
        procedureMaterialUsedService.save(procedureMaterialUsedEntity);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("用料 - 新增")
                .craftProcedureId(procedureMaterialUsedEntity.getProcedureId())
                .build()
        );
        return success(procedureMaterialUsedEntity);
    }

    /**
     * 修改工艺路线-工序绑定的工序用料
     *
     * @param
     * @return
     */
    @PutMapping("/procedure/material_used/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了工艺编码为#{craftCode}-工序名称为#{procedureName}-物料编码为#{materialCode}的工序用料")
    public ResponseData updateProcedureMaterialUsed(@RequestBody ProcedureMaterialUsedEntity procedureMaterialUsedEntity) {
        procedureMaterialUsedEntity.setCraftCode(craftService.getById(procedureMaterialUsedEntity.getCraftId()).getCraftCode());
        procedureMaterialUsedEntity.setProcedureName(craftProcedureService.getById(procedureMaterialUsedEntity.getProcedureId()).getProcedureName());
        procedureMaterialUsedService.updateById(procedureMaterialUsedEntity);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("用料 - 更新")
                .craftProcedureId(procedureMaterialUsedEntity.getProcedureId())
                .build()
        );
        return success(procedureMaterialUsedEntity);
    }

    /**
     * 删除工艺路线-工序绑定的工序用料
     *
     * @param
     * @return
     */
    @DeleteMapping("/procedure/material_used/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了工艺编码为#{craftCode}-工序名称为#{procedureName}-物料编码为#{materialCode}的工序用料")
    public ResponseData deleteProcedureMaterialUsed(@PathVariable(value = "id") Integer id) {
        ProcedureMaterialUsedEntity entity = procedureMaterialUsedService.getById(id);
        entity.setCraftCode(craftService.getById(entity.getCraftId()).getCraftCode());
        entity.setProcedureName(craftProcedureService.getById(entity.getProcedureId()).getProcedureName());
        procedureMaterialUsedService.removeById(id);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("用料 - 删除")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success(entity);
    }

    /**
     * 添加工艺路线-工序绑定的工序物料
     *
     * @param
     * @return
     */
    @PostMapping("/procedure/material/add")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了工艺编码为#{craftCode}-工序名称为#{procedureName}-物料编码为#{materialCode}的工序物料")
    public ResponseData addProcedureMaterial(@RequestBody ProcedureMaterialEntity procedureMaterialEntity) {
        // 此操作为了输出日志
        procedureMaterialEntity.setCraftCode(craftService.getById(procedureMaterialEntity.getCraftId()).getCraftCode());
        procedureMaterialEntity.setProcedureName(craftProcedureService.getById(procedureMaterialEntity.getProcedureId()).getProcedureName());
        procedureMaterialService.saveEntity(procedureMaterialEntity);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("物料 - 新增")
                .craftProcedureId(procedureMaterialEntity.getProcedureId())
                .build()
        );
        return success(procedureMaterialEntity);
    }

    /**
     * 修改工艺路线-工序绑定的工序物料
     *
     * @param
     * @return
     */
    @PutMapping("/procedure/material/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了工艺编码为#{craftCode}-工序名称为#{procedureName}-物料编码为#{materialCode}的工序物料")
    public ResponseData updateProcedureMaterial(@RequestBody ProcedureMaterialEntity procedureMaterialEntity) {
        procedureMaterialEntity.setCraftCode(craftService.getById(procedureMaterialEntity.getCraftId()).getCraftCode());
        procedureMaterialEntity.setProcedureName(craftProcedureService.getById(procedureMaterialEntity.getProcedureId()).getProcedureName());
        procedureMaterialService.updateById(procedureMaterialEntity);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("物料 - 更新")
                .craftProcedureId(procedureMaterialEntity.getProcedureId())
                .build()
        );
        return success(procedureMaterialEntity);
    }

    /**
     * 删除工艺路线-工序绑定的工序物料
     *
     * @param
     * @return
     */
    @DeleteMapping("/procedure/material/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了工艺编码为#{craftCode}-工序名称为#{procedureName}-物料编码为#{materialCode}的工序物料")
    public ResponseData deleteProcedureMaterial(@PathVariable(value = "id") Integer id) {
        ProcedureMaterialEntity entity = procedureMaterialService.getById(id);
        entity.setCraftCode(craftService.getById(entity.getCraftId()).getCraftCode());
        entity.setProcedureName(craftProcedureService.getById(entity.getProcedureId()).getProcedureName());
        procedureMaterialService.removeById(id);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("物料 - 删除")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success(entity);
    }

    /**
     * 新增工艺路线-工序设备类型
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @PostMapping("/procedure/device/type/add")
    @OperLog(module = "产品管理", type = OperationType.ADD, desc = "新增了工艺编码为#{craftCode}-工序id为#{procedureName}-名称为#{deviceTypeName}的设备类型")
    public ResponseData addProcedureDeviceType(@RequestBody ProcedureDeviceTypeEntity deviceTypeEntity) {
        if (procedureDeviceTypeService.addTargetReference(deviceTypeEntity)) {
            return success(deviceTypeEntity);
        } else {
            return fail();
        }
    }

    /**
     * 批量新增工艺路线-工序设备类型
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @PostMapping("/procedure/device/type/batch/add")
    public ResponseData addProcedureDeviceType(@RequestBody List<ProcedureDeviceTypeEntity> list) {
        procedureDeviceTypeService.addProcedureDeviceType(list);
        return success();
    }

    /**
     * 获取设备配方参数
     *
     * @param procedureDeviceTargetReferenceEntity
     * @return
     * <AUTHOR>
     */
    @PostMapping("/procedure/device/target/list")
    public ResponseData listProcedureDeviceTarget(@RequestBody ProcedureDeviceTargetReferenceEntity procedureDeviceTargetReferenceEntity) {
        LambdaQueryWrapper<ProcedureDeviceTargetReferenceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getCraftId, procedureDeviceTargetReferenceEntity.getCraftId());
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getProcedureId, procedureDeviceTargetReferenceEntity.getProcedureId());
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getDeviceModelId, procedureDeviceTargetReferenceEntity.getDeviceModelId());
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getNumber, procedureDeviceTargetReferenceEntity.getNumber());
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getFormulaNum, procedureDeviceTargetReferenceEntity.getFormulaNum());
        List<ProcedureDeviceTargetReferenceEntity> list = procedureDeviceTargetReferenceService.list(wrapper);
        return success(list);
    }

    /**
     * 保存/更新工艺工序--设备配方参数
     *
     * @param parameterConfigDTO
     * @return
     */
    @PostMapping("/procedure/device/target/save")
    public ResponseData addDeviceTargetReference(@RequestBody ProcessParameterConfigDTO parameterConfigDTO) {
        List<ProcedureDeviceTargetReferenceEntity> parameterConfigEntities = parameterConfigDTO.getParameterConfigEntities();
        for (ProcedureDeviceTargetReferenceEntity entity : parameterConfigEntities) {
            entity.setCraftId(parameterConfigDTO.getCraftId());
            entity.setDeviceModelId(parameterConfigDTO.getDeviceModelId());
            entity.setProcedureId(parameterConfigDTO.getProcedureId());
            entity.setCreateBy(getUsername());
            entity.setCreateTime(new Date());
            entity.setNumber(parameterConfigDTO.getNumber());
            entity.setFormulaNum(parameterConfigDTO.getFormulaNum());
            entity.setType("true");
        }
        procedureDeviceTargetReferenceService.saveOrUpdateBatch(parameterConfigEntities);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("设备参数配置 - 新增")
                .craftProcedureId(parameterConfigDTO.getProcedureId())
                .build()
        );
        return success();
    }

    /**
     * 修改设备配方参数
     *
     * @param procedureDeviceTargetReferenceEntityList
     * @return
     * <AUTHOR>
     */
    @PutMapping("/procedure/device/target/edit")
    public ResponseData editDeviceTargetReference(@RequestBody List<ProcedureDeviceTargetReferenceEntity> procedureDeviceTargetReferenceEntityList) {
        procedureDeviceTargetReferenceEntityList.forEach(procedureDeviceTargetReferenceEntity -> {
            procedureDeviceTargetReferenceEntity.setCreateBy(getUsername());
            procedureDeviceTargetReferenceEntity.setUpdateTime(new Date());
            procedureDeviceTargetReferenceService.updateById(procedureDeviceTargetReferenceEntity);
        });
        if(!CollectionUtils.isEmpty(procedureDeviceTargetReferenceEntityList)) {
            versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                    .description("设备参数配置 - 更新")
                    .craftProcedureId(procedureDeviceTargetReferenceEntityList.get(0).getProcedureId())
                    .build()
            );
        }
        return success();
    }

    /**
     * 删除设备配方参数
     *
     * @param id
     * @return
     */
    @DeleteMapping("/procedure/device/target/delete/{id}")
    public ResponseData deleteDeviceTargetReference(@PathVariable(value = "id") Integer id) {
        ProcedureDeviceTargetReferenceEntity reference = procedureDeviceTargetReferenceService.getById(id);
        procedureDeviceTargetReferenceService.removeById(id);
        if(reference != null) {
            versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                    .description("设备参数配置 - 删除")
                    .craftProcedureId(reference.getProcedureId())
                    .build()
            );
        }
        return success();
    }

    /**
     * 工艺工序-工序设备-配方参数 导入模板下载
     *
     * @param response
     */
    @GetMapping("/export/device/parameter/template")
    public void exportDeviceParameterTemplate(HttpServletResponse response) throws Exception {
        //找到导出的模板
        byte[] bytes = craftProcedureService.exportDeviceParameterTemplate();
        ExcelTemplateImportUtil.responseToClient(response, bytes, "设备配方参数默认模板" + Constant.XLSX);
    }


    /**
     * 工艺工序-工序设备-配方参数导入
     */
    @PostMapping("/excel/import/device/parameter")
    public ResponseData importDeviceParameter(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (!ExcelUtil.isExcelFile(filename)) {
            return fail(RespCodeEnum.PARSING_DATA_IS_EMPTY);
        }
        InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            log.error("IO异常", e);
            return fail(RespCodeEnum.IMPORT_FILE_FORMAT_WRONG);
        }
        //加锁
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(RedisKeyPrefix.CRAFT_IMPORT_DEVICE_PARAMETER_LOCK, new Date(), 1, TimeUnit.HOURS);
        if (lockStatus == null || !lockStatus) {
            log.info("获取redis同步锁失败");
            return fail(RespCodeEnum.IMPORT_OPREATION_NOT_RE);
        }
        //重置进度
        ImportProgressDTO build = ImportProgressDTO.builder().progress(Double.valueOf(Constant.ZERO_VALUE)).build();
        redisTemplate.opsForValue().set(RedisKeyPrefix.CRAFT_PROCEDURE_DEVICE_PARAMETER_IMPORT_PROGRESS, JSONObject.toJSONString(build), 1, TimeUnit.HOURS);
        craftProcedureService.importDeviceParameterExcel(filename, inputStream, getUsername());
        return success();
    }


    /**
     * 更新工艺路线-设备类型及对应的工序工时
     *
     * @param
     * @return
     */
    @PutMapping("/procedure/device/type/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了工艺编码#{craftCode}-工序id#{procedureName}-名称为#{deviceTypeName}的设备类型")
    public ResponseData updateProcedureDeviceType(@RequestBody ProcedureDeviceTypeEntity deviceTypeEntity) {
        deviceTypeEntity.setCraftCode(craftService.getById(deviceTypeEntity.getCraftId()).getCraftCode());
        deviceTypeEntity.setProcedureName(craftProcedureService.getById(deviceTypeEntity.getProcedureId()).getProcedureName());
        deviceTypeService.updateEntityById(deviceTypeEntity);
        return success(deviceTypeEntity);
    }

    /**
     * 删除工艺路线-设备类型及对应的工序工时
     *
     * @param
     * @return
     */
    @DeleteMapping("/procedure/device/type/delete/{id}")
    @OperLog(module = "产品管理", type = OperationType.DELETE, desc = "删除了工艺编码#{craftCode}-工序id#{procedureName}-名称为#{deviceTypeName}的设备类型")
    public ResponseData deleteProcedureDeviceType(@PathVariable(value = "id") Integer id) {
        ProcedureDeviceTypeEntity entity = deviceTypeService.getById(id);
        entity.setCraftCode(craftService.getById(entity.getCraftId()).getCraftCode());
        entity.setProcedureName(craftProcedureService.getById(entity.getProcedureId()).getProcedureName());
        deviceTypeService.deleteEntityById(entity);
        //删除参考值配置
        LambdaQueryWrapper<ProcedureDeviceTargetReferenceEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getCraftId, entity.getCraftId());
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getDeviceModelId, entity.getDeviceModelId());
        wrapper.eq(ProcedureDeviceTargetReferenceEntity::getProcedureId, entity.getProcedureId());
        procedureDeviceTargetReferenceService.remove(wrapper);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("设备 - 删除")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success(entity);
    }

    /**
     * 获取工序配置详细信息
     *
     * @param
     * @return
     */
    @GetMapping("/procedure/config")
    public ResponseData getProcedureConfig(@RequestParam(value = "id") Integer id) {
        CraftProcedureEntity procedureEntity = craftProcedureService.getProcedureConfig(id);
        return success(procedureEntity);
    }

    /**
     * 获取工艺路线图
     *
     * @param
     * @return
     */
    @GetMapping("/route/{craftId}")
    public ResponseData getCraftRoute(@PathVariable(value = "craftId") Integer craftId) {
        Collection<CraftRouteVO> craftRouteData = craftProcedureService.getCraftRoute(craftId);
        List<CraftProcedureEntity> craftRouteTree = craftProcedureService.getCraftTree(craftId);
        CraftRouteDTO dto = CraftRouteDTO.builder().data(craftRouteData).tree(craftRouteTree).build();
        return success(dto);
    }

    /**
     * 比较前后两个工艺路线是否一致20220907
     *
     * @param
     * @return
     */
    @GetMapping("/compare/{oldCraftId}/{newCraftId}")
    public ResponseData compareCraftRoute(@PathVariable(value = "oldCraftId") Integer oldCraftId, @PathVariable(value = "newCraftId") Integer newCraftId) {
        CraftRouteDTO dto = craftProcedureService.compareCraftRoute(oldCraftId, newCraftId);
        return success(dto);
    }

    /**
     * 通过工艺id梳理没有直接后继的工序，全部查入直接后继20220909
     *
     * @param craftId
     * @return
     */
    @GetMapping("/sortNextProcedure/{craftId}")
    public ResponseData sortNextProcedure(@PathVariable(value = "craftId") Integer craftId) {
        craftProcedureService.sortNextProcedure(craftId);
        return success("梳理完成");
    }

    /**
     * 工艺路线导入(导入excel)
     *
     * @param file
     * @return
     */
    @PostMapping("/import/{craftId}")
    public ResponseData craftRouteImport(@PathVariable Integer craftId, MultipartFile file) {
        try {
            craftProcedureService.importFile(craftId, file);
        } catch (IOException e) {
            return fail(e.getMessage());
        }
        return success();
    }

    /**
     * 审批
     */
    @GetMapping("/examine/approve")
    public ResponseData examineOrApprove(@RequestParam(value = "approvalStatus") Integer approvalStatus,
                                         @RequestParam(value = "approvalSuggestion", required = false) String approvalSuggestion,
                                         @RequestParam(value = "id") Integer id) {
        String username = getUsername();
        craftService.examineOrApprove(approvalStatus, approvalSuggestion, id, username);
        return success();
    }

    /**
     * 批量审批
     *
     * @return
     */
    @PostMapping("/approve/batch")
    public ResponseData approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(getUsername());
        craftService.approveBatch(dto);
        return success();
    }

    /**
     * 工艺批量编辑
     */
    @PostMapping("/batch/update/state")
    public ResponseData batchUpdate(@RequestBody BatchChangeStateDTO batchApprovalDTO) {
        if (batchApprovalDTO.getIds() == null) {
            return fail("编辑的单据id为空");
        }
        String username = getUsername();
        Boolean ret = craftService.batchUpdateState(batchApprovalDTO, username);
        return ResponseData.success(ret);
    }

    /**
     * 查询工艺文件(APP端工位作业下)支持模糊查询
     *
     * @param fid             工位id
     * @param workOrderNumber 工单号
     * @param name            文件名称(模糊查询)
     * @return
     */
    @GetMapping("/select/craft/file")
    public ResponseData selectCraftFile(@PathParam(value = "fid") Integer fid,
                                        @PathParam(value = "workOrderNumber") String workOrderNumber,
                                        @PathParam(value = "name") String name) {
        List<CraftFileDTO> craftFileDTOList = craftProcedureService.selectCraftFiles(fid, workOrderNumber, name);
        return success(craftFileDTOList);
    }


    /**
     * 查询工艺文件(APP端工位作业下)支持模糊查询[新]
     *
     * @param fid             工位id
     * @param workOrderNumber 工单号
     * @param name            文件名称(模糊查询)
     * @return
     */
    @GetMapping("/select/craft/all/files")
    public ResponseData selectallCraftFiles(@PathParam(value = "fid") Integer fid,
                                            @PathParam(value = "workOrderNumber") String workOrderNumber,
                                            @PathParam(value = "name") String name) {
        List<CraftFileNewDTO> craftFileDTOList = craftProcedureService.selectAllCraftFiles(fid, workOrderNumber, name);
        return success(craftFileDTOList);
    }


    /**
     * 导出工艺路线模板
     *
     * @param
     * @return
     */
    @GetMapping("/export")
    public void craftImportExport(HttpServletResponse response) throws IOException {
        byte[] bytes = craftProcedureService.download();
        String fileName = URLEncoder.encode("工艺路线模板", StandardCharsets.UTF_8.name());
        response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + Constant.XLSX + "\"; filename*=utf-8'zh_cn'" + fileName + Constant.XLSX);
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(bytes);
        outputStream.flush();
        outputStream.close();
    }


    /**
     * 工艺导入:
     * 工艺导入默认模板下载
     */
    @GetMapping("/export/default/template")
    public void downloadCraftDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工艺默认模板" + Constant.XLSX);
    }
    /**
     * 工艺导入:
     * 导入工艺模板
     */
    @PostMapping("/import/template")
    public ResponseData importTemplateExcel(MultipartFile file) {
        craftProcedureService.importTemplateExcel(file);
        return success();
    }
    /**
     * 工艺导入:
     * 用户导入的工艺模板下载
     */
    @GetMapping("/export/template")
    public void exportSaleOrderTemplateExcel(HttpServletResponse response) throws Exception {
        //找到导出的模板
        InputStream inputStream = craftProcedureService.exportCraftTemplate();
        ExcelTemplateImportUtil.responseToClient(response, inputStream, "工艺导入模板" + Constant.XLSX);
    }
    /**
     * 工艺导入:
     * 导入工艺excel
     */
    @PostMapping("/excel/import")
    public ResponseData importExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importExcel(file.getInputStream(), getUsername(), file.getOriginalFilename());
        return success();
    }
    /**
     * 工艺导入:
     * 获取工艺导入的进度
     */
    @GetMapping("/import/progress")
    public ResponseData getMaterialCompletenessProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_IMPORT_PROGRESS));
    }

    /**
     * 工艺工序用料导入：默认模板下载
     */
    @GetMapping("/export/procedure/material_used/default/template")
    public void downloadMaterialUsedDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureMaterialUsedTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序用料默认导入模板" + Constant.XLSX);
    }
    /**
     * 工艺工序用料导入：导入
     */
    @PostMapping("/excel/import/material_used")
    public ResponseData importMaterialUsedExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importMaterialUsedExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工艺工序用料导入：导入进度
     */
    @GetMapping("/import/material_used/progress")
    public ResponseData getImportMaterialUsedExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_MATERIAL_USED_IMPORT_PROGRESS));
    }

    /**
     * 工艺工序物料导入：默认模板下载
     */
    @GetMapping("/export/procedure/material/default/template")
    public void downloadMaterialDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureMaterialTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序物料默认导入模板" + Constant.XLSX);
    }
    /**
     * 工艺工序物料导入：导入
     */
    @PostMapping("/excel/import/material")
    public ResponseData importMaterialExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importMaterialExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工艺工序物料导入：导入进度
     */
    @GetMapping("/import/material/progress")
    public ResponseData getImportMaterialExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_MATERIAL_IMPORT_PROGRESS));
    }

    /**
     * 工艺工序检验导入：默认模板下载
     */
    @GetMapping("/export/procedure/inspect/default/template")
    public void downloadInspectDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureInspectTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序检验默认导入模板" + Constant.XLSX);
    }
    /**
     * 工艺工序检验导入：导入
     */
    @PostMapping("/excel/import/inspect")
    public ResponseData importInspectExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importInspectExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工艺工序检验导入：导入进度
     */
    @GetMapping("/import/inspect/progress")
    public ResponseData getImportInspectExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_INSPECT_IMPORT_PROGRESS));
    }

    /**
     * 工艺工序检验方案控制导入：默认模板下载
     */
    @GetMapping("/export/procedure/inspect_controller/default/template")
    public void downloadInspectControllerDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureInspectControllerTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序检验方案控制默认导入模板" + Constant.XLSX);
    }
    /**
     * 工艺工序检验方案控制导入
     */
    @PostMapping("/excel/import/inspect_controller")
    public ResponseData importInspectControllerExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importInspectControllerExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工艺工序检验方案控制导入：导入进度
     */
    @GetMapping("/import/inspect_controller/progress")
    public ResponseData getImportInspectControllerExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_INSPECT_CONTROLLER_IMPORT_PROGRESS));
    }

    /**
     * 工艺工序控制导入：默认模板下载
     */
    @GetMapping("/export/procedure/control/default/template")
    public void downloadControllerDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureControllerTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序控制默认导入模板" + Constant.XLSX);
    }
    /**
     * 工艺工序控制导入：导入
     */
    @PostMapping("/excel/import/control")
    public ResponseData importControlExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importControlExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工艺工序控制导入：获取导入进度
     */
    @GetMapping("/import/control/progress")
    public ResponseData getImportControlExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_CONTROL_IMPORT_PROGRESS));
    }


    /**
     * 工艺工序附件导入：默认导入模板下载
     */
    @GetMapping("/export/procedure/file/default/template")
    public void downloadFileDefaultTemplate(HttpServletResponse response) throws IOException {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource resource = resolver.getResource("classpath:template/craftProcedureFileTemplate.zip");
        ExcelTemplateImportUtil.responseToClient(response, resource.getInputStream(), "工序附件默认导入模板" + Constant.ZIP);
    }
    /**
     * 工艺工序附件导入：导入
     */
    @PostMapping("/excel/import/file")
    public ResponseData importFileExcel(MultipartFile file) {
        // 校验压缩文件
        ZipFileDTO dto = ZipAndRarUtil.checkFile(file, Constant.CRAFT_PROCEDURE_FILE_EXCEL_NAME);
        craftProcedureService.importFileExcel(file.getOriginalFilename(), dto.getUnzip(), dto.getExcelFile(), getUsername());
        return success();
    }
    /**
     * 工艺工序附件导入：导入进度
     */
    @GetMapping("/import/file/progress")
    public ResponseData getImportFileExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_FILE_IMPORT_PROGRESS));
    }


    /**
     * 工艺工序工时导入：默认导入模板下载
     */
    @GetMapping("/export/procedure/work_hour/default/template")
    public void downloadWorkHourDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureWorkHourTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序工时默认导入模板" + Constant.XLSX);
    }
    /**
     * 工艺工序工时导入：导入
     */
    @PostMapping("/excel/import/work_hour")
    public ResponseData importWorkHourExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importWorkHourExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工艺工序工时导入：获取导入进度
     */
    @GetMapping("/import/work_hour/progress")
    public ResponseData getImportWorkHourExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_WORK_HOUR_IMPORT_PROGRESS));
    }

    /**
     * 工艺工序的工艺参数导入：默认导入模板下载
     */
    @GetMapping("/export/procedure/parameter/default/template")
    public void downloadParameterDefaultTemplate(HttpServletResponse response) throws IOException {
        //找到导出的模板
        byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureParameterTemplate.xlsx");
        ExcelTemplateImportUtil.responseToClient(response, bytes, "工序参数默认导入模板" + Constant.XLSX);
    }
    /**
     * 工艺工序的工艺参数导入：导入
     */
    @PostMapping("/excel/import/parameter")
    public ResponseData importParameterExcel(MultipartFile file) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importParameterExcel(file.getOriginalFilename(), file.getInputStream(), getUsername());
        return success();
    }
    /**
     * 工艺工序的工艺参数导入：获取导入进度
     */
    @GetMapping("/import/parameter/progress")
    public ResponseData getImportParameterExcelProgress() {
        return success(importProgressService.importProgress(RedisKeyPrefix.CRAFT_PROCEDURE_PARAMETER_IMPORT_PROGRESS));
    }


    /**
     * 获取关联产线类型的工序列表
     *
     * @param
     * @return
     */
    @GetMapping("/procedure/list")
    public ResponseData getCraftProcedureListByLineModelId(@RequestParam(value = "modelId") Integer modelId) {
        return success(craftProcedureService.getCraftProcedureListByLineModelId(modelId));
    }


    /**
     * 获取工艺工序控制配置表
     *
     * @param craftProcedureId 工艺工序id
     * @return
     */
    @GetMapping("/procedure/controller/config/get/{craftProcedureId}")
    public ResponseData getControllerConfig(@PathVariable(value = "craftProcedureId") Integer craftProcedureId) {
        return success(procedureControllerConfigService.getDetail(craftProcedureId));
    }


    /**
     * 修改工艺工序控制配置
     *
     * @param procedureControllerConfigEntity
     * @return
     */
    @PutMapping("/procedure/controller/config/edit")
    public ResponseData getControllerConfig(@RequestBody ProcedureControllerConfigEntity procedureControllerConfigEntity) {
        Assert.notNull(procedureControllerConfigEntity.getCraftProcedureId(), "未指定具体工序");
        String userName = getUsername();
        procedureControllerConfigEntity.setUpdateBy(userName);
        procedureControllerConfigEntity.setUpdateTime(new Date());
        procedureControllerConfigService.edit(procedureControllerConfigEntity);
        // 记录版本变更
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("控制 - 更新")
                .craftProcedureId(procedureControllerConfigEntity.getCraftProcedureId())
                .build()
        );
        return success();
    }

    /**
     * 获取工位下工位类型对应的物料工艺工序列表
     *
     * @param
     * @return
     */
    @PostMapping("/procedure/list/by/fid")
    public ResponseData getCraftProcedureListByFid(@RequestParam(value = "fid") Integer fid, @RequestBody ScannerDTO productFlowCodeEntity) {
        return success(craftProcedureService.getCraftProcedureListByFid(fid, productFlowCodeEntity.getProductFlowCode(), productFlowCodeEntity.getRelationNumber(), productFlowCodeEntity.getType()));
    }

    /**
     * 获取工位下工单可选的工艺工序列表
     *
     * @param
     * @return
     */
    @PostMapping("/procedure/list/by/fid/work/order")
    public ResponseData getCraftProcedureListByFidAndWorkOrder(@RequestParam(value = "fid") Integer fid, @RequestBody ProductFlowCodeEntity productFlowCodeEntity) {
        return success(craftProcedureService.getCraftProcedureListByFidAndWorkOrder(fid, productFlowCodeEntity.getRelationNumber()));
    }

    /**
     * 获取工序控制下 流转/生产超时阈值类型 (百分比，固定值)
     *
     * @return
     */
    @GetMapping("/get/type")
    public ResponseData getTimeoutThresholdTypeList() {
        CommonEnum.CommonEnumBuilder builder = CommonEnum.builder();
        List<CommonEnum> collect = Arrays.stream(TimeoutThresholdTypeEnum.values())
                .map(o -> builder.code(o.getCode()).name(o.getName()).build())
                .collect(Collectors.toList());
        return success(collect);
    }

    /**
     * 工序流转条件类型列表
     *
     * @return
     */
    @GetMapping("/procedure/flow/type/list")
    public ResponseData procedureFlowTypeList() {
        List<ProcedureFlowTypeDTO> list = Arrays.stream(ProcedureFlowTypeEnum.values()).map(
                e -> ProcedureFlowTypeDTO.builder().code(e.getCode()).name(e.getName()).unit(e.getUnit()).build()
        ).collect(Collectors.toList());
        return ResponseData.success(list);
    }

    /**
     * 根据物料查询工艺列表
     *
     * @param materialCode 物料编码
     * @param businessType 创建生产工单时工单的业务类型
     * @return
     */
    @GetMapping("/list/by/code/{materialCode}")
    public ResponseData getListByMaterialCode(@PathVariable(value = "materialCode") String materialCode,
                                              @RequestParam(value = "businessType", required = false) String businessType) {
        List<CraftEntity> craftEntityList = craftService.getListByMaterialCode(materialCode, businessType);
        return success(craftEntityList);
    }

    @GetMapping("/list/by/code")
    public ResponseData listByCode(@RequestParam(value = "materialCode") String materialCode,
                                   @RequestParam(value = "businessType", required = false) String businessType) {
        List<CraftEntity> craftEntityList = craftService.listByCode(materialCode, businessType);
        return success(craftEntityList);
    }

    @GetMapping("/list/by/code2")
    public ResponseData listByCode2(@RequestParam(required = false) String materialCode,
                                    @RequestParam(required = false) Boolean isTemplate) {
        List<CraftEntity> craftEntityList = craftService.listByCode2(materialCode, isTemplate);
        return success(craftEntityList);
    }


    /**
     * 更新工序工时(标准调试工时等3个)
     *
     * @param
     * @return
     */
    @PutMapping("/procedure/work/hours/update")
    @OperLog(module = "产品管理", type = OperationType.UPDATE, desc = "修改了工艺编码#{craftCode}-工序id#{procedureName}-名称为#{deviceTypeName}的设备类型")
    public ResponseData updateProcedureWorkHours(@RequestBody ProcedureRelationWorkHoursEntity entity) {
        ProcedureRelationWorkHoursEntity procedureRelationWorkHoursEntity = procedureRelationWorkHoursService.updateEntityById(entity);
        versionChangeRecordService.changeVersionCraftProcedure(VersionChangeCraftProcedureDTO.builder()
                .description("工时 - 更新")
                .craftProcedureId(entity.getProcedureId())
                .build()
        );
        return success(procedureRelationWorkHoursEntity);
    }

    /**
     * 复制工艺配置
     *
     * @param craftCopyDTO
     * @return
     */
    @PostMapping("/copyCraft")
    public ResponseData copyCraft(@RequestBody @Valid CraftCopyDTO craftCopyDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        String username = getUsername();
        Integer integer = craftService.copyCraft(craftCopyDTO, username);
        return success(integer);
    }

    /**
     * 工艺工序导入：下载工艺工序默认导入模板
     * 邓尹杰2.0.1需求
     */
    @GetMapping("/export/craft/procedure/template")
    public void exportExcelTemplate(@RequestParam(required = false) Boolean isTemplate, HttpServletResponse response) throws IOException {
        if (Objects.nonNull(isTemplate) && isTemplate) {
            // 工艺模板导入文件存在差异，工艺模板没有工序用料
            byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureTeamplateTemplate.xlsx");
            ExcelTemplateImportUtil.responseToClient(response, bytes, "工艺模板默认导入模板" + Constant.XLSX);
        } else {
            byte[] bytes = ExcelUtil.exportDefaultExcel("classpath:template/craftProcedureTemplate.xlsx");
            ExcelTemplateImportUtil.responseToClient(response, bytes, "工艺默认导入模板" + Constant.XLSX);
        }
    }
    /**
     * 工艺工序导入：导入
     * 邓尹杰2.0.1需求
     */
    @PostMapping("/import/craft/procedure")
    public ResponseData importCraftProcedureExcel(MultipartFile file, @RequestParam(required = false) Boolean isTemplate) throws IOException {
        //1、判断导入文件的合法性
        ExcelUtil.checkImportExcelFile(file);
        craftProcedureService.importCraftProcedureExcel(file.getOriginalFilename(), file.getInputStream(), getUsername(), isTemplate);
        return success();
    }
    /**
     * 工艺工序导入：获取导入进度
     */
    @GetMapping("/import/craft/procedure/progress")
    public ResponseData getCraftProcedureImportProgress(@RequestParam(required = false) Boolean isTemplate) {
        String importProgressKey = RedisKeyPrefix.getCraftOrTemplateKey(RedisKeyPrefix.CRAFT_PROCEDURE_IMPORT_PROGRESS, isTemplate);
        return success(importProgressService.importProgress(importProgressKey));
    }

    /**
     * 影响分析 -- 获取工艺关联的
     * 生产订单(生效)、
     * 生产工单(生效、投产、挂起、完成)、
     * 作业工单（生效、投产、挂起、完成）
     *
     * @param
     * @return
     */
    @PostMapping("/impact/analysis")
    public ResponseData impactAnalysis(@RequestBody ImpactAnalysisSelectDTO selectDTO) {
        return success(craftService.impactAnalysis(selectDTO));
    }

    /**
     * 影响分析 -- 导出
     *
     * @param
     * @return
     */
    @PostMapping("/impact/analysis/export")
    public ResponseData impactAnalysisExport(@RequestBody ImpactAnalysisSelectDTO selectDTO,
                                             HttpServletResponse response) throws IOException {
        craftService.impactAnalysisExport(selectDTO, response);
        return success();
    }

    /**
     * 获取所属产品的工艺列表
     */
    @GetMapping("/template/list")
    public ResponseData templateList(@RequestParam(required = false) String value) {
        List<CraftEntity> list = craftService.lambdaQuery()
                .eq(CraftEntity::getIsTemplate, Constants.TRUE)
                .eq(CraftEntity::getState, CraftStateEnum.RELEASED.getCode())
                .and(StringUtils.isNotBlank(value), wp -> wp.like(CraftEntity::getCraftCode, value).or().like(CraftEntity::getName, value))
                .list();
        return success(list);
    }

    /**
     * 获取所属产品的工艺列表
     */
    @GetMapping("/procedures/group/by/work/center")
    public ResponseData getProceduresGroupByWorkCenter(@RequestParam(value = "craftCode", required = false) String craftCode) {
        List<CraftProcedureWorkCenterGroupDTO> groups = craftProcedureService.getProceduresGroupByWorkCenter(craftCode);
        return success(groups);
    }

    /**
     * 删除工艺路线的工序的校验
     *
     * @param craftProcedureId 工艺工序id
     * @return true--影响  false--不影响
     */
    @GetMapping("/procedures/delete/check")
    public ResponseData checkCraftProcedureBeforeDelete(@RequestParam(value = "craftProcedureId") Integer craftProcedureId) {
        return success(craftProcedureService.checkCraftProcedureBeforeDelete(craftProcedureId));
    }

    /**
     * 工艺基本信息列表导出 - 下载默认模板
     * 查询10条工艺基本信息数据导出数据源excel
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("/simple/default/export/template")
    public void simpleListDefaultExportTemplate(@RequestParam(required = false) Boolean isTemplate, HttpServletResponse response) throws IOException {
        // 获取最新的十条数据源
        CraftSelectDTO selectDTO = new CraftSelectDTO();
        selectDTO.setCurrent(1);
        selectDTO.setSize(10);
        selectDTO.setIsTemplate(isTemplate);
        Page<CraftEntity> page = craftService.getList(selectDTO);
        if (Objects.nonNull(isTemplate) && isTemplate) {
            List<SimpleCraftExportDTO> list = craftService.convertToSimpleCraftExportDTO(page.getRecords());
            List<SimpleCraftTempExportDTO> exportDTOS = JacksonUtil.convertArray(list, SimpleCraftTempExportDTO.class);
            EasyExcelUtil.export(response, "默认工艺基本信息默认导出模板", "数据源", exportDTOS, SimpleCraftTempExportDTO.class);
        } else {
            List<SimpleCraftExportDTO> list = craftService.convertToSimpleCraftExportDTO(page.getRecords());
            EasyExcelUtil.export(response, "工艺基本信息默认导出模板", "数据源", list, SimpleCraftExportDTO.class);
        }
    }

    /**
     * 工艺基本信息列表导出 - 自定义模板上传
     * 接收文件并清空名称为数据源sheet的内容
     *
     * @param file
     * @return
     */
    @PostMapping("/simple/upload/list/export/template")
    public ResponseData uploadSimpleListExportTemplate(@RequestParam(required = false) Boolean isTemplate, MultipartFile file) throws IOException {
        craftService.uploadSimpleListExportTemplate(isTemplate, file, getUsername());
        return success();
    }

    /**
     * 工艺基本信息列表导出 - 工艺基本信息自定义模板下载
     * 分页查询并填写数据源sheet的内容
     *
     * @param response
     * @return
     * @throws IOException
     */
    @PostMapping("/simple/download/list/export/template")
    public ResponseData downloadSimpleListExportTemplate(HttpServletResponse response, Integer id) throws IOException {
        ModelUploadFileEntity uploadFile = modelUploadFileService.getById(id);
        if (Objects.isNull(uploadFile)) {
            throw new ResponseException(RespCodeEnum.TEMPLATE_NOT_EXIST);
        }
        byte[] bytes = fastDfsClientService.getFileStream(uploadFile.getFileAddress());
        ExcelTemplateImportUtil.responseToClient(response, bytes, uploadFile.getFileName());
        return success();
    }

    /**
     * 工艺基本信息导出  :异步
     *
     * @param selectDTO
     * @return
     * @throws IOException
     */
    @PostMapping("/simple/syc/exports")
    public ResponseData simpleListExportTask(@RequestBody CraftSelectDTO selectDTO) {
        Long result = craftService.simpleListExportTask(selectDTO, getUsername());
        return success(result);
    }

    /**
     * 分页查询当前导出任务列表
     *
     * @param pageSize
     * @param currentPage
     * @return
     */
    @GetMapping("/simple/export/task/page")
    public ResponseData taskSimplePage(@RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                       @RequestParam(required = false, defaultValue = "1") Integer currentPage,
                                       @RequestParam(required = false) Boolean isTemplate) {
        IPage<ExcelTask> iPage = craftService.simpleTaskPage(currentPage, pageSize, isTemplate);
        return success(iPage);
    }

    /**
     * 查询 工艺基本信息导入进度
     *
     * @return
     */
    @GetMapping("/simple/export/task/{taskId}")
    public ResponseData simpleListTaskById(@PathVariable Long taskId,
                                           @RequestParam(required = false) Boolean isTemplate) {
        ExcelTask excelTask = craftService.simpleListTaskById(taskId, isTemplate);
        return success(excelTask);
    }

    /**
     * 获取工艺类型
     *
     * @return
     */
    @GetMapping("/type")
    public ResponseData getCraftType() {
        List<CommonType> list = new ArrayList<>();
        CraftTypeEnum[] values = CraftTypeEnum.values();
        for (CraftTypeEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

    /**
     * 工艺工序--更新检验方案控制配置
     *
     * @param
     * @return
     */
    @PutMapping("/procedure/inspect_controller/batch/update")
    public ResponseData updateCraftProcedureInspectController(@RequestBody @Validated List<CraftProcedureInspectControllerEntity> list) {
        craftProcedureInspectControllerService.updateCraftProcedureInspectController(list);
        return success();
    }


    /**
     * 工艺工序--获取检验方案控制配置详情
     *
     * @param craftProcedureId 工艺工序id
     * @return
     */
    @GetMapping("/procedure/inspect_controller/detail/{craftProcedureId}")
    public ResponseData getCraftProcedureInspectControllerById(@PathVariable(value = "craftProcedureId") Integer craftProcedureId) {
        return success(craftProcedureInspectControllerService.getCraftProcedureInspectControllerById(craftProcedureId));
    }

    @GetMapping("/delete/check")
    public ResponseData deleteCheck(@RequestParam(value = "craftId") Integer craftId) {
        return success(craftService.deleteCheck(craftId));
    }

    @GetMapping("/delete/check/progress")
    public ResponseData deleteCheckProgress(@RequestParam(value = "key") String key) {
        return success(craftService.deleteCheckProgress(key));
    }

    /**
     * 工艺详情预览（含工艺工序信息）
     *
     * @return
     */
    @GetMapping("/detail/preview/{id}")
    public ResponseData detailPreview(@PathVariable(value = "id") Integer id) {
        return success(craftService.detailPreview(id));
    }

    /**
     * 通过工艺工序的工序物料列表
     *
     * @return
     */
    @GetMapping("/procedure_material/list")
    public ResponseData getProcedureMaterialByMaterialCode(@RequestParam(value = "materialCode", required = false) String materialCode,
                                                           @RequestParam(required = false) Integer skuId,
                                                           @RequestParam(required = false, defaultValue = "massProduction") String productionState) {
        return ResponseData.success(craftService.getProcedureMaterialByMaterialCode(materialCode, skuId, productionState));
    }


    /**
     * 通过物料查询工艺工序列表
     *
     * @return
     */
    @GetMapping("/get/crafts")
    public ResponseData getCrafts(@RequestParam(value = "materialCode") String materialCode) {
        return ResponseData.success(craftService.getCrafts(materialCode));
    }


    /**
     * 通过工艺ID和生产订单计划类型返回工序列表
     *
     * @return
     */
    @GetMapping("/get/craft/procedure/id")
    public ResponseData getCraftProcedureByCraftId(@RequestParam(value = "craftId") Integer craftId) {
        return ResponseData.success(craftService.getCraftProcedureByCraftId(craftId));
    }

    /**
     * 获取工艺关联的工艺资源列表（即工序定义关联的能力标签列表）
     *
     * @return
     */
    @GetMapping("/capacity_label/list")
    public ResponseData getCapacityLabelList(@RequestParam(value = "craftId") Integer craftId) {
        return ResponseData.success(craftService.getCapacityLabelList(craftId));
    }

    /**
     * 查询工艺模板绑定的物料列表
     *
     * @return
     */
    @GetMapping("/material/list")
    public ResponseData getMaterialList(@RequestParam(value = "craftCode") String craftCode) {
        return ResponseData.success(craftService.getMaterialList(craftCode));
    }

    /**
     * 工艺模板绑定物料
     *
     * @return
     */
    @PostMapping("/bind/material")
    public ResponseData bindMaterial(@RequestBody CraftBindMaterialDTO dto) {
        craftService.bindMaterial(dto);
        return ResponseData.success();
    }

    /**
     * 是否是最后一道非委外工序
     *
     * @return
     */
    @GetMapping("/last/no_sub_contract/procedure")
    public ResponseData isLastNoSubContractProcedure(@RequestParam Integer craftProcedureId) {
        return ResponseData.success(craftProcedureService.isLastNoSubContractProcedure(craftProcedureId));
    }

    @GetMapping("/relation/state")
    public ResponseData getBomState() {
        List<CommonType> list = new ArrayList<>();
        CraftRelatedConditionEnum[] values = CraftRelatedConditionEnum.values();
        for (CraftRelatedConditionEnum value : values) {
            CommonType type = new CommonType();
            type.setCode(value.getCode());
            type.setName(value.getName());
            list.add(type);
        }
        return success(list);
    }

}
