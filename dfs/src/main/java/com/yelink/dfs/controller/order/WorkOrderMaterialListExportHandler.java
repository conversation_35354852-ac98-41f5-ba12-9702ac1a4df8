package com.yelink.dfs.controller.order;

import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.asyncexcel.core.exporter.ExportHandler;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.order.dto.WorkOrderMaterialListExportDTO;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.order.WorkOrderMaterialListMaterialService;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfscommon.common.unit.UnitUtil;
import com.yelink.dfscommon.constant.ExcelExportFormEnum;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.entity.ams.dto.MaterialListSelectDTO;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListMaterialEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 生产用料清单列表数据导出
 */
@Slf4j
@ExcelHandle
public class WorkOrderMaterialListExportHandler implements ExportHandler<WorkOrderMaterialListExportDTO> {
    @Resource
    private WorkOrderMaterialListService workOrderMaterialListService;
    @Resource
    private WorkOrderMaterialListMaterialService workOrderMaterialListMaterialService;
    @Resource
    private CommonService commonService;

    /**
     * 导出数据最大不能超过100万行
     */
    public static final long EXPORT_MAX_ROWS = 1000000;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        String cleanSheetNames = (String) param.getParameters().get("cleanSheetNames");
        Integer templateId = (Integer) param.getParameters().get("templateId");
        String templateSheetName = (String) param.getParameters().get("templateSheetName");
        commonService.initExcelContext(templateId, templateSheetName, cleanSheetNames, context, WorkOrderMaterialListExportDTO.class, ExcelExportFormEnum.WORK_ORDER_MATERIAL_LIST.getFullPathCode());
    }


    @Override
    public ExportPage<WorkOrderMaterialListExportDTO> exportData(int startPage, int limit, DataExportParam param) {
        // 需要兼容列表导出、报表管理的导出
        MaterialListSelectDTO selectDTO = (MaterialListSelectDTO) param.getParameters().get(MaterialListSelectDTO.class.getName());
        selectDTO.setCurrent(startPage);
        selectDTO.setSize(limit);
        // 根据用户勾选的具体数据进行导出，如果是按单导出，则需要通过id查询关联的物料id再进行导出
        if (ShowTypeEnum.ORDER.getType().equals(selectDTO.getShowType()) && !CollectionUtils.isEmpty(selectDTO.getMaterialListId())) {
            List<Integer> orderMaterialRowIds = workOrderMaterialListMaterialService.lambdaQuery()
                    .in(WorkOrderMaterialListMaterialEntity::getMaterialListId, selectDTO.getMaterialListId())
                    .list().stream()
                    .map(WorkOrderMaterialListMaterialEntity::getId)
                    .collect(Collectors.toList());
            selectDTO.setRelatedMaterialIds(orderMaterialRowIds);
        }
        // 固定展示格式都为按物料展示
        selectDTO.setShowType(ShowTypeEnum.MATERIAL.getType());
        Page<WorkOrderMaterialListEntity> page = workOrderMaterialListService.getList(selectDTO);
        UnitUtil.formatObj(page);
        List<WorkOrderMaterialListExportDTO> exports = WorkOrderMaterialListExportDTO.convertToExportDTO(page.getRecords());
        ExportPage<WorkOrderMaterialListExportDTO> result = new ExportPage<>();
        result.setTotal(Math.min(page.getTotal(), EXPORT_MAX_ROWS));
        result.setCurrent((long) startPage);
        result.setSize((long) limit);
        result.setRecords(exports);
        return result;
    }


    @Override
    public void beforePerPage(ExportContext ctx, DataExportParam param) {
        log.info("开始查询第{}页", (long) Math.ceil(ctx.getSuccessCount() + ctx.getFailCount()) / ctx.getLimit());
    }

    @Override
    public void afterPerPage(List<WorkOrderMaterialListExportDTO> list, ExportContext ctx, DataExportParam param) {
        log.info("已完成数量：{}", ctx.getSuccessCount());
    }

    @Override
    public void callBack(ExcelContext ctx, DataParam param) {
        ExportContext context = (ExportContext) ctx;
        log.info("导出完成：{}", context.getFailMessage());
    }
}
