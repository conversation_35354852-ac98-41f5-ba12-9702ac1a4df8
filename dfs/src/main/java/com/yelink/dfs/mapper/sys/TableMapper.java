package com.yelink.dfs.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.entity.common.TableEntity;
import com.yelink.dfs.entity.sys.CleanableTableVo;
import com.yelink.dfs.entity.sys.dto.TableCreateOrUpdateDTO;
import com.yelink.dfs.entity.sys.dto.TableFieldSelectDTO;
import com.yelink.dfs.entity.sys.vo.TableColumnVO;
import com.yelink.dfscommon.dto.CommonTableDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * TableMapper
 *
 * <AUTHOR>
 * @Date 2021-4-25
 */
@Repository
public interface TableMapper extends BaseMapper<TableEntity> {
    /**
     * 分页查询dfs数据库可删除的表信息
     *
     * @param page         分页
     * @param tableName    表名
     * @param tableComment 表中文名
     * @param tableSchema  数据库名
     * @return List<CommonTableDTO>
     */
    Page<CommonTableDTO> getDfsTableList(Page page, String tableName, String tableComment, String tableSchema);

    /**
     * 获取dfs数据库可删除的表信息
     *
     * @return List<CommonTableDTO>
     */
    List<CommonTableDTO> getAllDfsTable(String tableSchema);

    /**
     * 清理数据
     * 相关数据表：dfs_operation_log、dfs_report_count、dfs_report_fac_state、dfs_sensor_record
     *
     * @param tableName 数据库表名
     * @param time      时间
     */
    void cleanDfsData(String tableName, String time);

    /**
     * 清理数据
     * 相关数据表：dfs_record_*、dfs_quality
     *
     * @param tableName 数据库表名
     * @param time      时间
     */
    void cleanDfsRecordOrQualityData(String tableName, String time);

    /**
     * 清理数据
     * 相关数据表：dfs_alarm_record
     *
     * @param tableName 数据库表名
     * @param time      时间
     */
    void cleanDfsAlarmRecordData(String tableName, String time);

    /**
     * 清理数据
     * 相关数据表：dfs_notice_record
     *
     * @param tableName 数据库表名
     * @param time      时间
     */
    void cleanDfsNoticeRecordData(String tableName, String time);

    /**
     * 清理数据空间
     */
    void cleanDfsDataSpace(String tableName);

    /**
     * 查询dfs中不能删除的表
     *
     * @return List<CleanableTableVo>
     */
    List<CleanableTableVo> getCleanableTableList();

    /**
     * 获取最早的时间
     * 相关数据表：dfs_operation_log、dfs_report_count、dfs_report_fac_state、dfs_sensor_record
     *
     * @param tableName
     * @return
     */
    String getEarliestCreatTime(String tableName);

    /**
     * 获取最早的时间
     * 相关数据表：dfs_alarm_record
     *
     * @param tableName
     * @return
     */
    String getEarliestAlarmTime(String tableName);

    /**
     * 获取最早的时间
     * 相关数据表：dfs_notice_record
     *
     * @param tableName
     * @return
     */
    String getEarliestSendTime(String tableName);

    /**
     * 获取最早的时间
     * 相关数据表：dfs_record_*、dfs_quality
     *
     * @param name
     * @return
     */
    String getEarliestTime(String name);

    /**
     * 获取条数
     *
     * @param tableName
     * @return
     */
    int getRows(String tableName);

    /**
     * 获取仅备份表结构的表名list
     *
     * @param backupTableList 仅备份结构的表list（额外包含）
     * @param tableSchema     需要操作的数据库
     * @return
     */
    List<String> getOnlyBackupStructureTable(List<String> backupTableList, String tableSchema);

    /**
     * 获取备份结构及数据的表名list
     *
     * @param backupTableList 仅备份结构的表list（额外排除）
     * @param tableSchema     需要操作的数据库
     * @return
     */
    List<String> getBackupDataStructureTable(List<String> backupTableList, String tableSchema);

    /**
     * 判断所属的表是否存在于该数据库中
     *
     * @param
     * @return
     */
    Boolean checkTableIsExist(String tableSchema, String tableName);

    /**
     * 判断所属的表是否存在于该数据库中
     *
     * @param
     * @return
     */
    void createTable(@Param("dto") TableCreateOrUpdateDTO dto);

    /**
     * 新增列
     */
    void addColumn(String tableSchema, String tableName, String fieldSql);

    /**
     * 删除列
     */
    void dropColumn(String tableSchema, String tableName, String deleteFieldCode);

    /**
     * 更新列
     */
    void updateColumn(String tableSchema, String tableName, String oldFieldCode, String fieldSql);

    /**
     * 删除表结构
     *
     * @return
     */
    void dropTable(String tableSchema, String tableName);

    /**
     * 执行查询sql
     */
    List<LinkedHashMap<String, Object>> executeSql(String sql);

    /**
     * 查询sql
     */
    Page<Map> selectTable(String tableSchema, String tableName, List<TableFieldSelectDTO> fieldSelects, Page<Object> page);

    /**
     * 添加表视图
     */
    void createTableView(String tableSchema, String tableName, String script);

    /**
     * 删除表视图
     */
    void deleteTableView(String tableSchema, String tableName);

    /**
     * 获取schema的元数据
     * @param tableNames 表名
     * @param tableColumns 表字段
     * @return 表对应列的元数据
     */
    List<TableColumnVO> listTableColumn(List<String> tableNames, List<String> tableColumns);

    /**
     * 获取视图元数据
     * @param viewName 视图名称
     * @return 视图对应列的元数据
     */
    @Select("SELECT * FROM `information_schema`.`COLUMNS` WHERE `table_name` = #{viewName}")
    List<TableColumnVO> getViewMetaDate(String viewName);

    /**
     * 获取表元数据
     * @param tableSchema 库
     * @param tableName 表
     * @return 视图对应列的元数据
     */
    @Select("SELECT * FROM `information_schema`.`COLUMNS` WHERE `table_schema` = #{tableSchema} AND `table_name` = #{tableName}")
    List<TableColumnVO> getColumnMetaDate(String tableSchema, String tableName);
    /**
     * 分页查询某张表
     * @param tableName 表名
     * @param offset 起始位置
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @Select("SELECT * FROM ${tableName} LIMIT #{offset}, #{pageSize}")
    List<LinkedHashMap<String, Object>> dataPage(
            @Param("tableName") String tableName,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize
    );
    /**
     * 查询某张表记录数
     * @param tableName 表名
     * @return 记录数
     */
    @Select("SELECT count(1) FROM ${tableName}")
    long dataCount(@Param("tableName") String tableName);

    /**
     * 获取表注释
     *
     * @param tableSchema 数据库名
     * @param tableName 表名
     * @return 表注释
     */
    @Select("SELECT table_comment FROM information_schema.tables WHERE table_schema = #{tableSchema} AND table_name = #{tableName} LIMIT 1")
    String getTableComment(@Param("tableSchema") String tableSchema, @Param("tableName") String tableName);


    @Select("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = #{tableName} AND TABLE_SCHEMA = #{tableSchema}")
    List<Map<String, String>> getTableColumns(@Param("tableSchema") String tableSchema, @Param("tableName") String tableName);

}
