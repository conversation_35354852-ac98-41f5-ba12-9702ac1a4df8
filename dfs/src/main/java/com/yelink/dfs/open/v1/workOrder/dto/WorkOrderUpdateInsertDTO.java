package com.yelink.dfs.open.v1.workOrder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;


/**
 * @Description:
 * @Author: zengzhengfu
 * @Date: 2024/7/30
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WorkOrderUpdateInsertDTO {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", required = true)
    private String workOrderNumber;

    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    private String workOrderName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "计划开始时间,新增必填")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @NotNull(message = "开始时间不能为空")
    private Date startDate;

    /**
     * 截止时间
     */
    @ApiModelProperty(value = "计划截止时间,新增必填")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @NotNull(message = "截止时间不能为空")
    private Date endDate;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 状态
     * 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消
     */
    @ApiModelProperty(value = "状态 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消")
    private Integer state;

    /**
     * 派工状态
     */
    @ApiModelProperty("派工状态 toBeAssigned-待派工、assigned-已派工")
    private String assignmentState;

    /**
     * 产线编号
     */
    @ApiModelProperty("产线编号")
    private String lineCode;

    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量,新增必填")
//    @NotNull(message = "计划数量不能为空")
    @UnitFormatColumn
    private Double planQuantity;

    /**
     * 计划批数
     */
    @ApiModelProperty("计划批数")
    private Double plannedBatches;

    /**
     * 每批计划数
     */
    @ApiModelProperty("每批计划数")
    private Double plansPerBatch;

    /**
     * 实际批数
     */
    @ApiModelProperty("实际批数")
    private Double actualBatches;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号,新增必填")
//    @NotNull(message = "物料编号不能为空")
    @UnitColumn
    private String materialCode;

    /**
     * 不合格量
     */
    @ApiModelProperty("不合格量")
    @UnitFormatColumn
    private Double unqualified;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 理论工作时长
     */
    @ApiModelProperty("理论工作时长")
    private Double theoreticalWorkingHours;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型", required = true, notes = "系统内置 1-正常工单 2-返回工单,dfs3.13版本支持自定义单据类型")
    private String orderType;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 有效工时
     */
    @ApiModelProperty("有效工时")
    private Double effectiveHours;

    /**
     * 计划员账号
     */
    @ApiModelProperty("计划员账号")
    private String magName;

    /**
     * 已完成数量
     */
    @ApiModelProperty("已完成数量")
    @UnitFormatColumn
    private Double finishCount;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    @UnitFormatColumn
    private Double inputTotal;

    /**
     * 工单接收人（工单完成时通知人员账号，逗号隔开）
     */
    @ApiModelProperty("工单接收人（工单完成时通知人员账号，逗号隔开）")
    private String noticeUsername;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级 正常、优先、加急、特急")
    private String priority;

    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    private String customerCode;

    /**
     * 计划交付时间/计划开始生产时间
     */
    @ApiModelProperty("计划交付时间/计划开始生产时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handOverDate;

    /**
     * 订单号，用逗号隔开
     */
    @ApiModelProperty("订单号")
    private String productOrderNumber;

    /**
     * 关联的生产订单物料行号
     */
    @ApiModelProperty("关联的生产订单物料行号")
    private Integer relatedProductOrderMaterialLineNumber;

    /**
     * 销售订单号，用逗号隔开
     */
    @ApiModelProperty("销售订单号")
    private String saleOrderNumber;

    /**
     * 关联的销售订单物料行号
     */
    @ApiModelProperty("关联的销售订单物料行号")
    private Integer relatedSaleOrderMaterialLineNumber;

    /**
     * 包装数
     */
    @ApiModelProperty("包装数")
    private Double packageQuantity;

    /**
     * 报工系数
     */
    @ApiModelProperty("报工系数")
    private Double coefficient;

    /**
     * 是否备料
     */
    @ApiModelProperty("是否备料")
    private Boolean prepared;

    /**
     * 是否发料
     */
    @ApiModelProperty("是否发料")
    private Boolean assigned;

    /**
     * erp关联单据编号
     */
    @ApiModelProperty("erp关联单据编号")
    private String erpDocumentCode;

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    private Boolean issue;

    /**
     * 计量重量(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量重量(工单物料计划数量记录计算用)")
    private Double measurementQuantity;

    /**
     * 计量单位(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量单位(工单物料计划数量记录计算用)")
    private String measurementUnit;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String approver;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    private String actualApprover;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;

    /**
     * 审批建议
     */
    @ApiModelProperty("审批建议")
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 工艺编号
     */
    @ApiModelProperty("工艺编号")
    private String craftCode;

    /**
     * 绑定的工艺工序列表
     */
    @ApiModelProperty("绑定的工艺工序列表")
    private List<CraftProcedureEntityUpsertDTO> craftProcedureEntities;

    /**
     * 产前状态
     */
    @ApiModelProperty("产前状态")
    private Boolean prenatalStatus;

    /**
     * 工作中心编码
     */
    @ApiModelProperty(value = "工作中心编码,新增必填")
//    @NotBlank(message = "工作中心编码不能为空")
    private String workCenterCode;

    /**
     * 已领料数
     */
    @ApiModelProperty("已领料数")
    @UnitFormatColumn
    private Double pickingQuantity;

    /**
     * 已入库数
     */
    @ApiModelProperty("已入库数")
    @UnitFormatColumn
    private Double inventoryQuantity;

    /**
     * 供应商code
     */
    @ApiModelProperty("供应商code")
    private String supplierCode;

    /**
     * 数据导入时间
     */
    @ApiModelProperty("数据导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date importTime;

    /**
     * 班组编号
     */
    @ApiModelProperty("班组编号")
    private String teamCode;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码")
    private String deviceCode;

    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    private String customerMaterialCode;

    /**
     * 工单关联班组编码
     */
    @ApiModelProperty("工单关联班组编码")
    private List<String> relevanceTeamCodes;

    /**
     * 工单关联设备编码
     */
    @ApiModelProperty("工单关联设备编码")
    private List<String> relevanceDeviceCodes;

    /**
     * 工单关联制造单元编码
     */
    @ApiModelProperty("工单关联制造单元编码")
    private List<String> relevanceLineCodes;


    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    private Integer skuId;

    /**
     * 包装方案编码
     */
    @ApiModelProperty("包装方案编码")
    private String packageSchemeCode;

    /**
     * 是否通过对外接口插入/更新
     * 某些字段接口需要提供更新，但是不能影响系统原本的业务
     */
    private Boolean operateByApi = true;

    /**
     * 工单拓展字段1
     */
    @ApiModelProperty(value = "工单拓展字段1")
    private String workOrderExtendFieldOne;
    /**
     * 工单拓展字段2
     */
    @ApiModelProperty(value = "工单拓展字段2")
    private String workOrderExtendFieldTwo;
    /**
     * 工单拓展字段3
     */
    @ApiModelProperty(value = "工单拓展字段3")
    private String workOrderExtendFieldThree;
    /**
     * 工单拓展字段4
     */
    @ApiModelProperty(value = "工单拓展字段4")
    private String workOrderExtendFieldFour;
    /**
     * 工单拓展字段5
     */
    @ApiModelProperty(value = "工单拓展字段5")
    private String workOrderExtendFieldFive;

    /**
     * 工单拓展字段6
     */
    @ApiModelProperty(value = "工单拓展字段6")
    private String workOrderExtendFieldSix;
    /**
     * 工单拓展字段7
     */
    @ApiModelProperty(value = "工单拓展字段7")
    private String workOrderExtendFieldSeven;
    /**
     * 工单拓展字段8
     */
    @ApiModelProperty(value = "工单拓展字段8")
    private String workOrderExtendFieldEight;
    /**
     * 工单拓展字段9
     */
    @ApiModelProperty(value = "工单拓展字段9")
    private String workOrderExtendFieldNine;
    /**
     * 工单拓展字段10
     */
    @ApiModelProperty(value = "工单拓展字段10")
    private String workOrderExtendFieldTen;
    /**
     * 工单物料拓展字段1
     */
    @ApiModelProperty(value = "工单物料拓展字段1")
    private String workOrderMaterialExtendFieldOne;
    /**
     * 工单物料拓展字段2
     */
    @ApiModelProperty(value = "工单物料拓展字段2")
    private String workOrderMaterialExtendFieldTwo;
    /**
     * 工单物料拓展字段3
     */
    @ApiModelProperty(value = "工单物料拓展字段3")
    private String workOrderMaterialExtendFieldThree;
    /**
     * 工单物料拓展字段4
     */
    @ApiModelProperty(value = "工单物料拓展字段4")
    private String workOrderMaterialExtendFieldFour;
    /**
     * 工单物料拓展字段5
     */
    @ApiModelProperty(value = "工单物料拓展字段5")
    private String workOrderMaterialExtendFieldFive;
    /**
     * 工单物料拓展字段6
     */
    @ApiModelProperty(value = "工单物料拓展字段6")
    private String workOrderMaterialExtendFieldSix;
    /**
     * 工单物料拓展字段7
     */
    @ApiModelProperty(value = "工单物料拓展字段7")
    private String workOrderMaterialExtendFieldSeven;
    /**
     * 工单物料拓展字段8
     */
    @ApiModelProperty(value = "工单物料拓展字段8")
    private String workOrderMaterialExtendFieldEight;
    /**
     * 工单物料拓展字段9
     */
    @ApiModelProperty(value = "工单物料拓展字段9")
    private String workOrderMaterialExtendFieldNine;
    /**
     * 工单物料拓展字段10
     */
    @ApiModelProperty(value = "工单物料拓展字段10")
    private String workOrderMaterialExtendFieldTen;

    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    private String businessUnitName;

    /**
     * 投产检查结果（true--通过  false--不通过  null--空）
     */
    @ApiModelProperty(value = "投产检查结果（true--通过  false--不通过  null--空）")
    private Boolean investCheckResult;

    /**
     * 是否更新关联订单数据
     */
    @ApiModelProperty(value = "存在工单时，是否更新关联订单数据，默认为更新")
    private Boolean isUpdateRelateOrder = true;

    /**
     * 存在工单时，是否更新工单关联资源
     */
    @ApiModelProperty(value = "存在工单时，是否更新工单关联资源，默认为更新")
    private Boolean isUpdateRelevanceResource = true;


}


