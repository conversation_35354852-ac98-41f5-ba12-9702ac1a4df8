package com.yelink.dfs.open.v1.workOrder;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.common.DeleteRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderRemarkEntity;
import com.yelink.dfs.entity.order.dto.BatchUpdateWorkOrderDTO;
import com.yelink.dfs.entity.order.dto.UpdateWorkOrderPackageSchemeDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderOpenSelectExtendDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkInsertDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderRemarkUpdateDTO;
import com.yelink.dfs.entity.product.dto.CraftFileDTO;
import com.yelink.dfs.entity.reporter.dto.ReporterRecordDTO;
import com.yelink.dfscommon.common.unit.config.UnitFormatMethod;
import com.yelink.dfscommon.dto.dfs.ReporterRecordVO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumberDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderUpdateDTO;
import com.yelink.dfs.open.v1.workOrder.dto.ProductionBaseUnitDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderInsertDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderUpdateInsertDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderUpdateStateDTO;
import com.yelink.dfs.service.common.DeleteRecordService;
import com.yelink.dfs.service.order.OrderExecuteSeqService;
import com.yelink.dfs.service.order.WorkOrderRemarkService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.order.WorkOrderSubService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.pojo.message.KafkaMessageResult;
import com.yelink.dfscommon.pojo.message.NewMessageEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @Description: 生产工单对外接口
 * @Author: zhuangwq
 * @Date: 2022/9/16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v1/open/work_orders")
@Api(tags = "任务中心/V1/生产工单接口")
public class WorkOrderOpenController extends BaseController {

    private WorkOrderService workOrderService;
    private ReportLineService reportLineService;
    private WorkOrderSubService workOrderSubService;
    private DeleteRecordService deleteRecordService;
    private OrderExecuteSeqService orderExecuteSeqService;
    private WorkOrderRemarkService workOrderRemarkService;
    /**
     * 查询生产工单全量信息（包括关联的所有数据）
     *
     * @param dto
     * @return
     */
    @PostMapping("/detail/all")
    @ApiOperation(value = "查询生产工单信息", notes = "只支持传递工单号，查询的数据为工单的所有的关联数据")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public Result<WorkOrderEntity> getWorkOrderByAllData(@RequestBody WorkOrderNumberDTO dto) {
        WorkOrderEntity entity = null;
        if (StringUtils.isNotBlank(dto.getWorkOrderNumber())) {
            WorkOrderEntity one = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, dto.getWorkOrderNumber()).one();
            if (one != null) {
                WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(dto.getWorkOrderNumber());
                entity = workOrderService.getWorkOrderById(detailDTO);
            }
        }
        return Result.success(entity);
    }

    /**
     * 查询生产工单信息
     *
     * @param dto
     * @return
     */
    @PostMapping("/detail")
    @ApiOperation(value = "查询生产工单信息", notes = "可自定义查询需要关联的数据")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public Result<WorkOrderEntity> getWorkOrder(@RequestBody WorkOrderDetailDTO dto) {
        WorkOrderEntity entity = null;
        if (StringUtils.isNotBlank(dto.getWorkOrderNumber())) {
            WorkOrderEntity one = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, dto.getWorkOrderNumber()).one();
            // 为提升查询效率，如果仅查询简单信息，直接返回
            if (Objects.nonNull(dto.getIsShowSimpleInfo()) && dto.getIsShowSimpleInfo()) {
                return Result.success(one);
            }
            if (one != null) {
                entity = workOrderService.getWorkOrderById(dto);
            }
        }
        return Result.success(entity);
    }

    /**
     * 查询生产工单列表全量信息（包括关联的所有数据）
     *
     * @param workOrderSelectDTO
     * @return
     */
    @ApiOperation(value = "查询生产工单列表", notes = "查询的数据为工单的所有的关联数据")
    @PostMapping("/list/all")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public Result<PageResult<WorkOrderEntity>> orderListSimpleByAllData(@RequestBody WorkOrderOpenSelectExtendDTO workOrderSelectDTO) {
        if (StringUtils.isBlank(workOrderSelectDTO.getUsername())) {
            workOrderSelectDTO.setUsername(getUsername());
        }
        workOrderSelectDTO.setIsShowCraftProcedureInfo(true);
        workOrderSelectDTO.setIsShowMaterialFieldInfo(true);
        workOrderSelectDTO.setIsShowPackageSchemeInfo(true);
        workOrderSelectDTO.setIsShowPlanTheoryHourInfo(true);
        workOrderSelectDTO.setIsShowUserInfo(true);
        Page<WorkOrderEntity> list = workOrderService.getWorkOrderPageByOpenApi(workOrderSelectDTO);
        return Result.success(list);
    }

    /**
     * 查询生产工单列表（可模糊、条件查询）
     *
     * @param workOrderSelectDTO
     * @return
     */
    @ApiOperation(value = "查询生产工单列表", notes = "可自定义查询需要关联的数据")
    @PostMapping("/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public Result<PageResult<WorkOrderEntity>> orderListSimple(@RequestBody WorkOrderOpenSelectExtendDTO workOrderSelectDTO) {
        if (StringUtils.isBlank(workOrderSelectDTO.getUsername())) {
            workOrderSelectDTO.setUsername(getUsername());
        }
        Page<WorkOrderEntity> list = workOrderService.getWorkOrderPageByOpenApi(workOrderSelectDTO);
        return Result.success(list);
    }

    /**
     * 创建生产工单
     *
     * @param workOrderInsertDTO
     * @return
     */
    @ApiOperation(value = "创建生产工单")
    @PostMapping("/insert")
    @UnitFormatMethod(UnitFormatMethod.MethodType.REQUEST)
    public Result<Integer> addOrder(@RequestBody @Validated WorkOrderInsertDTO workOrderInsertDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        WorkOrderEntity workOrderEntity = JacksonUtil.convertObject(workOrderInsertDTO, WorkOrderEntity.class);
        workOrderService.add(workOrderEntity);
        return Result.success(workOrderEntity.getWorkOrderId());
    }

    /**
     * 创建生效状态的生产工单
     *
     * @param workOrderInsertDTO
     * @return
     */
    @ApiOperation(value = "创建生效状态的生产工单")
    @PostMapping("/insert/released")
    @UnitFormatMethod(UnitFormatMethod.MethodType.REQUEST)
    public Result<Integer> addReleasedWorkOrder(@RequestBody @Validated WorkOrderInsertDTO workOrderInsertDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        WorkOrderEntity workOrderEntity = JacksonUtil.convertObject(workOrderInsertDTO, WorkOrderEntity.class);
        WorkOrderEntity entity = workOrderService.addReleasedWorkOrder(workOrderEntity, workOrderEntity.getCreateBy());
        return Result.success(entity.getWorkOrderId());
    }

    /**
     * 修改生产工单信息
     *
     * @param workOrderUpdateDTO
     * @return
     */
    @ApiOperation(value = "修改生产工单信息")
    @PutMapping("/update")
    @UnitFormatMethod(UnitFormatMethod.MethodType.REQUEST)
    public Result update(@RequestBody @Validated WorkOrderUpdateDTO workOrderUpdateDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        WorkOrderEntity workOrderEntity = JacksonUtil.convertObject(workOrderUpdateDTO, WorkOrderEntity.class);
        WorkOrderEntity old = workOrderService.getById(workOrderEntity.getWorkOrderId());
        if (Objects.isNull(old)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderEntity.getWorkOrderId()));
        }
        workOrderEntity.setUpdateDate(new Date());
        workOrderEntity.setOperateByApi(true);
        workOrderService.updateByWorkId(workOrderEntity, workOrderEntity.getUpdateBy());
        return Result.success();
    }

    /**
     * 新增-更新工单
     *
     * @param workOrderUpdateDTO
     * @return
     */
    @PostMapping("/upsert")
    @ApiOperation(value = "新增-更新工单", notes = "唯一标识:工单编号，不存在新增，存在则更新")
    @UnitFormatMethod(UnitFormatMethod.MethodType.REQUEST)
    public Result upsert(@RequestBody @Validated WorkOrderUpdateInsertDTO workOrderUpdateDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        workOrderSubService.upsert(workOrderUpdateDTO);
        return Result.success();
    }

    /**
     * 修改生产工单状态
     *
     * @param workOrderUpdateDTO
     * @return
     */
    @ApiOperation(value = "修改生产工单状态", hidden = true)
    @PutMapping("/update/state")
    public Result updateStateOnly(@RequestBody @Validated WorkOrderUpdateDTO workOrderUpdateDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        WorkOrderEntity workOrderEntity = JacksonUtil.convertObject(workOrderUpdateDTO, WorkOrderEntity.class);
        if (workOrderEntity == null) {
            return Result.fail("工单为空");
        }
        workOrderService.updateStateOnly(workOrderEntity, 0, workOrderEntity.getUpdateBy());
        return Result.success();
    }

    /**
     * 生产工单批量编辑
     *
     * @param
     * @return
     */
    @ApiOperation(value = "生产工单批量编辑")
    @PutMapping("/batch/update")
    public Result batchUpdate(@RequestBody BatchUpdateWorkOrderDTO entity) {
        // 随机生成一个code,用于标识本次操作
        String code = RandomUtil.randomString(10);
        workOrderService.batchUpdateWorkOrder(entity, getUsername(), code);
        return Result.success();
    }

    /**
     * 通过Id删除生产工单信息
     *
     * @param workOrderId
     * @return
     */
    @ApiImplicitParam(dataType = "Integer", name = "workOrderId", required = true, value = "生产工单ID")
    @ApiOperation(value = "通过Id删除生产工单信息")
    @DeleteMapping("/delete/{workOrderId}")
    public Result delete(@PathVariable Integer workOrderId) {
        workOrderService.deleteById(workOrderId, getUsername());
        return Result.success();
    }

    /**
     * 通过工单号删除生产工单信息
     *
     * @param
     * @return
     */
    @ApiOperation(value = "通过工单号删除生产工单信息")
    @DeleteMapping("/delete")
    public Result deleteByWorkOrderNumber(@RequestBody WorkOrderDetailDTO dto) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(dto.getWorkOrderNumber());
        if (Objects.isNull(workOrderEntity)) {
            return Result.success();
        }
        workOrderService.deleteById(workOrderEntity.getWorkOrderId(), getUsername());
        return Result.success();
    }

    /**
     * 通过工单Id查询生产工单详情
     *
     * @param workOrderId
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "Integer", name = "workOrderId", required = true, value = "生产工单ID"),
            @ApiImplicitParam(dataType = "Boolean", name = "isShowSimpleInfo", value = "是否仅查询简单的信息（不包含关联的数据）")
    })
    @ApiOperation(value = "通过工单Id查询生产工单详情")
    @GetMapping("/select/{workOrderId}")
    public Result<WorkOrderEntity> selectById(@PathVariable("workOrderId") Integer workOrderId,
                                              @RequestParam(value = "isShowSimpleInfo", required = false) Boolean isShowSimpleInfo) {
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Objects.nonNull(isShowSimpleInfo) && isShowSimpleInfo) {
            WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
            return Result.success(workOrderEntity);
        }
        WorkOrderEntity workOrderEntity = workOrderService.getById(workOrderId);
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderEntity.getWorkOrderNumber());
        WorkOrderEntity entity = workOrderService.getWorkOrderById(detailDTO);
        return Result.success(entity);
    }

    /**
     * 通过工单号查询工单的附件(支持附件名称模糊查询)
     *
     * @param workOrderNumber
     * @param fileName
     * @return
     */
    @ApiImplicitParams({
            @ApiImplicitParam(dataType = "String", name = "workOrderNumber", required = true, value = "生产工单号"),
            @ApiImplicitParam(dataType = "String", name = "fileName", value = "附件名称")
    })
    @ApiOperation(value = "通过工单号查询工单的附件(支持附件名称模糊查询)")
    @GetMapping("/work/order/file")
    public Result<List<CraftFileDTO>> getWorkOrderFileByWorkNumber(@RequestParam(value = "workOrderNumber") String workOrderNumber,
                                                                   @RequestParam(value = "fileName", required = false) String fileName) {
        List<CraftFileDTO> workOrderFileList = workOrderService.getWorkOrderFileByWorkNumber(workOrderNumber, fileName);
        return Result.success(workOrderFileList);
    }

    /**
     * 生产工单的报工记录查询
     *
     * @param dto 报工记录-请求DTO
     * @return
     */
    @ApiOperation(value = "查询生产工单的报工记录列表")
    @PostMapping("/reporter/record/list")
    public Result<PageResult<ReporterRecordVO>> getList(@RequestBody ReporterRecordDTO dto) {
        Page<ReporterRecordVO> result = reportLineService.getReporterRecord(dto);
        return Result.success(result);
    }

    /**
     * 批量审批
     *
     * @return
     */
    @ApiOperation(value = "批量审批生产工单")
    @PostMapping("/approve/batch")
    public Result approveBatch(@RequestBody ApproveBatchDTO dto) {
        dto.setActualApprover(StringUtils.isNotBlank(dto.getActualApprover()) ? dto.getActualApprover() : getUsername());
        workOrderService.approveBatch(dto);
        return Result.success();
    }


    /**
     * 通过销售订单获取生产工单列表
     *
     * @return
     */
    @ApiOperation(value = "通过销售订单获取生产工单列表")
    @GetMapping("/get/work/order/by/sale")
    public Result<List> getWorkOrderBySaleOrder(@RequestParam String saleOrderNumber,
                                                @RequestParam String materialCode) {
        List<WorkOrderEntity> list = workOrderService.lambdaQuery()
                .eq(WorkOrderEntity::getSaleOrderNumber, saleOrderNumber)
                .eq(WorkOrderEntity::getMaterialCode, materialCode)
                .list();
        return Result.success(list.stream().map(WorkOrderEntity::getWorkOrderNumber).filter(Objects::nonNull).collect(Collectors.toList()));
    }

    /**
     * 记录删除工单时的关联单据记录
     *
     * @param recordEntities 关联数据的删除记录
     * @return
     */
    @ApiOperation(value = "记录删除工单时的关联单据记录")
    @PostMapping("/record/delete/record")
    public Result recordDeleteRecord(@RequestBody List<DeleteRecordEntity> recordEntities) {
        deleteRecordService.saveBatch(recordEntities);
        return Result.success();
    }

    /**
     * 更新工单的包装方案
     *
     * @param
     * @return
     */
    @ApiOperation(value = "更新工单的包装方案")
    @PostMapping("/update/package_scheme")
    public Result updatePackageScheme(@RequestBody UpdateWorkOrderPackageSchemeDTO dto) {
        workOrderService.updatePackageScheme(dto);
        return Result.success();
    }

    /**
     * 通过产线查询最早投产的工单号
     *
     * @return
     */
    @PostMapping("/earliest/producing")
    @ApiOperation(value = "通过产线查询最早投产的工单号")
    public Result getEarliestProducingWorkOrder(@RequestBody ProductionBaseUnitDTO dto) {
        Result success = Result.success();
        if (dto.getLineId() != null) {
            String occupyOrderByLineId = orderExecuteSeqService.getOccupyOrderByLineId(dto.getLineId());
            success.setData(occupyOrderByLineId);
            return success;
        }

        String occupyOrderByDeviceId = orderExecuteSeqService.getOccupyOrderByDeviceId(dto.getDeviceId());
        success.setData(occupyOrderByDeviceId);
        return success;
    }

    /**
     * 批量修改工单状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改工单状态")
    @PutMapping("/batch/update/state")
    public Result<List> batchUpdateState(@RequestBody @Validated WorkOrderUpdateStateDTO dto, BindingResult bindingResult) {
        log.info("批量修改工单状态:" + JacksonUtil.toJSONString(dto));
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        if (StringUtils.isBlank(dto.getUsername())) {
            dto.setUsername(getUsername());
        }
        workOrderService.batchUpdateState(dto);
        return Result.success();
    }

    /**
     * kafka消息说明（返回参数说明，非接口）
     *
     * @return
     */
    @ApiOperation(value = "kafka消息说明（返回参数说明，非接口）", notes = "topic:event_value_chain\n" + "kafka消息method参数说明：\n" +
            "workOrder.add：新增工单\n" +
            "workOrder.update：修改工单\n" +
            "workOrder.delete：删除工单" +
            "workOrder.statusChange：工单状态改变\n" +
            "workOrder.countChange：工单修改数量\n",
            extensions = {
                    @Extension(properties = {
                            @ExtensionProperty(name = "topic", value = "event_value_chain"),
                    }),
                    @Extension(name = "messageModelId", properties = {
                            @ExtensionProperty(name = "新增工单", value = "workOrder.add"),
                            @ExtensionProperty(name = "修改工单", value = "workOrder.update"),
                            @ExtensionProperty(name = "删除工单", value = "workOrder.delete"),
                            @ExtensionProperty(name = "工单状态改变", value = "workOrder.statusChange"),
                            @ExtensionProperty(name = "工单修改数量", value = "workOrder.countChange")
                    })
            })
    @GetMapping("/kafka/message")
    public KafkaMessageResult<NewMessageEntity<WorkOrderEntity>> message() {
        return KafkaMessageResult.success();
    }


    /**
     * 查询工单备注列表
     *
     * @param workOrderNumber
     * @return
     */
    @ApiOperation(value = "查询工单备注列表")
    @ApiImplicitParam(dataType = "String", name = "workOrderNumber", required = true, value = "生产工单号")
    @GetMapping("/remark/list")
    public ResponseData list(@RequestParam String workOrderNumber) {
        List<WorkOrderRemarkEntity> list = workOrderRemarkService.listWorkOrderRemark(workOrderNumber);
        return success(list);
    }

    /**
     * 新增工单备注
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "新增工单备注")
    @PostMapping("/remark/add")
    public ResponseData add(@RequestBody @Validated WorkOrderRemarkInsertDTO dto) {
        workOrderRemarkService.add(dto);
        return success();
    }

    /**
     * 修改工单备注
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改工单备注")
    @PostMapping("/remark/edit")
    public ResponseData edit(@RequestBody @Validated WorkOrderRemarkUpdateDTO dto) {
        workOrderRemarkService.edit(dto);
        return success();
    }

    /**
     * 删除工单备注
     * @param id
     * @return
     */
    @ApiOperation(value = "删除工单备注")
    @DeleteMapping("/remark/remove")
    public ResponseData remove(@RequestParam Integer id) {
        workOrderRemarkService.removeById(id);
        return success();
    }
}
