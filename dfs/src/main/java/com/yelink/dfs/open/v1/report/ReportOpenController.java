package com.yelink.dfs.open.v1.report;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.constant.OperationType;
import com.yelink.dfs.constant.ReportType;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.target.record.ReportDayCountEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.target.record.dto.ReportLineAuditDTO;
import com.yelink.dfs.entity.target.record.dto.ReportLineAuditSelectDTO;
import com.yelink.dfs.open.v1.report.dto.ReportActionDTO;
import com.yelink.dfs.open.v1.report.dto.ReportCountInsertDTO;
import com.yelink.dfs.open.v1.report.dto.ReportDayCountDTO;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.target.record.ReportCountService;
import com.yelink.dfs.service.target.record.ReportDayCountService;
import com.yelink.dfs.service.target.record.ReportLineAuditLogService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfscommon.annotation.OperLog;
import com.yelink.dfscommon.common.unit.config.UnitFormatMethod;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.dto.dfs.ReportDTO;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.pojo.message.KafkaMessageResult;
import com.yelink.dfscommon.pojo.message.NewMessageEntity;
import com.yelink.dfscommon.utils.JacksonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * @Description: 工单报工接口
 * @Author: zengzhengfu
 * @Date: 2022/9/22
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "工作中心/V1/报工接口")
@RequestMapping("/v1/open/report")
public class ReportOpenController extends BaseController {

    private ReportCountService reportCountService;
    private ReportLineService reportLineService;
    private ProductionLineService productionLineService;
    private ReportDayCountService reportDayCountService;
    private WorkOrderService workOrderService;
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    private ReportLineAuditLogService reportLineAuditLogService;

    /**
     * 产线报工--选择工单开始停止
     * <p>
     * workOrder
     * action    0-投产  1-完成 2-挂起
     *
     * @return
     */
    @PostMapping("/update/state")
    @ApiOperation(value = "修改工单状态")
    @OperLog(module = "工单状态变更", type = OperationType.ADD, desc = "更新了单号为#{workOrderNumber}的状态")
    public ResponseData reportAction(@RequestBody ReportActionDTO reportActionDTO) {
        ReportDTO dto = JacksonUtil.convertObject(reportActionDTO, ReportDTO.class);
        return success(productionLineService.reportAction(dto));
    }

    /**
     * 产线报工--输入工单产量
     *
     * @param entity 工单产量
     * @return
     */
    @PostMapping("/count")
    @ApiOperation(value = "工单报工---输入工单产量", hidden = true)
    @OperLog(module = "产线报工", type = OperationType.ADD, desc = "新增了单号为#{workOrder}的报工记录")
    public Result countOutput(@RequestBody ReportCountInsertDTO entity) {
        ReportLineEntity reportLineEntity = JacksonUtil.convertObject(entity, ReportLineEntity.class);
        reportCountService.countOutput(reportLineEntity);
        return Result.success(entity);
    }

    /**
     * 修改报工数量
     *
     * @param entity
     * @return
     */
    @PutMapping("/update")
    @ApiOperation(value = "工单报工--修改报工数量", hidden = true)
    @OperLog(module = "产线报工", type = OperationType.UPDATE, desc = "更新了单号为#{workOrder}为的报工记录")
    public Result updateCount(@RequestBody ReportLineEntity entity) {
        reportCountService.updateCount(entity);
        return Result.success(entity);
    }

    /**
     * 工单报工--获取报工记录列表
     * 去掉按天小计 报工记录汇总
     *
     * @return
     */
    @ApiOperation(value = "工单报工--获取报工记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "workOrder", value = "工单号", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "batch", value = "批次", dataType = "String", defaultValue = ""),
            @ApiImplicitParam(name = "shiftId", value = "班次id", dataType = "Integer", defaultValue = ""),
            @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "Long", defaultValue = ""),
            @ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "Long", defaultValue = ""),
            @ApiImplicitParam(name = "id", value = "报工记录id", dataType = "Integer", defaultValue = ""),
            @ApiImplicitParam(name = "current", value = "当前页", dataType = "Integer", defaultValue = ""),
            @ApiImplicitParam(name = "size", value = "每页显示条数", dataType = "Integer", defaultValue = "")
    })
    @GetMapping("/report/history/count")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public Result<PageResult<ReportLineEntity>> getReportLineHistory(@RequestParam(value = "workOrder", required = false) String workOrder,
                                                                     @RequestParam(value = "batch", required = false) String batch,
                                                                     @RequestParam(value = "shiftId", required = false) Integer shiftId,
                                                                     @RequestParam(value = "startDate", required = false) Long startDate,
                                                                     @RequestParam(value = "endDate", required = false) Long endDate,
                                                                     @RequestParam(value = "id", required = false) Integer id,
                                                                     @RequestParam(value = "current", required = false) Integer current,
                                                                     @RequestParam(value = "size", required = false) Integer size) {
        LambdaQueryWrapper<ReportLineEntity> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(StringUtils.isNotBlank(workOrder), ReportLineEntity::getWorkOrder, workOrder)
                .eq(ReportLineEntity::getType, ReportType.REPORT.getType())
                .eq(id != null, ReportLineEntity::getId, id)
                .eq(Objects.nonNull(shiftId), ReportLineEntity::getShiftId, shiftId)
                .like(StringUtils.isNotBlank(batch), ReportLineEntity::getBatch, batch)
                .orderByDesc(ReportLineEntity::getCreateTime)
                .orderByDesc(ReportLineEntity::getId);
        if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
            countWrapper.between(ReportLineEntity::getReportDate, new Date(startDate), new Date(endDate));
        }
        Page<ReportLineEntity> page;
        if (current == null || size == null) {
            List<ReportLineEntity> list = reportLineService.list(countWrapper);
            page = new Page<>(1, list.size(), list.size());
            page.setRecords(list);
        } else {
            page = reportLineService.page(new Page<>(current, size), countWrapper);
        }
        return Result.success(page);
    }


    /**
     * kafka消息说明（返回参数说明，非接口）
     *
     * @return
     */
    @ApiOperation(value = "kafka消息说明（返回参数说明，非接口）", notes = "topic:event_record\n" + "kafka消息method参数说明：\n" +
            "reportLine.add：新增报工\n" +
            "reportLine.update：更新报工\n",
            extensions = {
                    @Extension(properties = {
                            @ExtensionProperty(name = "topic", value = "event_record"),
                    }),
                    @Extension(name = "messageModelId", properties = {
                            @ExtensionProperty(name = "新增报工", value = "reportLine.add"),
                            @ExtensionProperty(name = "更新报工", value = "reportLine.update"),
                    })
            })
    @GetMapping("/kafka/message")
    public KafkaMessageResult<NewMessageEntity<ReportLineEntity>> message() {
        return KafkaMessageResult.success();
    }


    /**
     * 查询每日报工数量
     *
     * @return
     */
    @PostMapping("/day/count")
    @ApiOperation(value = "工位看板--查询每日报工数量", hidden = true)
    public Result<List<ReportDayCountEntity>> getReportDayCount(@RequestBody ReportDayCountDTO dto) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(dto.getWorkOrderNumber());
        List<ReportDayCountEntity> reportDayCountEntities = reportDayCountService.getReportDayCountEntities(workOrderEntity, dto.getReportLineId());
        return Result.success(reportDayCountEntities);
    }


    /**
     * 条件查询报工完成总数量*
     * @param dto 工单编号：必填
     * @param dto  日期：选填
     * @return Double
     */
    @ApiOperation(value = "条件查询报工完成总数量")
    @PostMapping("/day/finish/count")
    public ResponseData getWorkOrderDayCount(@RequestBody ReportDayCountDTO dto) {
        if (StringUtils.isBlank(dto.getWorkOrderNumber())) {
            throw new ParamException("工单编码不能为空");
        }
        Date date = Date.from(dto.getTime().atStartOfDay(ZoneId.systemDefault()).toInstant());
        return success(recordWorkOrderDayCountService.getWorkOrderDayCount(dto.getWorkOrderNumber(), date));
    }

    /**
     * 报工记录审核
     */
    @PutMapping("/audit")
    @ApiOperation(value = "报工记录审核")
    public Result<?> audit(@RequestBody @Valid List<ReportLineAuditDTO> dtos) {
        reportCountService.audit(dtos);
        return Result.success();
    }
    @PostMapping("/audit_log/page")
    @ApiOperation(value = "报工记录审核")
    public Result<?> auditLogPage(@RequestBody @Valid ReportLineAuditSelectDTO dto) {
        return Result.success(reportLineAuditLogService.getPage(dto));
    }

    @PostMapping("/complete_summary")
    @ApiOperation(value = "完工汇总")
    public Result<?> completeSummary(@RequestBody List<String> workOrderNumbers) {
        return Result.success(reportLineService.completeSummary(workOrderNumbers));
    }


}
