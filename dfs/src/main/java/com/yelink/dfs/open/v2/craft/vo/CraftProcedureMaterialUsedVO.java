package com.yelink.dfs.open.v2.craft.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 工序用料
 * <AUTHOR>
 */
@Data
@ToString
public class CraftProcedureMaterialUsedVO {

    /**
     * Id
     */
    @ApiModelProperty("Id")
    private Integer id;

    @ApiModelProperty("工艺ID")
    private Integer craftId;

    @ApiModelProperty("工艺工序ID")
    private Integer procedureId;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("用量")
    private Double number;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("数据来源")
    private String dataSource;

    @ApiModelProperty("扩展字段")
    private String extend;

}
