package com.yelink.dfs.open.v1.workOrder.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.entity.order.WorkOrderTeamEntity;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-03-17 11:59
 */

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WorkOrderInsertDTO {

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", required = true)
    private String workOrderNumber;

    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    private String workOrderName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "计划开始时间不能为空")
    private Date startDate;

    /**
     * 截止时间
     */
    @ApiModelProperty(value = "计划截止时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "计划截止时间不能为空")
    private Date endDate;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 状态
     * 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消
     */
    @ApiModelProperty("状态 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消")
    private Integer state;

    /**
     * 派工状态
     */
    @ApiModelProperty("派工状态 toBeAssigned-待派工、assigned-已派工")
    private String assignmentState;

    /**
     * 产线编号
     */
    @ApiModelProperty("产线编号")
    private String lineCode;

    /**
     * 产线名称
     */
    @ApiModelProperty("产线名称")
    private String lineName;

    /**
     * 产线id
     */
    @ApiModelProperty("产线id")
    private Integer lineId;

    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量", required = true)
    @NotNull(message = "计划数量不能为空")
    @UnitFormatColumn
    private Double planQuantity;

    /**
     * 物料ID
     */
    @ApiModelProperty("物料ID")
    private Integer relateOrderMaterialId;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号", required = true)
    @NotNull(message = "物料编号不能为空")
    @UnitColumn
    private String materialCode;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型", required = true, notes = "系统内置 1-正常工单 2-返回工单,dfs3.13版本支持自定义单据类型")
    private String orderType;

    /**
     * CreateBy
     */
    @ApiModelProperty("CreateBy")
    private String createBy;

    /**
     * CreateDate
     */
    @ApiModelProperty("CreateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * UpdateBy
     */
    @ApiModelProperty("UpdateBy")
    private String updateBy;

    /**
     * UpdateDate
     */
    @ApiModelProperty("UpdateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 计划员真实姓名
     */
    @ApiModelProperty("计划员真实姓名")
    private String magNickname;

    /**
     * 计划员手机号
     */
    @ApiModelProperty("计划员手机号")
    private String magPhone;

    /**
     * 计划员账号
     */
    @ApiModelProperty("计划员账号")
    private String magName;

    /**
     * 工单接收人（工单完成时通知人员账号，逗号隔开）
     */
    @ApiModelProperty("工单接收人（工单完成时通知人员账号，逗号隔开）")
    private String noticeUsername;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级 正常、优先、加急、特急", required = true)
    private String priority;

    /**
     * 包装数
     */
    @ApiModelProperty("包装数")
    private Double packageQuantity;

    /**
     * 报工系数
     */
    @ApiModelProperty("报工系数")
    private Double coefficient;

    /**
     * 是否备料
     */
    @ApiModelProperty("是否备料")
    private Boolean prepared;

    /**
     * 是否发料
     */
    @ApiModelProperty("是否发料")
    private Boolean assigned;

    /**
     * erp关联单据编号
     */
    @ApiModelProperty("erp关联单据编号")
    private String erpDocumentCode;

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    private Boolean issue;

    /**
     * 工艺Id
     */
    @ApiModelProperty("工艺Id")
    private Integer craftId;
    /**
     * 工艺编号
     */
    @ApiModelProperty("工艺编号")
    private String craftCode;

    /**
     * 订单号，用逗号隔开
     */
    @ApiModelProperty("订单号，用逗号隔开")
    private String productOrderNumber;

    /**
     * 销售订单号，用逗号隔开
     */
    @ApiModelProperty("销售订单号，用逗号隔开")
    private String saleOrderNumber;

    /**
     * 生产工单列表
     */
    @ApiModelProperty("生产工单列表")
    private List<ProductOrderDTO> productOrderList;

    /**
     * 销售订单列表
     */
    @ApiModelProperty("销售订单列表")
    private List<SaleOrderDTO> saleOrderList;

    /**
     * 计量重量(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量重量(工单物料计划数量记录计算用)")
    private Double measurementQuantity;

    /**
     * 计量单位(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量单位(工单物料计划数量记录计算用)")
    private String measurementUnit;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String approver;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    private String actualApprover;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;

    /**
     * 审批建议
     */
    @ApiModelProperty("审批建议")
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 产前状态
     */
    @ApiModelProperty("产前状态")
    private Boolean prenatalStatus;

    /**
     * 工作中心ID
     */
    @ApiModelProperty("工作中心ID")
    private Integer workCenterId;

    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    private String workCenterName;

    /**
     * 供应商code
     */
    @ApiModelProperty("供应商code")
    private String supplierCode;

    /**
     * 供应商名字
     */
    @ApiModelProperty("供应商名字")
    private String supplierName;

    /**
     * 附件列表
     */
    @ApiModelProperty("附件列表")
    private List<WorkOrderAppendixDTO> appendixEntities;

    /**
     * 绑定的工艺工序列表
     */
    @ApiModelProperty("绑定的工艺工序列表")
    private List<CraftProcedureEntityDTO> craftProcedureEntities;

    /**
     * 数据导入时间
     */
    @ApiModelProperty("数据导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date importTime;

    /**
     * 班组ID
     */
    @ApiModelProperty("班组ID")
    private Integer teamId;

    /**
     * 工单投产班组成员列表
     */
    @ApiModelProperty("工单投产班组成员列表")
    private List<WorkOrderTeamEntity> workOrderTeamEntities;

    /**
     * 工单关联班组id
     */
    @ApiModelProperty("工单关联班组id")
    private List<Integer> relevanceTeamIds;

    /**
     * 工单关联设备id
     */
    @ApiModelProperty("工单关联设备id")
    private List<Integer> relevanceDeviceIds;

    /**
     * 设备ID
     */
    @ApiModelProperty("设备ID")
    private Integer deviceId;

    /**
     * 排产状态(0-不可排,1-待排产,2-已排产)
     */
    @ApiModelProperty("排产状态(0-不可排,1-待排产,2-已排产)")
    private Integer planState;

    /**
     * 排产顺序
     */
    @ApiModelProperty("排产顺序")
    private Integer planSequence;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    private Integer skuId;

    /**
     * 包装方案编码
     */
    @ApiModelProperty("包装方案编码")
    private String packageSchemeCode;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    private Double productCount;

    /**
     * 待排数量
     */
    @ApiModelProperty("待排数量")
    private Double pendentQuantity;

    /**
     * 流转数量
     */
    @ApiModelProperty("流转数量")
    private Double inStockCount;

    /**
     * 生产订单按工艺路线下推次数
     */
    @ApiModelProperty("生产订单按工艺路线下推次数")
    private Integer pushTimes;

    /**
     * 关联的销售订单物料行号
     */
    @ApiModelProperty("关联的销售订单物料行号")
    @TableField(exist = false)
    private Integer relatedSaleOrderMaterialLineNumber;

    /**
     * 关联的生产订单物料行号
     */
    @ApiModelProperty("关联的生产订单物料行号")
    @TableField(exist = false)
    private Integer relatedProductOrderMaterialLineNumber;

    /**
     * 领料状态
     */
    @ApiModelProperty("领料状态名称")
    private String pickingStateName;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    private String customerCode;

    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    private String customerMaterialCode;

    /**
     * 客户物料名称
     */
    @ApiModelProperty("客户物料名称")
    private String customerMaterialName;

    /**
     * 客户物料规格
     */
    @ApiModelProperty("客户物料规格")
    private String customerSpecification;

    /**
     * 工单拓展字段1
     */
    @ApiModelProperty(value = "工单拓展字段1")
    private String workOrderExtendFieldOne;
    /**
     * 工单拓展字段2
     */
    @ApiModelProperty(value = "工单拓展字段2")
    private String workOrderExtendFieldTwo;
    /**
     * 工单拓展字段3
     */
    @ApiModelProperty(value = "工单拓展字段3")
    private String workOrderExtendFieldThree;
    /**
     * 工单拓展字段4
     */
    @ApiModelProperty(value = "工单拓展字段4")
    private String workOrderExtendFieldFour;
    /**
     * 工单拓展字段5
     */
    @ApiModelProperty(value = "工单拓展字段5")
    private String workOrderExtendFieldFive;

    /**
     * 工单拓展字段6
     */
    @ApiModelProperty(value = "工单拓展字段6")
    @TableField(value = "work_order_extend_field_six")
    private String workOrderExtendFieldSix;
    /**
     * 工单拓展字段7
     */
    @ApiModelProperty(value = "工单拓展字段7")
    @TableField(value = "work_order_extend_field_seven")
    private String workOrderExtendFieldSeven;
    /**
     * 工单拓展字段8
     */
    @ApiModelProperty(value = "工单拓展字段8")
    @TableField(value = "work_order_extend_field_eight")
    private String workOrderExtendFieldEight;
    /**
     * 工单拓展字段9
     */
    @ApiModelProperty(value = "工单拓展字段9")
    @TableField(value = "work_order_extend_field_nine")
    private String workOrderExtendFieldNine;
    /**
     * 工单拓展字段10
     */
    @ApiModelProperty(value = "工单拓展字段10")
    @TableField(value = "work_order_extend_field_ten")
    private String workOrderExtendFieldTen;
    /**
     * 工单物料拓展字段1
     */
    @ApiModelProperty(value = "工单物料拓展字段1")
    @TableField(value = "work_order_material_extend_field_one")
    private String workOrderMaterialExtendFieldOne;
    /**
     * 工单物料拓展字段2
     */
    @ApiModelProperty(value = "工单物料拓展字段2")
    @TableField(value = "work_order_material_extend_field_two")
    private String workOrderMaterialExtendFieldTwo;
    /**
     * 工单物料拓展字段3
     */
    @ApiModelProperty(value = "工单物料拓展字段3")
    @TableField(value = "work_order_material_extend_field_three")
    private String workOrderMaterialExtendFieldThree;
    /**
     * 工单物料拓展字段4
     */
    @ApiModelProperty(value = "工单物料拓展字段4")
    @TableField(value = "work_order_material_extend_field_four")
    private String workOrderMaterialExtendFieldFour;
    /**
     * 工单物料拓展字段5
     */
    @ApiModelProperty(value = "工单物料拓展字段5")
    @TableField(value = "work_order_material_extend_field_five")
    private String workOrderMaterialExtendFieldFive;
    /**
     * 工单物料拓展字段6
     */
    @ApiModelProperty(value = "工单物料拓展字段6")
    @TableField(value = "work_order_material_extend_field_six")
    private String workOrderMaterialExtendFieldSix;
    /**
     * 工单物料拓展字段7
     */
    @ApiModelProperty(value = "工单物料拓展字段7")
    @TableField(value = "work_order_material_extend_field_seven")
    private String workOrderMaterialExtendFieldSeven;
    /**
     * 工单物料拓展字段8
     */
    @ApiModelProperty(value = "工单物料拓展字段8")
    @TableField(value = "work_order_material_extend_field_eight")
    private String workOrderMaterialExtendFieldEight;
    /**
     * 工单物料拓展字段9
     */
    @ApiModelProperty(value = "工单物料拓展字段9")
    @TableField(value = "work_order_material_extend_field_nine")
    private String workOrderMaterialExtendFieldNine;
    /**
     * 工单物料拓展字段10
     */
    @ApiModelProperty(value = "工单物料拓展字段10")
    @TableField(value = "work_order_material_extend_field_ten")
    private String workOrderMaterialExtendFieldTen;

    /**
     * 业务单元编码
     */
    @ApiModelProperty("业务单元编码")
    @TableField(value = "business_unit_code")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务单元名称")
    @TableField(value = "business_unit_name")
    private String businessUnitName;


}
