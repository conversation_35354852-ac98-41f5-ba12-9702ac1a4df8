package com.yelink.dfs.open.v1.workOrder.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.entity.order.WorkOrderTeamEntity;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021-03-17 11:59
 */

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WorkOrderUpdateDTO {

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id", required = true)
    @NotNull(message = "工单id不能为空")
    private Integer workOrderId;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", required = true)
    private String workOrderNumber;

    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    private String workOrderName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    private Date startDate;


    /**
     * 截止时间
     */
    @ApiModelProperty(value = "计划截止时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "截止时间不能为空")
    private Date endDate;

    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 状态
     * 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消
     */
    @ApiModelProperty(value = "状态 1-创建、2-发放、3-投入、4-挂起、5-完成、6-关闭、7-取消", required = true)
    @NotNull(message = "状态不能为空")
    private Integer state;

    /**
     * 派工状态
     */
    @ApiModelProperty("派工状态 toBeAssigned-待派工、assigned-已派工")
    private String assignmentState;

    /**
     * 存在项目合同 0 不存在 1 存在
     */
    @ApiModelProperty("存在项目合同")
    private Boolean projectContract;

    /**
     * 产线编号
     */
    @ApiModelProperty("产线编号")
    private String lineCode;

    /**
     * 产线名称
     */
    @ApiModelProperty("产线名称")
    private String lineName;

    /**
     * 产线id
     */
    @ApiModelProperty("产线id")
    private Integer lineId;

    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量", required = true)
    @NotNull(message = "计划数量不能为空")
    @UnitFormatColumn
    private Double planQuantity;

    /**
     * 计划批数
     */
    @ApiModelProperty("计划批数")
    private Double plannedBatches;

    /**
     * 每批计划数
     */
    @ApiModelProperty("每批计划数")
    private Double plansPerBatch;

    /**
     * 实际批数
     */
    @ApiModelProperty("实际批数")
    private Double actualBatches;

    /**
     * 关联销售订单的物料行ID
     */
    @ApiModelProperty("关联销售订单的物料行ID")
    private Integer relateOrderMaterialId;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号", required = true)
    @NotNull(message = "物料编号不能为空")
    @UnitColumn
    private String materialCode;

    /**
     * 不合格量
     */
    @ApiModelProperty("不合格量")
    @UnitFormatColumn
    private Double unqualified;
    /**
     * 子工单顺序
     */
    @ApiModelProperty("子工单顺序")
    private String idSequence;

    /**
     * 进度（完成率）
     */
    @ApiModelProperty("进度（完成率）")
    private Double progress;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 物料当前库存
     */
    @ApiModelProperty("物料当前库存")
    private Double currentInventory;
    /**
     * 物料是否齐套
     */
    @ApiModelProperty("物料是否齐套")
    private Boolean materialIsComplete;
    /**
     * 物料齐套数量
     */
    @ApiModelProperty("物料齐套数量")
    private Double materialCompleteNum;
    /**
     * 物料欠套数量
     */
    @ApiModelProperty("物料欠套数量")
    private Double materialOweNum;
    /**
     * 理论工作时长
     */
    @ApiModelProperty("理论工作时长")
    private Double theoreticalWorkingHours;

    /**
     * 类型（该类型用于指标）
     */
    @ApiModelProperty("类型（该类型用于指标）")
    @TableField(value = "type")
    private String type;

    /**
     * 单据类型
     */
    @ApiModelProperty(value = "单据类型", required = true, notes = "系统内置 1-正常工单 2-返回工单,dfs3.13版本支持自定义单据类型")
    private String orderType;

    /**
     * UpdateBy
     */
    @ApiModelProperty("UpdateBy")
    private String updateBy;

    /**
     * UpdateDate
     */
    @ApiModelProperty("UpdateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 有效工时
     */
    @ApiModelProperty("有效工时")
    private Double effectiveHours;

    /**
     * 计划员真实姓名
     */
    @ApiModelProperty("计划员真实姓名")
    private String magNickname;

    /**
     * 计划员手机号
     */
    @ApiModelProperty("计划员手机号")
    private String magPhone;

    /**
     * 计划员账号
     */
    @ApiModelProperty("计划员账号")
    private String magName;

    /**
     * 已完成数量
     */
    @ApiModelProperty("已完成数量")
    @UnitFormatColumn
    private Double finishCount;

    /**
     * 投入数量
     */
    @ApiModelProperty("投入数量")
    @UnitFormatColumn
    private Double inputTotal;

    /**
     * 工单接收人（工单完成时通知人员账号，逗号隔开）
     */
    @ApiModelProperty("工单接收人（工单完成时通知人员账号，逗号隔开）")
    private String noticeUsername;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级 正常、优先、加急、特急", required = true)
    private String priority;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    @TableField(value = "customer_code")
    private String customerCode;

    /**
     * 包装数
     */
    @ApiModelProperty("包装数")
    private Double packageQuantity;

    /**
     * 报工系数
     */
    @ApiModelProperty("报工系数")
    private Double coefficient;

    /**
     * 是否备料
     */
    @ApiModelProperty("是否备料")
    private Boolean prepared;

    /**
     * 是否发料
     */
    @ApiModelProperty("是否发料")
    private Boolean assigned;

    /**
     * erp关联单据编号
     */
    @ApiModelProperty("erp关联单据编号")
    private String erpDocumentCode;

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    private Boolean issue;

    /**
     * 工艺Id
     */
    @ApiModelProperty("工艺Id")
    private Integer craftId;
    /**
     * 工艺编号
     */
    @ApiModelProperty("工艺编号")
    private String craftCode;

    /**
     * 订单号，用逗号隔开
     */
    @ApiModelProperty("订单号，用逗号隔开")
    private String productOrderNumber;

    /**
     * 销售订单号，用逗号隔开
     */
    @ApiModelProperty("销售订单号，用逗号隔开")
    private String saleOrderNumber;

    /**
     * 生产订单列表
     */
    @ApiModelProperty("生产订单列表")
    private List<ProductOrderEntity> productOrderList;
    /**
     * 销售订单列表
     */
    @ApiModelProperty("销售订单列表")
    private List<SaleOrderEntity> saleOrderList;

    /**
     * 计量重量(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量重量(工单物料计划数量记录计算用)")
    private Double measurementQuantity;

    /**
     * 计量单位(工单物料计划数量记录计算用)
     */
    @ApiModelProperty("计量单位(工单物料计划数量记录计算用)")
    private String measurementUnit;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String approver;

    /**
     * 实际审批人
     */
    @ApiModelProperty("实际审批人")
    private String actualApprover;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;

    /**
     * 审批建议
     */
    @ApiModelProperty("审批建议")
    private String approvalSuggestion;

    /**
     * 审批时间
     */
    @ApiModelProperty("审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalTime;

    /**
     * 产前状态
     */
    @ApiModelProperty("产前状态")
    private Boolean prenatalStatus;

    /**
     * 计划工时
     */
    @ApiModelProperty("计划工时")
    private Double plannedWorkingHours;

    /**
     * 实际工时
     */
    @ApiModelProperty("实际工时")
    private Double actualWorkingHours;

    /**
     * 工单执行
     */
    @ApiModelProperty("工单执行")
    private String executionStatus;

    /**
     * 时差
     */
    @ApiModelProperty("时差")
    private Double timeDifference;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    @UnitFormatColumn
    private Double productCount;

    /**
     * 待排数量
     */
    @ApiModelProperty("待排数量")
    @UnitFormatColumn
    private Double pendentQuantity;

    /**
     * 流转数量
     */
    @ApiModelProperty("流转数量")
    @UnitFormatColumn
    private Double inStockCount;

    /**
     * 工作中心ID
     */
    @ApiModelProperty("工作中心ID")
    private Integer workCenterId;

    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    private String workCenterName;

    /**
     * 已领料数
     */
    @ApiModelProperty("已领料数")
    @UnitFormatColumn
    private Double pickingQuantity;

    /**
     * 已入库数
     */
    @ApiModelProperty("已入库数")
    @UnitFormatColumn
    private Double inventoryQuantity;

    /**
     * 供应商code
     */
    @ApiModelProperty("供应商code")
    private String supplierCode;

    /**
     * 供应商名字
     */
    @ApiModelProperty("供应商名字")
    private String supplierName;

    /**
     * 附件列表
     */
    @ApiModelProperty("附件列表")
    private List<WorkOrderAppendixDTO> appendixEntities;

    /**
     * 有效工时=报工数量/标准产能
     */
    @TableField(value = "effective_working_hour")
    private Double effectiveWorkingHour;

    /**
     * 计数器累计参考值
     */
    @TableField(value = "auto_count")
    private Double autoCount;

    /**
     * 绑定的工艺工序列表
     */
    @ApiModelProperty("绑定的工艺工序列表")
    private List<CraftProcedureEntityDTO> craftProcedureEntities;

    /**
     * 流转时长
     */
    @ApiModelProperty("流转时长")
    @TableField(value = "circulation_duration")
    private Double circulationDuration;

    /**
     * 数据导入时间
     */
    @ApiModelProperty("数据导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date importTime;

    /**
     * 状态变更时间
     */
    @ApiModelProperty("状态变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stateChangeTime;

    /**
     * 排产状态(0-不可排,1-待排产,2-已排产)
     */
    @ApiModelProperty("排产状态(0-不可排,1-待排产,2-已排产)")
    @TableField(value = "plan_state")
    private Integer planState;

    /**
     * 排程顺序
     */
    @ApiModelProperty("排程顺序")
    private Integer schedulingSequence;

    /**
     * 排程状态
     */
    @ApiModelProperty("排程状态")
    private Integer schedulingState;

    /**
     * 排程数量
     */
    @ApiModelProperty("排程数量")
    @UnitFormatColumn
    private Double schedulingCount;

    @ApiModelProperty("流转状态(0-不可流转,1-可流转,2-流转超时,3-已流转)")
    private Integer circulationState;

    /**
     * 班组ID
     */
    @ApiModelProperty("班组ID")
    private Integer teamId;

    /**
     * 生产基本单元id
     */
    private Integer productionBasicUnitId;

    /**
     * 隔离ID(工作中心ID+“-”+生产基本单元id)
     */
    private String isolationId;

    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    private String customerMaterialCode;

    /**
     * 客户物料名称
     */
    @ApiModelProperty("客户物料名称")
    private String customerMaterialName;

    /**
     * 客户物料规格
     */
    @ApiModelProperty("客户物料规格")
    private String customerSpecification;

    /**
     * 工单投产班组成员列表
     */
    @ApiModelProperty("工单投产班组成员列表")
    private List<WorkOrderTeamEntity> workOrderTeamEntities;

    /**
     * 工单关联班组id
     */
    private List<Integer> relevanceTeamIds;

    /**
     * 工单关联设备id
     */
    private List<Integer> relevanceDeviceIds;

    /**
     * 设备ID
     */
    @ApiModelProperty("设备ID")
    private Integer deviceId;

    /**
     * 排产顺序
     */
    @ApiModelProperty("排产顺序")
    private Integer planSequence;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    private Integer skuId;

    /**
     * 包装方案编码
     */
    @ApiModelProperty("包装方案编码")
    private String packageSchemeCode;

    /**
     * 生产订单按工艺路线下推次数
     */
    @ApiModelProperty("生产订单按工艺路线下推次数")
    private Integer pushTimes;

    /**
     * 关联的销售订单物料行号
     */
    @ApiModelProperty("关联的销售订单物料行号")
    private Integer relatedSaleOrderMaterialLineNumber;

    /**
     * 关联的生产订单物料行号
     */
    @ApiModelProperty("关联的生产订单物料行号")
    private Integer relatedProductOrderMaterialLineNumber;


    /**
     * 是否已打印
     */
    @ApiModelProperty("是否已打印")
    @TableField(value = "is_print")
    private Boolean isPrint;

    /**
     * 领料状态
     */
    @ApiModelProperty("领料状态名称")
    private String pickingStateName;

    /**
     * 上料防错类型
     */
    @ApiModelProperty(value = "上料防错类型")
    private String materialCheckType;

    /**
     * 上料防错是否支持替代料，0否1是
     */
    @ApiModelProperty("上料防错是否支持替代料")
    private Boolean materialCheckReplace;

    /**
     * 工单拓展字段1
     */
    @ApiModelProperty(value = "工单拓展字段1")
    private String workOrderExtendFieldOne;
    /**
     * 工单拓展字段2
     */
    @ApiModelProperty(value = "工单拓展字段2")
    private String workOrderExtendFieldTwo;
    /**
     * 工单拓展字段3
     */
    @ApiModelProperty(value = "工单拓展字段3")
    private String workOrderExtendFieldThree;
    /**
     * 工单拓展字段4
     */
    @ApiModelProperty(value = "工单拓展字段4")
    private String workOrderExtendFieldFour;
    /**
     * 工单拓展字段5
     */
    @ApiModelProperty(value = "工单拓展字段5")
    private String workOrderExtendFieldFive;

    /**
     * 工单拓展字段6
     */
    @ApiModelProperty(value = "工单拓展字段6")
    private String workOrderExtendFieldSix;
    /**
     * 工单拓展字段7
     */
    @ApiModelProperty(value = "工单拓展字段7")
    private String workOrderExtendFieldSeven;
    /**
     * 工单拓展字段8
     */
    @ApiModelProperty(value = "工单拓展字段8")
    private String workOrderExtendFieldEight;
    /**
     * 工单拓展字段9
     */
    @ApiModelProperty(value = "工单拓展字段9")
    private String workOrderExtendFieldNine;
    /**
     * 工单拓展字段10
     */
    @ApiModelProperty(value = "工单拓展字段10")
    private String workOrderExtendFieldTen;
    /**
     * 工单物料拓展字段1
     */
    @ApiModelProperty(value = "工单物料拓展字段1")
    private String workOrderMaterialExtendFieldOne;
    /**
     * 工单物料拓展字段2
     */
    @ApiModelProperty(value = "工单物料拓展字段2")
    private String workOrderMaterialExtendFieldTwo;
    /**
     * 工单物料拓展字段3
     */
    @ApiModelProperty(value = "工单物料拓展字段3")
    private String workOrderMaterialExtendFieldThree;
    /**
     * 工单物料拓展字段4
     */
    @ApiModelProperty(value = "工单物料拓展字段4")
    private String workOrderMaterialExtendFieldFour;
    /**
     * 工单物料拓展字段5
     */
    @ApiModelProperty(value = "工单物料拓展字段5")
    private String workOrderMaterialExtendFieldFive;
    /**
     * 工单物料拓展字段6
     */
    @ApiModelProperty(value = "工单物料拓展字段6")
    private String workOrderMaterialExtendFieldSix;
    /**
     * 工单物料拓展字段7
     */
    @ApiModelProperty(value = "工单物料拓展字段7")
    private String workOrderMaterialExtendFieldSeven;
    /**
     * 工单物料拓展字段8
     */
    @ApiModelProperty(value = "工单物料拓展字段8")
    private String workOrderMaterialExtendFieldEight;
    /**
     * 工单物料拓展字段9
     */
    @ApiModelProperty(value = "工单物料拓展字段9")
    private String workOrderMaterialExtendFieldNine;
    /**
     * 工单物料拓展字段10
     */
    @ApiModelProperty(value = "工单物料拓展字段10")
    private String workOrderMaterialExtendFieldTen;

    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    private String businessUnitCode;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    private String businessUnitName;

    /**
     * 投产检查结果（true--通过  false--不通过  null--空）
     */
    @ApiModelProperty(value = "投产检查结果（true--通过  false--不通过  null--空）")
    private Boolean investCheckResult;

    /**
     * 原工单号
     */
    @ApiModelProperty("原工单号")
    private String originalWorkOrderNumber;

}
