package com.yelink.dfs.open.v2.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.entity.model.dto.AutoCountDTO;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.target.record.dto.CheckReportBackDTO;
import com.yelink.dfs.open.v2.order.dto.AutoCountQueryDTO;
import com.yelink.dfs.open.v2.order.dto.CheckReportV2DTO;
import com.yelink.dfs.open.v2.order.dto.ReportLineDTO;
import com.yelink.dfs.open.v2.order.dto.ReportQueryDTO;
import com.yelink.dfs.service.target.record.ReportCountService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfscommon.common.unit.config.UnitFormatMethod;
import com.yelink.dfscommon.entity.dfs.CheckReportDTO;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.swagger.annotation.CustomApiOperation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Description: 工单报工记录接口V2
 * @Author: zhuangwq
 * @Date: 2022/9/16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v2/open/report")
@Api(tags = {"任务中心/V2/工单报工记录接口"})
public class ReportOpenV2Controller extends BaseController {

    private ReportLineService reportLineService;
    private final ReportCountService reportCountService;

    /**
     * 查询报工记录列表
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "查询报工记录列表", notes = "查询报工记录列表")
    @PostMapping("/list")
    @UnitFormatMethod(value = UnitFormatMethod.MethodType.RESPONSE)
    public Result<PageResult<ReportLineDTO>> list(@RequestBody ReportQueryDTO dto) {
        Page<ReportLineEntity> page = reportLineService.getList(dto);
        return Result.success(page, ReportLineDTO.class);
    }

    /**
     * 查询自动报工数量
     *
     * @param dto
     * @return
     */
    @CustomApiOperation(msgCodes = {"9007"})
    @ApiOperation(value = "查询自动报工数量", notes = "查询自动报工数量")
    @PostMapping("/auto/report/count")
    public Result<AutoCountDTO> getAutoCount(@RequestBody @Validated AutoCountQueryDTO dto) {
        return Result.success(reportCountService.getAutoCountAndDate(dto.getWorkOrderNumber(), null, null, null, null));
    }

    @ApiOperation(value = "报工前检查", httpMethod = "POST")
    @PostMapping("/check/report")
    public Result<CheckReportBackDTO> checkReport(@RequestBody CheckReportV2DTO dto) {
        return Result.success(reportCountService.checkReport(JacksonUtil.convertObject(dto, CheckReportDTO.class)));
    }
}
