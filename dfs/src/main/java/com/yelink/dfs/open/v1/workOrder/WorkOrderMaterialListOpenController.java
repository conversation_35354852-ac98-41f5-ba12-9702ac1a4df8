package com.yelink.dfs.open.v1.workOrder;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yelink.dfs.common.BaseController;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderMaterialListInsertDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderMaterialListUpdateDTO;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfscommon.common.unit.config.UnitFormatMethod;
import com.yelink.dfscommon.constant.ParamException;
import com.yelink.dfscommon.dto.ams.open.order.MaterialListCodeDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.MaterialListQuantityParam;
import com.yelink.dfscommon.entity.ams.dto.MaterialListSelectDTO;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.pojo.message.KafkaMessageResult;
import com.yelink.dfscommon.pojo.message.NewMessageEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 生产工单用料清单对外接口
 *
 * <AUTHOR>
 * @Date 2022/10/19 21:33
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v1/open/work_order/material_lists")
@Api(tags = "任务中心/V1/生产工单用料清单接口")
public class WorkOrderMaterialListOpenController extends BaseController {

    private WorkOrderMaterialListService materialListService;

    /**
     * 查询生产工单用料清单列表（可分页查询）
     */
    @ApiOperation(value = "查询生产工单用料清单列表（可分页查询）")
    @PostMapping("/list")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public Result<PageResult<WorkOrderMaterialListEntity>> getList(@RequestBody MaterialListSelectDTO materialListSelectDTO) {
        Page<WorkOrderMaterialListEntity> materialListPage = materialListService.getList(materialListSelectDTO);
        return Result.success(materialListPage);
    }

    /**
     * 新增生产工单用料清单
     *
     * @param insertDTO
     * @return
     */
    @ApiOperation(value = "新增生产工单用料清单",
            notes = "返回状态码说明：\n" +
                    "1258： 生产工单用料清单编号已存在")
    @PostMapping("/add")
    @UnitFormatMethod(UnitFormatMethod.MethodType.REQUEST)
    public Result<Integer> add(@RequestBody @Validated WorkOrderMaterialListInsertDTO insertDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        // 将对外接口的参数转换成系统中需要的参数
        WorkOrderMaterialListEntity entity = JSON.parseObject(JSON.toJSONString(insertDTO), WorkOrderMaterialListEntity.class);
        materialListService.add(entity);
        return Result.success(entity.getMaterialListId());
    }

    /**
     * 新增生效的生产东单用料清单
     *
     * @param
     * @return
     */
    @ApiOperation(value = "新增生效的生产工单用料清单",
            notes = "返回状态码说明：\n" +
                    "1258： 生产工单用料清单编号已存在" +
                    "9550： 单据未经审批")
    @PostMapping("/add/released")
    @UnitFormatMethod(UnitFormatMethod.MethodType.REQUEST)
    public Result<Integer> addReleasedOrder(@RequestBody @Validated WorkOrderMaterialListInsertDTO insertDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        // 将对外接口的参数转换成系统中需要的参数
        WorkOrderMaterialListEntity entity = JSON.parseObject(JSON.toJSONString(insertDTO), WorkOrderMaterialListEntity.class);
        materialListService.addReleasedOrder(entity);
        return Result.success(entity.getMaterialListId());
    }


    /**
     * 更新生产工单用料清单
     *
     * @param
     * @return
     */
    @ApiOperation(value = "更新生产工单用料清单")
    @PutMapping("/update")
    @UnitFormatMethod(UnitFormatMethod.MethodType.REQUEST)
    public Result update(@RequestBody @Validated WorkOrderMaterialListUpdateDTO updateDTO, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ParamException(bindingResult);
        }
        // 将对外接口的参数转换成系统中需要的参数
        WorkOrderMaterialListEntity entity = JSON.parseObject(JSON.toJSONString(updateDTO), WorkOrderMaterialListEntity.class);
        materialListService.update(entity);
        return Result.success();
    }

    /**
     * 删除生产工单用料清单
     *
     * @param
     * @return
     */
    @ApiImplicitParam(dataType = "int", name = "materialListId", required = true, value = "生产工单用料清单ID")
    @ApiOperation(value = "删除生产工单用料清单")
    @DeleteMapping("/delete")
    public Result delete(@RequestParam(value = "materialListId") Integer materialListId) {
        materialListService.delete(materialListId);
        return Result.success();
    }

    /**
     * 通过id查询生产工单用料清单详情
     *
     * @param
     * @return
     */
    @ApiImplicitParam(dataType = "int", name = "materialListId", required = true, value = "生产工单用料清单ID")
    @ApiOperation(value = "通过id查询生产工单用料清单详情")
    @GetMapping("/detail/{materialListId}")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public Result<WorkOrderMaterialListEntity> detailById(@PathVariable(value = "materialListId") Integer materialListId) {
        return Result.success(materialListService.detail(materialListId));
    }

    /**
     * 通过编号查询生产工单用料清单详情
     *
     * @param
     * @return
     */
    @ApiOperation(value = "通过编号查询生产工单用料清单详情")
    @PostMapping("/detail")
    @UnitFormatMethod(UnitFormatMethod.MethodType.RESPONSE)
    public Result<WorkOrderMaterialListEntity> detailByCode(@RequestBody MaterialListCodeDetailDTO param) {
        WorkOrderMaterialListEntity materialListEntity = materialListService.lambdaQuery().eq(WorkOrderMaterialListEntity::getMaterialListCode, param.getMaterialListCode()).one();
        if (materialListEntity == null) {
            return Result.success();
        }
        return Result.success(materialListService.detail(materialListEntity.getMaterialListId()));
    }

    /**
     * 更新生产工单用料清单的相关数量
     *
     * @param
     * @return
     */
    @ApiOperation(value = "更新生产工单用料清单的相关数量")
    @PostMapping("/update/quantity")
    public Result updateQuantity(@RequestBody MaterialListQuantityParam param) {
        materialListService.updateQuantity(param);
        return Result.success();
    }

    /**
     * kafka消息说明（返回参数说明，非接口）
     *
     * @return
     */
    @ApiOperation(value = "kafka消息说明（返回参数说明，非接口）", notes = "kafka消息method参数说明: 无"
    )
    @GetMapping("/kafka/message")
    public KafkaMessageResult<NewMessageEntity<WorkOrderMaterialListEntity>> message() {
        return KafkaMessageResult.success();
    }


}
