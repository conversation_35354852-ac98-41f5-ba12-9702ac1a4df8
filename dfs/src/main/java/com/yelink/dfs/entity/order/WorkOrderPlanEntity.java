package com.yelink.dfs.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.config.unit.WorkOrder2MaterialFormatter;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfscommon.annotation.LogTag;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021-07-20 19:08
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@LogTag(bean = "workOrderPlanServiceImpl", method = "getById", param = "id")
@TableName("dfs_work_order_plan")
public class WorkOrderPlanEntity extends Model<WorkOrderPlanEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 工单号
     */
    @TableField(value = "work_order_number")
    @UnitColumn(formatClass = WorkOrder2MaterialFormatter.class)
    private String workOrderNumber;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "time")
    private Date time;

    /**
     * 排产数量
     */
    @TableField(value = "plan_quantity")
    @UnitFormatColumn
    private Double planQuantity;

    @TableField("production_basic_unit_type")
    private String productionBasicUnitType;

    @TableField("production_basic_unit_id")
    private Integer productionBasicUnitId;

    @TableField("production_basic_unit_name")
    private String productionBasicUnitName;

    /**
     * CreateBy
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * CreateDate
     */
    @TableField(value = "create_date")
    private Date createDate;

    /**
     * UpdateBy
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * UpdateDate
     */
    @TableField(value = "update_date")
    private Date updateDate;

    /**
     * 是否主生产基本单元
     */
    @TableField(value = "is_main")
    private Boolean isMain;

    @TableField(exist = false)
    private Double finishCount;

    @TableField(exist = false)
    private Double achievementRate;

    public interface Insert {
    }

    public interface Update {
    }


    // 由于业务上 work_order_number+production_basic_unit 本身存在 主资源与关联资源的互斥性， 故is_main不再插入uni_code

    public String buildUniCode() {
        return workOrderNumber
                + Constant.UNDERLINE + productionBasicUnitType
                + Constant.UNDERLINE + productionBasicUnitId
                + Constant.UNDERLINE + DateUtil.dateStr(time)
                ;
    }

    public String buildBasicCode() {
        return workOrderNumber
                + Constant.UNDERLINE + productionBasicUnitType
                + Constant.UNDERLINE + productionBasicUnitId
                ;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
