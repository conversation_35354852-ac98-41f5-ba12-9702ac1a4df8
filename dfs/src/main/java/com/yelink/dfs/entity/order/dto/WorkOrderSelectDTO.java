package com.yelink.dfs.entity.order.dto;

import com.yelink.dfs.entity.capacity.dto.ProductionResourceDTO;
import com.yelink.dfs.entity.product.dto.MaterialEntitySelectDTO;
import com.yelink.dfs.utils.CommonUtil;
import com.yelink.dfscommon.common.sort.BaseLimitCountPageDTO;
import com.yelink.dfscommon.common.sort.SortDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuSelectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用于工单查询条件的对象
 *
 * <AUTHOR>
 * @Date 2021/12/10 11:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderSelectDTO extends BaseLimitCountPageDTO {

    /**
     * 是否仅查询简单的信息（不包含关联的数据）
     *
     * @param
     * @return
     */
    @ApiModelProperty("是否仅查询简单的信息（不包含关联的数据）")
    private Boolean isShowSimpleInfo;

    /**
     * 查询关联的工艺工序列表（不填/true，则不查出）
     *
     * @param
     * @return
     */
    @ApiModelProperty("是否查询关联的工艺工序列表（不填/true，则查出）")
    private Boolean isShowCraftProcedureInfo;

    /**
     * 是否查询用户相关信息（不填/true，则查出）
     *
     * @param
     * @return
     */
    @ApiModelProperty("查询用户相关信息（不填/true，则查出）")
    private Boolean isShowUserInfo;

    /**
     * 是否全选打印（不填/false，则为否，true则为是）
     *
     * @param
     * @return
     */
    @ApiModelProperty("是否全选打印（不填/false，则为否，true则为是）")
    private Boolean isSelectAllPrint;

    private String projectDefineName;

    private String contractName;

    /**
     * 是否查询关联的包装方案信息（不填/true，则查出）
     *
     * @param
     * @return
     */
    @ApiModelProperty("是否查询关联的包装方案信息（不填/true，则查出）")
    private Boolean isShowPackageSchemeInfo;

    /**
     * 是否查询关联的物料全量信息（不填/true，则查出）
     *
     * @param
     * @return
     */
    @ApiModelProperty("是否查询关联的物料全量信息（不填/true，则查出）")
    private Boolean isShowMaterialFieldInfo;

    /**
     * 标签规则id
     */
    @ApiModelProperty(value = "标签规则id")
    private Integer ruleId;
    /**
     * 工单id
     */
    @ApiModelProperty("工单id")
    private List<Integer> workOrderIds;
    /**
     * 工单编号
     */
    @ApiModelProperty("工单编号")
    private String workOrderNumber;
    /**
     * 多个工单编号
     */
    @ApiModelProperty("多个工单编号")
    private List<String> workOrderNumbers;
    /**
     * 工单名称
     */
    @ApiModelProperty("工单名称")
    private String workOrderName;
    /**
     * 是否为父工单
     */
    @ApiModelProperty("是否为父工单")
    private Boolean isParent;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String state;
    /**
     * 派工状态（toBeAssigned  未派工  assigned 已派工）
     */
    @ApiModelProperty("派工状态")
    private String assignmentState;

    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型")
    private String orderType;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private String businessType;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private String states;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private String approvalStatus;
    /**
     * 审批人
     */
    @ApiModelProperty("审批人")
    private String approver;
    /**
     * 创建开始时间
     */
    @ApiModelProperty("创建开始时间")
    private String startTime;
    /**
     * 创建结束时间
     */
    @ApiModelProperty("创建结束时间")
    private String endTime;
    /**
     * 计划开始时间
     */
    @ApiModelProperty("计划开始时间")
    private String planStartTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划结束时间")
    private String planEndTime;
    /**
     * 实际开始时间
     */
    @ApiModelProperty("实际开始时间")
    private String actualStartTime;
    /**
     * 实际结束时间
     */
    @ApiModelProperty("实际结束时间")
    private String actualEndTime;
    /**
     * 计划完成开始时间
     */
    @ApiModelProperty("计划完成开始时间")
    private String planCompleteStartTime;
    /**
     * 计划完成结束时间
     */
    @ApiModelProperty("计划完成结束时间")
    private String planCompleteEndTime;
    /**
     * 实际完成开始时间
     */
    @ApiModelProperty("实际完成开始时间")
    private String actualCompleteStartTime;
    /**
     * 实际完成结束时间
     */
    @ApiModelProperty("实际完成结束时间")
    private String actualCompleteEndTime;
    /**
     * 产线id
     */
    @ApiModelProperty("产线id")
    private String lineId;
    /**
     * 产线编号
     */
    @ApiModelProperty("产线编号 (多个逗号分割)")
    private String lineCodes;
    /**
     * 制造单元类型id(多个逗号分割)
     */
    @ApiModelProperty("制造单元类型id(多个逗号分割)")
    private String lineModelId;
    /**
     * 工作中心id (多个逗号分割)
     */
    @ApiModelProperty("工作中心id (多个逗号分割)")
    private String workCenterId;

    /**
     * 工序id(多个逗号分割)
     */
    @ApiModelProperty("工序id(多个逗号分割)")
    private String procedureIds;
    private String procedureIds2;

    /**
     * 生产基本单元id
     */
    @ApiModelProperty("生产基本单元id")
    private Integer productionBasicUnitId;

    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer current;
    /**
     * 分页参数
     */
    @ApiModelProperty("分页参数")
    private Integer size;

    /**
     * 销售订单编号
     */
    @ApiModelProperty("销售订单编号（模糊匹配）")
    private String saleOrderNumber;

    /**
     * 销售订单编号
     */
    @ApiModelProperty("销售订单编号（精准匹配）")
    private String fullSaleOrderNumber;

    /**
     * 执行状态
     */
    @ApiModelProperty("执行状态")
    private String executionStatus;

    /**
     * 更新开始时间
     */
    @ApiModelProperty("更新开始时间")
    private String updateStartTime;
    /**
     * 更新结束时间
     */
    @ApiModelProperty("更新结束时间")
    private String updateEndTime;

    /**
     * 区间开始时间
     */
    @ApiModelProperty("区间开始时间")
    private String intervalStartTime;
    /**
     * 区间结束时间
     */
    @ApiModelProperty("区间结束时间")
    private String intervalEndTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 进度（完成率）
     */
    @ApiModelProperty("进度（完成率）")
    private String completionRate;

    private List<SortDTO> sortList;

    /**
     * 生产订单编号
     */
    @ApiModelProperty("生产订单编号（模糊匹配）")
    private String productOrderNumber;

    /**
     * 多个生产订单id
     */
    @ApiModelProperty("多个生产订单id")
    private List<Integer> productOrderIds;

    /**
     * 订单计划时间开始
     */
    @ApiModelProperty("订单计划时间开始")
    private String planProductEndTimeStart;

    /**
     * 订单计划时间结束
     */
    @ApiModelProperty("订单计划时间结束")
    private String planProductEndTimeEnd;

    /**
     * 工序名称
     */
    @ApiModelProperty("工序名称，支持模糊查询")
    private String procedureName;

    /**
     * 工序名称
     */
    @ApiModelProperty("工序名称，多个工序按英文逗号分隔")
    private String procedureNames;

    /**
     * 工序编码
     */
    @ApiModelProperty("工序编码")
    private String procedureCode;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    private MaterialEntitySelectDTO materialFields;

    /**
     * 是否支持作业工单
     */
    @ApiModelProperty("是否支持作业工单")
    private Boolean isAssignment;

    /**
     * 工作中心基本生产单元类型
     */
    @ApiModelProperty("工作中心基本生产单元类型")
    private String workCenterType;

    /**
     * 排产状态(0-不可排,1-待排产,2-已排产)
     */
    @ApiModelProperty("排产状态(0-不可排,1-待排产,2-已排产)")
    private Integer planState;

    /**
     * 排程状态
     */
    @ApiModelProperty("排程状态")
    private Integer schedulingState;

    /**
     * 排程顺序
     */
    @ApiModelProperty("排程顺序")
    private Integer schedulingSequence;

    /**
     * 物料特征参数
     */
    @ApiModelProperty("物料特征参数")
    private List<MaterialSkuSelectDTO> materialSkus;

    /**
     * 班组id（逗号隔开）
     */
    @ApiModelProperty("班组id（逗号隔开）")
    private String teamIds;

    /**
     * 班组成员账号
     */
    @ApiModelProperty("班组成员账号")
    private String memberName;

    @ApiModelProperty("是否已打印")
    private Boolean isPrint;

    @ApiModelProperty("工艺工序id")
    private List<Integer> craftProcedureIds;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Integer deviceId;

    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码s")
    private String deviceCode;
    /**
     * 设备编码
     */
    @ApiModelProperty("设备编码，多个按英文逗号分隔")
    private String deviceCodes;
    /**
     * 导出模板的id
     */
    private Integer templateId;

    /**
     * 上料防错类型
     */
    private String materialCheckType;

    /**
     * 上料防错是否支持替代料，0否1是
     */
    private Boolean materialCheckReplace;

    /**
     * 排除的状态
     */
    private String excludeStates;

    /**
     * 领料状态名称
     */
    private String pickingStateName;

    /**
     * 关联资源id，多个按英文逗号分隔
     */
    private String relevanceResourceIds;
    /**
     * 关联资源类型(line-制造单元 team-班组 device-设备)
     */
    private String relevanceResourceType;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    private String customerCode;


    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    private String customerMaterialCode;

    /**
     * 客户物料名称
     */
    @ApiModelProperty("客户物料名称")
    private String customerMaterialName;

    /**
     * 客户物料规格
     */
    @ApiModelProperty("客户物料规格")
    private String customerSpecification;

    /**
     * 投产检查结果(true--通过  false--不通过)
     */
    @ApiModelProperty("投产检查结果(true--通过  false--不通过)")
    private Boolean investCheckResult;

    /**
     * 工单拓展字段1
     */
    @ApiModelProperty(value = "工单拓展字段1")
    private String workOrderExtendFieldOne;
    /**
     * 工单拓展字段2
     */
    @ApiModelProperty(value = "工单拓展字段2")
    private String workOrderExtendFieldTwo;
    /**
     * 工单拓展字段3
     */
    @ApiModelProperty(value = "工单拓展字段3")
    private String workOrderExtendFieldThree;
    /**
     * 工单拓展字段4
     */
    @ApiModelProperty(value = "工单拓展字段4")
    private String workOrderExtendFieldFour;
    /**
     * 工单拓展字段5
     */
    @ApiModelProperty(value = "工单拓展字段5")
    private String workOrderExtendFieldFive;

    /**
     * 工单拓展字段6
     */
    @ApiModelProperty(value = "工单拓展字段6")
    private String workOrderExtendFieldSix;
    /**
     * 工单拓展字段7
     */
    @ApiModelProperty(value = "工单拓展字段7")
    private String workOrderExtendFieldSeven;
    /**
     * 工单拓展字段8
     */
    @ApiModelProperty(value = "工单拓展字段8")
    private String workOrderExtendFieldEight;
    /**
     * 工单拓展字段9
     */
    @ApiModelProperty(value = "工单拓展字段9")
    private String workOrderExtendFieldNine;
    /**
     * 工单拓展字段10
     */
    @ApiModelProperty(value = "工单拓展字段10")
    private String workOrderExtendFieldTen;
    /**
     * 工单物料拓展字段1
     */
    @ApiModelProperty(value = "工单物料拓展字段1")
    private String workOrderMaterialExtendFieldOne;
    /**
     * 工单物料拓展字段2
     */
    @ApiModelProperty(value = "工单物料拓展字段2")
    private String workOrderMaterialExtendFieldTwo;
    /**
     * 工单物料拓展字段3
     */
    @ApiModelProperty(value = "工单物料拓展字段3")
    private String workOrderMaterialExtendFieldThree;
    /**
     * 工单物料拓展字段4
     */
    @ApiModelProperty(value = "工单物料拓展字段4")
    private String workOrderMaterialExtendFieldFour;
    /**
     * 工单物料拓展字段5
     */
    @ApiModelProperty(value = "工单物料拓展字段5")
    private String workOrderMaterialExtendFieldFive;
    /**
     * 工单物料拓展字段6
     */
    @ApiModelProperty(value = "工单物料拓展字段6")
    private String workOrderMaterialExtendFieldSix;
    /**
     * 工单物料拓展字段7
     */
    @ApiModelProperty(value = "工单物料拓展字段7")
    private String workOrderMaterialExtendFieldSeven;
    /**
     * 工单物料拓展字段8
     */
    @ApiModelProperty(value = "工单物料拓展字段8")
    private String workOrderMaterialExtendFieldEight;
    /**
     * 工单物料拓展字段9
     */
    @ApiModelProperty(value = "工单物料拓展字段9")
    private String workOrderMaterialExtendFieldNine;
    /**
     * 工单物料拓展字段10
     */
    @ApiModelProperty(value = "工单物料拓展字段10")
    private String workOrderMaterialExtendFieldTen;

    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    private String businessUnitCode;

    /**
     * 业务主体编码
     */
    @ApiModelProperty("业务主体编码")
    private List<String> businessUnitCodeList;
    /**
     * 业务主体名称
     */
    @ApiModelProperty("业务主体名称")
    private String businessUnitName;

    /**
     * 是否预览
     */
    @ApiModelProperty("是否预览")
    private Boolean isPreview;

    /**
     * 是否查询关联资源信息（true则查出，false或者空则不查）
     */
    @ApiModelProperty("是否查询关联资源信息（true则查出，false或者空则不查）")
    private Boolean isShowAssociatedResourcesInfo;

    @ApiModelProperty("是否查询直通信息（true则查出，false或者空则不查）")
    private Boolean isShowDirectAccessInfo;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String createName;

    /**
     * 更新人名称
     */
    @ApiModelProperty("更新人名称")
    private String updateName;

    @ApiModelProperty("是否查询工单报工相关")
    private Boolean isShowReportLineInfo;

    @ApiModelProperty("是否查询下推相关")
    private Boolean isShowPushDownInfo;

    @ApiModelProperty("是否查询批次相关")
    private Boolean isShowBarCodeInfo;

    @ApiModelProperty("隐藏创建态")
    private Boolean isHiddenCreatedState;

    @ApiModelProperty("投产的生产基本单元列表")
    private List<ProductionResourceDTO> investBasicUnits;

    /**
     * 下推状态
     */
    @ApiModelProperty("下推状态(noPushDown-未下推 partPushDown-部分下推 allPushDown-已下推)，用于查询对应下推状态的工单")
    private String pushDownState;

    /**
     * 目标单据类型
     */
    @ApiModelProperty("目标单据类型，配合下推状态使用，用于查询特定目标单据类型的下推状态工单")
    private String targetOrderType;

    /**
     * 工序名称 + 工序别名 混合
     * @return 所有查的工序ids
     */
    public String getFinalProcedureIds() {
        return CommonUtil.combineIdStr(procedureIds, procedureIds2);
    }
}
