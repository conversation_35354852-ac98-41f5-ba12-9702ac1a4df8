package com.yelink.dfs.entity.common.config.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 业务配置-业务单元全局配置
 *
 * <AUTHOR> @Date 2023/02/01 18:12
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PushDownIdentifierConfDTO implements Serializable {

    /**
     * 未下推颜色
     */
    private String noPushDown;

    /**
     * 部分下推颜色
     */
    private String partPushDown;

    /**
     * 已下推颜色
     */
    private String allPushDown;

}
