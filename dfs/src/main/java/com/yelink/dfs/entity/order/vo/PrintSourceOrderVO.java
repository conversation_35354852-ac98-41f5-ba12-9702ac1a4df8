package com.yelink.dfs.entity.order.vo;

import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 标签打印的数据源单据
 * @author: zhuangwq
 * @create: 2024-02-27 10:49
 **/
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PrintSourceOrderVO {

    /**
     * 单据号
     */
    private String orderNumber;
    /**
     * 计划数
     */
    @UnitFormatColumn
    private Double planQuantity;

    @UnitColumn
    private String materialCode;

}
