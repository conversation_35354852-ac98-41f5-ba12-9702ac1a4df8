package com.yelink.dfs.entity.screen.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.config.unit.WorkOrder2MaterialFormatter;
import com.yelink.dfs.entity.order.WorkOrderSubcontractEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 产线情况返回实体
 * @time 2021/7/21 19:27
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class LineSituationDTO {
    /**
     * 工单id
     */
    private Integer workOrderId;

    /**
     * 工单号
     */
    @UnitColumn(formatClass = WorkOrder2MaterialFormatter.class)
    private String workOrderNumber;
    /**
     * 工单名称
     */
    private String workOrderName;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 产线id
     */
    private Integer lineId;

    /**
     * 产线名字
     */
    private String lineName;

    /**
     * 产线模型Id
     */
    private Integer lineModelId;

    /**
     * 计划数量
     */
    @UnitFormatColumn
    private Double planQuantity;

    /**
     * 完成数量
     */
    @UnitFormatColumn
    private Double finishCount;

    /**
     * 合格率
     */
    private Double yield;

    /**
     *
     */
    private String name;

    /**
     * 不良数量
     */
    @UnitFormatColumn
    private Double unqualified;

    /**
     * 进度
     */
    private Double schedule;

    /**
     * 工单状态
     */
    private Integer state;

    /**
     * 工单状态中文
     */
    private String stateName;

    /**
     * 是否能设置当天计划值
     */
    private Boolean setPlan;

    /**
     * 工位信息列表
     */
    private List<FacilitiesDTO> facilitiesDTOList;

    /**
     * 是否正在计数
     */
    private Boolean isCountNow = false;


    /**
     * 产线code
     */
    private String lineCode;

    /**
     * 报工系数
     */
    private Double coefficient;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 工单备注
     */
    private String remark;

    private MaterialEntity materialFields;

    private Integer skuId;
    /**
     * 工作中心Id
     */
    private Integer workCenterId;

    /**
     * 工作中心名称
     */
    private String workCenterName;

    /**
     * 工作中心类型
     */
    private String workCenterType;
    /**
     * 工作中心关联资源类型
     */
    private String workCenterRelevanceType;

    /**
     * 设备id
     */
    private Integer deviceId;
    /**
     * 设备code
     */
    private String deviceCode;
    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 班组id
     */
    private Integer teamId;
    /**
     * 班组编号
     */
    private String teamCode;
    /**
     * 班组名称
     */
    private String teamName;

    /**
     * 工序名称
     */
    private String procedureName;

    /**
     * 派工状态名称
     */
    private String assignmentStateName;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 销售订单
     */
    private SaleOrderVO saleOrderVO;

    /**
     * 生产订单
     */
    private ProductOrderEntity productOrderEntity;

    /**
     * 计数器累计参考值
     */
    private Double autoCount;


    /**
     * 单件理论工时
     */
    private Double theoryHour;

    private Boolean isSubcontract;

    private List<WorkOrderSubcontractEntity> workOrderSubcontracts;
    private SkuEntity sku;

    /**
     * 项目定义ID
     */
    @ApiModelProperty("项目定义ID")
    @TableField(exist = false)
    private String projectDefineId;

    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectDefineName;

}
