package com.yelink.dfs.entity.barcode;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.open.v1.barcode.dto.BarCodeSimpleDTO;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.dto.dfs.InspectPrintDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2021-08-05 15:34
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("dfs_bar_code")
public class BarCodeEntity extends Model<BarCodeEntity> {

    private static final long serialVersionUID = 1L;

    /**
     * BarCodeId
     */
    @ApiModelProperty("BarCodeId")
    @TableId(value = "bar_code_id", type = IdType.AUTO)
    private Integer barCodeId;

    /**
     * 条码号/标签号
     */
    @ApiModelProperty("条码号/标签号")
    @TableField(value = "bar_code")
    private String barCode;

    /**
     * 关联单据类型
     */
    @TableField(value = "rule_type")
    private String ruleType;

    /**
     * 关联单据类型名字（不同类型关联不同单号）
     */
    @ApiModelProperty("关联单据类型名字（不同类型关联不同单号）")
    @TableField(exist = false)
    private String ruleTypeName;

    /**
     * 关联单号
     */
    @ApiModelProperty("关联单号")
    @TableField(value = "relate_number")
    private String relateNumber;

    /**
     * 关联物料行Id,用于区分同一个单据中出现的相同物料
     */
    @ApiModelProperty("关联物料行Id")
    @TableField(value = "relate_material_id")
    private Integer relateMaterialId;

    /**
     * 物料编号
     */
    @ApiModelProperty("物料编号")
    @TableField(value = "material_code")
    @UnitColumn
    private String materialCode;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @TableField(value = "state")
    private Integer state;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @TableField(value = "count")
    @UnitFormatColumn
    private Double count;

    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    @TableField(exist = false)
    private Double batchCount;

    /**
     * 条码组成信息
     */
    @ApiModelProperty("条码组成信息")
    @TableField(value = "code_info")
    private String codeInfo;

    /**
     * 打印时间
     */
    @ApiModelProperty("打印时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "print_time")
    private Date printTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    @TableField(value = "supplier")
    private String supplier;

    /**
     * 扩展字段
     */
    @ApiModelProperty("扩展字段，此字段仅供内部dfs使用，如需使用请使用带barCodeExtendField前缀的字段")
    @TableField(value = "extend_fields")
    private String extendFields;

    /**
     * 批次扩展字段1
     */
    @ApiModelProperty("批次扩展字段1")
    @TableField(value = "bar_code_extend_field_one")
    private String barCodeExtendFieldOne;

    /**
     * 新增批次时所属的模块 ()
     * {@link BarCodeTypeEnum}
     */
    @ApiModelProperty(value = "新增批次时所属的模块\n" +
            "finished--生产批次号\n" +
            "purchase--采购批次号\n" +
            "productBarCode--订单批次号\n" +
            "subcontractingReceiptBatch--委外收货批次号" +
            "productFlowCode--生产流水码\n" +
            "finishedProductCode--生产成品码\n" +
            "purchaseSingleProductCode--采购单品码\n", required = true)
    @TableField(exist = false)
    private String moduleType;

    /**
     * 状态名称
     */
    @ApiModelProperty("状态名称")
    @TableField(exist = false)
    private String stateName;

    /**
     * 批量新增数量
     */
    @ApiModelProperty("批量新增数量")
    @TableField(exist = false)
    private Integer num;

    /**
     * 批号规则id
     */
    @ApiModelProperty("批号规则id")
    @TableField(exist = false)
    private Integer numberRuleId;

    /**
     * 批次号规则
     */
    @ApiModelProperty("批次号规则")
    @TableField(exist = false)
    private String batchNumberRule;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    @TableField(exist = false)
    private String createByNickName;

    /**
     * 更新人名称
     */
    @ApiModelProperty("更新人名称")
    @TableField(exist = false)
    private String updateByNickName;

    /**
     * 生成规则json
     */
    @ApiModelProperty("生成规则json")
    @TableField(exist = false)
    private String ruleDetail;

    /**
     * 校验次数是否需要加1
     */
    @TableField(exist = false)
    private boolean isCheckTimesNeedAddOne;

    /**
     * 自增字段
     */
    @ApiModelProperty("自增字段")
    @TableField(value = "seq")
    private Integer seq;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value = "remark")
    private String remarks;

    /**
     * 供应商编码
     */
    @ApiModelProperty("供应商批号")
    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 等级
     */
    @ApiModelProperty("等级")
    @TableField(value = "grade")
    private String grade;

    /**
     * 批次规格
     */
    @ApiModelProperty("批次规格")
    @TableField(value = "batch_specs")
    private Double batchSpecs;

    /**
     * 物料字段
     */
    @ApiModelProperty("物料字段")
    @TableField(exist = false)
    private MaterialEntity materialFields;

    /**
     * 关联单据对象
     * 第一个string为关联单号类型
     * 第二个string为关联单号（可以是生产工单号、销售订单号、采购订单。。。多种单号）
     * 举例说明：
     * 生成采购单品码
     * relatedMap 需传 relatedMap: {"purchase": "值为具体的采购订单号"}
     */
    @TableField(exist = false)
    private Map<String, String> relatedMap;

    /**
     * 特征参数skuId
     */
    @ApiModelProperty("特征参数skuId")
    @TableField(value = "sku_id")
    private Integer skuId;

    /**
     * 是否已打印
     */
    @ApiModelProperty("是否已打印")
    @TableField(value = "is_print")
    private Boolean isPrint;

    /**
     * 生产数量
     */
    @ApiModelProperty("生产数量")
    @TableField(value = "finish_count")
    @UnitFormatColumn
    private Double finishCount;

    /**
     * 不良数量
     */
    @ApiModelProperty("不良数量")
    @TableField(value = "unqualified")
    @UnitFormatColumn
    private Double unqualified;

    /**
     * 采购时间
     */
    @ApiModelProperty("采购时间")
    @TableField(value = "purchase_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date purchaseTime;


    /**
     * 生产时间
     */
    @ApiModelProperty("生产时间")
    @TableField(value = "production_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date productionTime;

    /**
     * 客户编号
     */
    @ApiModelProperty("客户编号")
    @TableField(value = "customer_code")
    private String customerCode;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 客户物料编码
     */
    @ApiModelProperty("客户物料编码")
    @TableField(value = "customer_material_code")
    private String customerMaterialCode;

    /**
     * 客户物料名称
     */
    @ApiModelProperty("客户物料名称")
    @TableField(value = "customer_material_name")
    private String customerMaterialName;

    /**
     * 客户物料规格
     */
    @ApiModelProperty("客户物料规格")
    @TableField(value = "customer_specification")
    private String customerSpecification;

    /**
     * 供应商物料编码
     */
    @ApiModelProperty("供应商物料编码")
    @TableField(value = "supplier_material_code")
    private String supplierMaterialCode;

    /**
     * 供应商物料名称
     */
    @ApiModelProperty("供应商物料名称")
    @TableField(value = "supplier_material_name")
    private String supplierMaterialName;

    /**
     * 供应商物料规格
     */
    @ApiModelProperty("供应商物料规格")
    @TableField(value = "supplier_specification")
    private String supplierSpecification;

    /**
     * 批次扩展属性
     */
    @TableField(exist = false)
    private Map<String, String> extendMap;

    /**
     * 批次打印需要的来料检验单信息
     */
    @TableField(exist = false)
    private InspectPrintDTO inspectPrint;

    /**
     * 下推数量
     */
    @TableField(exist = false)
    private Double pushDownQuantity;
    /**
     * 入库数量
     */
    @ApiModelProperty("入库数量")
    @TableField(value = "inventory_quantity")
    @UnitFormatColumn
    private Double inventoryQuantity;

    public Double getPushDownQuantity() {
        return Optional.ofNullable(pushDownQuantity).orElse(0d);
    }
    public Double getInventoryQuantity() {
        return Optional.ofNullable(inventoryQuantity).orElse(0d);
    }

    public String buildOrderSkuCode() {
        Integer skuInfo = skuId == null? Constants.SKU_ID_DEFAULT_VAL : skuId;
        return this.relateNumber + Constant.UNDERLINE + skuInfo;
    }

    @Override
    public Serializable pkVal() {
        return this.barCodeId;
    }

    public static BarCodeSimpleDTO convertToDto(BarCodeEntity entity) {
        return JSON.parseObject(JSON.toJSONString(entity), BarCodeSimpleDTO.class);
    }

}
