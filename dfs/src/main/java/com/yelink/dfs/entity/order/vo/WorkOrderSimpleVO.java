package com.yelink.dfs.entity.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class WorkOrderSimpleVO {

    private Integer workOrderId;
    /**
     * 工单号
     */
    private String workOrderNumber;

    /**
     * 工单名称
     */
    private String workOrderName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
    @UnitFormatColumn
    private Double planQuantity;
    @UnitFormatColumn
    private Double finishCount;
    @UnitColumn
    private String materialCode;

    private String materialName;

    private String saleOrderNumber;

    private String productOrderNumber;

}
