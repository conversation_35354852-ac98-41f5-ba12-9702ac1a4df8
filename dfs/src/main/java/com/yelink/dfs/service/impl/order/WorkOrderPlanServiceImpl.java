package com.yelink.dfs.service.impl.order;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderLineRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanImportDTO;
import com.yelink.dfs.entity.order.WorkOrderTeamRelevanceEntity;
import com.yelink.dfs.mapper.order.WorkOrderBasicUnitDayCountMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.order.WorkOrderPlanMapper;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderPlanScheduleWriteBackDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderPlanScheduleWriteBackDetailDTO;
import com.yelink.dfs.service.common.DictService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderLineRelevanceService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderTeamRelevanceService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.dfs.UploadFileCodeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.ExcelTemplateSetDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.dfscommon.utils.PathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.BaseFormulaEvaluator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-07-20 19:08
 */
@Slf4j
@Service
public class WorkOrderPlanServiceImpl extends ServiceImpl<WorkOrderPlanMapper, WorkOrderPlanEntity> implements WorkOrderPlanService {
    @Resource
    private WorkOrderMapper workOrderMapper;
    @Resource
    private UploadService uploadService;
    @Resource
    private ImportProgressService importProgressService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private BusinessConfigValueService businessConfigValueService;
    @Resource
    private UserAuthenService userAuthenService;
    @Resource
    private WorkOrderBasicUnitDayCountMapper workOrderBasicUnitDayCountMapper;
    @Resource
    private DictService dictService;

    /**
     * 创建或修改工单计划
     *
     * @param workOrderEntity
     */
    @Override
    public void createWorkOrderPlan(WorkOrderEntity workOrderEntity) {
        Double planQuantity = workOrderEntity.getPlanQuantity();

        //根据时间插入计划列表,如果有记录则修改
        //如果第一天时间小于首班最早时间，则把头一天也计算在内
        Date startDateTemp = dictService.getDayOutputBeginTime(workOrderEntity.getStartDate());
        Date startDate = DateUtil.formatToDate(startDateTemp, DateUtil.DATETIME_FORMAT_ZERO);
        //如果最后一天时间晚于首班最早时间则把当天也算在内
        Date endDateTemp = dictService.getDayOutputBeginTime(workOrderEntity.getEndDate());
        Date endDate = DateUtil.formatToDate(endDateTemp, DateUtil.DATETIME_FORMAT_ZERO);
        long count = DateUtil.getDaysBetweenDays(startDate, endDate) + 1;
        if (count < 0) {
            throw new ResponseException("结束时间不能小于开始时间");
        }

        //由于创建工单和修改工单都调用这个接口，所以需要判断时间段跟之前的时间段是否一致，毫秒值相比较
        List<Long> dateList = new ArrayList<>();
        Date date = (Date) startDate.clone();
        while (date.compareTo(endDate) <= 0) {
            dateList.add(date.getTime());
            date = DateUtil.addDate(date, 1);
        }
        LambdaQueryWrapper<WorkOrderPlanEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber());
        List<WorkOrderPlanEntity> list = this.list(queryWrapper);

        if (!CollectionUtils.isEmpty(list)) {
            List<Long> oldList = list.stream().map(o -> o.getTime().getTime()).collect(Collectors.toList());
            //如果一致则不需要更改每日计划
            if (oldList.containsAll(dateList) && dateList.containsAll(oldList)) {
                return;
            }
        } else {
            //避免空指针
            list = new ArrayList<>();
        }
        //先删除
        List<Integer> idList = list.stream().map(WorkOrderPlanEntity::getId).collect(Collectors.toList());
        removeByIds(idList);

        //整数分配给每一天
        Double value = MathUtil.divideDouble(planQuantity, count, 0);
        //余数天分配+1
        Double lastValue = planQuantity - value * count;

        HashMap<Long, Double> planMap = new HashMap<>();
        Date dateTmp = (Date) startDate.clone();
        while (dateTmp.compareTo(endDate) <= 0) {
            planMap.put(dateTmp.getTime(), value);
            dateTmp = DateUtil.addDate(dateTmp, 1);
        }

        dateTmp = (Date) startDate.clone();
        for (int i = 0; i < lastValue; i++) {
            double v = 1.0 + planMap.get(dateTmp.getTime());
            planMap.put(dateTmp.getTime(), v);
            dateTmp = DateUtil.addDate(dateTmp, 1);
        }
        // 工单的生产基本单元
        WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService = SpringUtil.getBean(WorkOrderBasicUnitRelationService.class);
        assert workOrderBasicUnitRelationService != null;
        WorkOrderBasicUnitRelationEntity basicUnitRelation = workOrderBasicUnitRelationService.lambdaQuery().eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber()).last("limit 1").one();
        while (startDate.compareTo(endDate) <= 0) {
            WorkOrderPlanEntity workOrderPlanEntityTmp = this.lambdaQuery()
                    .eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                    .eq(WorkOrderPlanEntity::getTime, startDate).one();
            WorkOrderPlanEntity workOrderPlanEntity = WorkOrderPlanEntity.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                    .productionBasicUnitType(workOrderEntity.getWorkCenterType())
                    .productionBasicUnitId(Optional.ofNullable(basicUnitRelation).map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).orElse(null))
                    .productionBasicUnitName(Optional.ofNullable(basicUnitRelation).map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).orElse(null))
                    .time(startDate)
                    .planQuantity(planMap.get(startDate.getTime()))
                    .build();

            if (workOrderPlanEntityTmp == null) {
                this.save(workOrderPlanEntity);
            } else {
                workOrderPlanEntityTmp.setPlanQuantity(workOrderPlanEntity.getPlanQuantity());
                this.updateById(workOrderPlanEntityTmp);
            }
            startDate = DateUtil.addDate(startDate, 1);
        }
    }

    @Override
    public void saveWorkOrderPlan(WorkOrderEntity workOrderEntity) {
        if(!Boolean.TRUE.equals(workOrderEntity.getAutoCreatePlan())) {
            return;
        }
        Date now = new Date();
        List<WorkOrderPlanEntity> workOrderPlanList = workOrderEntity.getWorkOrderPlanList();
        //前端没传则,且配置为true才自动生成工单计划
        boolean autoCreatePlan = Boolean.parseBoolean(businessConfigValueService.getValue(ConfigConstant.WORK_ORDER_PLAN_CREATE_CONFIG));
        //前端不传计划
        if (CollectionUtils.isEmpty(workOrderPlanList)) {
            //配置为是，则自动创建
            if (autoCreatePlan) {
                createWorkOrderPlan(workOrderEntity);
            }
            return;
        }
        //前端传了工单计划
        //校验日计划是否重复
        checkDuplicate(workOrderPlanList);

        for (WorkOrderPlanEntity workOrderPlanEntity : workOrderPlanList) {
            workOrderPlanEntity.setId(null);
            workOrderPlanEntity.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
            workOrderPlanEntity.setCreateDate(now);
        }
        this.saveBatch(workOrderPlanList);
    }

    @Override
    public void updateWorkOrderPlan(WorkOrderEntity workOrderEntity) {
   /*     Date now = new Date();

        List<WorkOrderPlanEntity> workOrderPlanList = workOrderEntity.getWorkOrderPlanList();
        if (workOrderPlanList == null) {
            //为空则不更新
            return;
        }
        if (workOrderPlanList.isEmpty()) {
            //空数组则删除
            this.lambdaUpdate().eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber()).remove();
            return;
        }
        //校验日计划是否重复
        checkDuplicate(workOrderPlanList);

        //前端传了工单计划
        //数据库未查出计划
        List<WorkOrderPlanEntity> list = this.lambdaQuery().eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber()).list();
        if (CollectionUtils.isEmpty(list)) {
            for (WorkOrderPlanEntity workOrderPlanEntity : workOrderPlanList) {
                workOrderPlanEntity.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
                workOrderPlanEntity.setCreateDate(now);
            }
            saveBatch(workOrderPlanList);
            return;
        }
        //数据库查出计划
        Map<Long, WorkOrderPlanEntity> dbCollect = list.stream().collect(Collectors.groupingBy(o -> o.getTime().getTime(), Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        for (WorkOrderPlanEntity workOrderPlanEntity : workOrderPlanList) {
            long key = workOrderPlanEntity.getTime().getTime();
            WorkOrderPlanEntity dbEntity = dbCollect.get(key);
            //对于数据库存在的记录则更新
            if (dbEntity != null) {
                dbEntity.setPlanQuantity(workOrderPlanEntity.getPlanQuantity());
                dbEntity.setUpdateDate(now);
                updateById(dbEntity);
            } else {
                //不存在则新增
                workOrderPlanEntity.setWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
                workOrderPlanEntity.setCreateDate(now);
                save(workOrderPlanEntity);
            }
            dbCollect.remove(key);
        }

        //删除剩下的记录
        List<Integer> ids = dbCollect.values().stream().map(WorkOrderPlanEntity::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ids)) {
            removeByIds(ids);
        }*/
    }

    private void checkDuplicate(List<WorkOrderPlanEntity> workOrderPlanList) {
        Map<Date, List<WorkOrderPlanEntity>> collect = workOrderPlanList.stream().collect(Collectors.groupingBy(WorkOrderPlanEntity::getTime));
        for (Map.Entry<Date, List<WorkOrderPlanEntity>> entry : collect.entrySet()) {
            if (entry.getValue().size() > 1) {
                throw new ResponseException(RespCodeEnum.WORK_ORDER_PLAN_DUPLICATE_DATE);
            }
        }
    }

    @Override
    public Double getPlanQuantityByOrders(List<String> workOrders, Date date) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return 0.0;
        }
        LambdaQueryWrapper<WorkOrderPlanEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WorkOrderPlanEntity::getWorkOrderNumber, workOrders)
                .eq(WorkOrderPlanEntity::getTime, date);
        List<WorkOrderPlanEntity> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return 0.0;
        }
        return list.stream().mapToDouble(WorkOrderPlanEntity::getPlanQuantity).sum();
    }

    @Override
    public Double getPlanQuantityByOrder(String workOrder, Date date) {
        LambdaQueryWrapper<WorkOrderPlanEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderPlanEntity::getWorkOrderNumber, workOrder)
                .eq(WorkOrderPlanEntity::getTime, date);
        WorkOrderPlanEntity one = getOne(wrapper);
        if (one == null) {
            return 0.0;
        }
        return one.getPlanQuantity();
    }

    @Override
    public List<WorkOrderPlanEntity> getByWorkOrderNumber(String workOrderNumber, boolean achievementRate) {
        Map<String, List<WorkOrderPlanEntity>> group = getByWorkOrderNumbers(Collections.singletonList(workOrderNumber), achievementRate);
        return group.getOrDefault(workOrderNumber, Collections.emptyList());
    }
    @Override
    public Map<String, List<WorkOrderPlanEntity>> getByWorkOrderNumbers(Collection<String> workOrderNumbers, boolean achievementRate) {
        if(CollectionUtils.isEmpty(workOrderNumbers)) {
            return Collections.emptyMap();
        }
        List<WorkOrderPlanEntity> allPlans = this.lambdaQuery()
                .in(WorkOrderPlanEntity::getWorkOrderNumber, workOrderNumbers)
                .gt(WorkOrderPlanEntity::getPlanQuantity, 0)
                .list();
        Map<String, List<WorkOrderPlanEntity>> workOrderNumberPlanGroup = allPlans.stream().collect(Collectors.groupingBy(WorkOrderPlanEntity::getWorkOrderNumber));
        // 达成率： 每日产量 / 计划数
        if(achievementRate) {
            List<WorkOrderBasicUnitDayCountEntity> allDayCounts = workOrderBasicUnitDayCountMapper.selectList(
                    Wrappers.lambdaQuery(WorkOrderBasicUnitDayCountEntity.class)
                            .in(WorkOrderBasicUnitDayCountEntity::getWorkOrderNumber, workOrderNumbers)
            );
            Map<String, List<WorkOrderBasicUnitDayCountEntity>> workOrderDayCountGroup = allDayCounts.stream().collect(Collectors.groupingBy(WorkOrderBasicUnitDayCountEntity::getWorkOrderNumber));

            for (Map.Entry<String, List<WorkOrderPlanEntity>> entry : workOrderNumberPlanGroup.entrySet()) {
                String workOrderNumber = entry.getKey();
                List<WorkOrderPlanEntity> plans = entry.getValue();
                List<WorkOrderBasicUnitDayCountEntity> dayCounts = workOrderDayCountGroup.getOrDefault(workOrderNumber, Collections.emptyList());
                Map<String, Double> uniCodeDayCountMap = dayCounts.stream().collect(Collectors.toMap(WorkOrderBasicUnitDayCountEntity::getUniCode, WorkOrderBasicUnitDayCountEntity::getCount));
                // 主资源/关联资源 均可靠uniCode匹配到
                for (WorkOrderPlanEntity plan : plans) {
                    double finishCount = uniCodeDayCountMap.getOrDefault(plan.buildUniCode(), 0d);
                    plan.setFinishCount(finishCount);
                    plan.setAchievementRate(NullableDouble.of(finishCount).div(plan.getPlanQuantity()).cal());
                }
            }

        }
        return workOrderNumberPlanGroup;
    }


    @Override
    public void uploadCustomTemplate(MultipartFile file, String operationUsername) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        // 上传
        uploadService.uploadReferencedFile(UploadFileCodeEnum.WORK_ORDER_PLAN_TEMPLATE_NAME.getCode(), operationUsername, file);
    }

    @Override
    public byte[] downloadImportTemplate() throws IOException {
        Workbook templateWorkbook = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        InputStream inputStream = null;
        try {
            inputStream = getTransferTemplate();
            templateWorkbook = WorkbookFactory.create(inputStream);
            //删除说明sheet和目标数据sheet
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } finally {
            ExcelUtil.closeWorkBook(templateWorkbook);
            IOUtils.closeQuietly(byteArrayOutputStream);
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public InputStream downloadCustomImportTemplate() throws IOException {
        byte[] download = uploadService.download(UploadFileCodeEnum.WORK_ORDER_PLAN_TEMPLATE_NAME.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            return new ByteArrayInputStream(download);
        }
        throw new ResponseException("未配置自定义转换模板");
    }

    private InputStream getTransferTemplate() throws IOException {
        InputStream inputStream;
        byte[] download = uploadService.download(UploadFileCodeEnum.WORK_ORDER_PLAN_TEMPLATE_NAME.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            inputStream = new ByteArrayInputStream(download);
        } else {
            //默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template/workOrderPlanTemplate.xlsx");
            inputStream = resource.getInputStream();
        }
        return inputStream;
    }

    @Async
    @Override
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        final String lockKey = RedisKeyPrefix.WORK_ORDER_PLAN_IMPORT_LOCK;
        importProgressKey = RedisKeyPrefix.WORK_ORDER_PLAN_IMPORT_PROGRESS;
        //初始化锁 + 进度
        importProgressService.initLockProgress(lockKey, importProgressKey);
        InputStream templateInputStream = null;
        File templateFile = null;
        try {
            //1、获取模板文件 ： 用户自定义/默认模板,转成文件
            templateInputStream = getTransferTemplate();
            // 创建一个空的临时文件
            templateFile = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!templateFile.getParentFile().exists()) {
                templateFile.getParentFile().mkdirs();
            }
            FileUtils.copyInputStreamToFile(templateInputStream, templateFile);
            //2、读取模板配置说明
            ExcelTemplateSetDTO excelTemplateSetDTO = EasyExcelUtil.read(new FileInputStream(templateFile), ExcelTemplateSetDTO.class, 0, 3).get(0);
            //3、通过配置将数据转换并复制到模板原始数据
            List<WorkOrderPlanImportDTO> totalImportRecords = executeDataToTarget(WorkbookFactory.create(inputStream), templateFile, excelTemplateSetDTO);
            //4、校验并转换数据
            // 业务配置：是否覆盖当日之前的计划
            boolean isCoverBeforePlan = Boolean.parseBoolean(businessConfigValueService.getValue(ConfigConstant.WORK_ORDER_PLAN_IMPORT_CONFIG));
            List<WorkOrderPlanImportDTO> canImportRecords = verifyFormat(totalImportRecords, isCoverBeforePlan);

            //5、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(totalImportRecords, WorkOrderPlanImportDTO.class, operationUsername);
            //6、保存导入记录
            int countPass = CollectionUtils.isEmpty(totalImportRecords) ? 0 : (int) totalImportRecords.stream().filter(WorkOrderPlanImportDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.WORK_ORDER_PLAN_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.WORK_ORDER_PLAN_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(totalImportRecords.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //7、保存数据
            int rowTotal = canImportRecords.size();
            int successCount = 0, failCount = totalImportRecords.size() - rowTotal;

            // yyyy-MM-dd
            Date nowDate = DateUtil.formatToDate(new Date(), DateUtil.DATE_FORMAT);
            Map<String, Boolean> workPlanDeleteMap = new HashMap<>(16);
            for (WorkOrderPlanImportDTO insertImport : canImportRecords) {
                // 删除该工单所有的计划, 如果不能覆盖当日之前的计划, 则只能删除今日之后的
                if (workPlanDeleteMap.get(insertImport.getWorkOrderNumber()) == null) {
                    this.lambdaUpdate()
                            .eq(WorkOrderPlanEntity::getWorkOrderNumber, insertImport.getWorkOrderNumber())
                            .ge(!isCoverBeforePlan, WorkOrderPlanEntity::getTime, nowDate)
                            .remove();
                    workPlanDeleteMap.put(insertImport.getWorkOrderNumber(), true);
                }
                // 保存或更新：如果不存在就插入一条新记录, 存在就更新
                Date createTime = Objects.nonNull(insertImport.getCreateTime()) ? insertImport.getCreateTime() : new Date();
                this.getBaseMapper().saveOrUpdateRecord(insertImport.getWorkOrderNumber(), insertImport.getPlanDate(), insertImport.getPlanQuantity(), operationUsername, createTime);

                successCount++;
                importProgressService.updateProgress(importProgressKey, false, totalImportRecords.size(), successCount, failCount);
            }
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, totalImportRecords.size(), successCount, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.error("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(templateInputStream);
            IOUtils.closeQuietly(inputStream);
            FileUtils.deleteQuietly(templateFile);
            importProgressService.releaseLock(lockKey);
        }
    }

    /**
     * 处理列表模式导入数据，判断导入数据 统计导入结果
     */
    private List<WorkOrderPlanImportDTO> executeDataToTarget(Workbook importWorkbook, File templateFile, ExcelTemplateSetDTO excelTemplateSetDTO) throws IOException {
        List<WorkOrderPlanImportDTO> list = new ArrayList<>();
        Sheet importSheet = importWorkbook.getSheetAt(0);
        int rowTotal = importSheet.getPhysicalNumberOfRows() - 1;
        Workbook templateWorkbook = WorkbookFactory.create(templateFile);
        try {
            Sheet templateResourceSheet = templateWorkbook.getSheetAt(2);
            for (int i = 2; i <= rowTotal; i++) {
                // 模板只有第一行数据有公式映射,所有都写到第一行
                // 清除第三行的数据
                ExcelUtil.clearSheet(templateResourceSheet, 2, 3, 0, excelTemplateSetDTO.getColumnNum());
                Row templateResourceSheetRow = templateResourceSheet.getRow(2);
                if (templateResourceSheetRow == null) {
                    templateResourceSheetRow = templateResourceSheet.createRow(2);
                }
                for (int k = 0; k < excelTemplateSetDTO.getColumnNum(); k++) {
                    Row row = importSheet.getRow(i);
                    if (row == null) {
                        continue;
                    }
                    Cell xssfCell = row.getCell(k);
                    if (xssfCell != null) {
                        ExcelUtil.copyCell(xssfCell, templateResourceSheetRow.createCell(k));
                    }
                }
                // 打开工作簿时应用程序是否应执行完全重新计算。
                templateWorkbook.setForceFormulaRecalculation(true);
                // 强行刷新单元格公式 计算公式保存结果，但不改变公式
                BaseFormulaEvaluator.evaluateAllFormulaCells(templateWorkbook);

                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                templateWorkbook.write(outputStream);
                ByteArrayInputStream byteStream = new ByteArrayInputStream(outputStream.toByteArray());
                IOUtils.closeQuietly(outputStream);
                List<WorkOrderPlanImportDTO> read = EasyExcelUtil.read(byteStream, WorkOrderPlanImportDTO.class, 1, 2);
                if (!CollectionUtils.isEmpty(read)) {
                    list.addAll(read);
                }
            }
            return list;
        } finally {
            ExcelUtil.closeWorkBook(templateWorkbook);
            ExcelUtil.closeWorkBook(importWorkbook);
        }
    }

    /**
     * 校验数据,返回可以操作的数据
     */
    private List<WorkOrderPlanImportDTO> verifyFormat(List<WorkOrderPlanImportDTO> imports, boolean isCoverBeforePlan) {
        log.info("校验数据：{}", JSON.toJSONString(imports));

        // yyyy-MM-dd
        Date nowDate = DateUtil.formatToDate(new Date(), DateUtil.DATE_FORMAT);
        Map<String, WorkOrderEntity> workOrderEntityMap = new HashMap<>(16);
        List<WorkOrderPlanImportDTO> canImports = new ArrayList<>();
        boolean canImport;
        StringBuilder importResult;
        for (WorkOrderPlanImportDTO importDTO : imports) {
            importResult = new StringBuilder();
            canImport = true;

            if (StringUtils.isBlank(importDTO.getWorkOrderNumber())) {
                importResult.append("工单编号不能为空；");
                canImport = false;
            } else {
                WorkOrderEntity workOrder = workOrderEntityMap.get(importDTO.getWorkOrderNumber()) != null ? workOrderEntityMap.get(importDTO.getWorkOrderNumber()) : workOrderMapper.getByWorkOrderNumber(importDTO.getWorkOrderNumber());
                workOrderEntityMap.put(importDTO.getWorkOrderNumber(), workOrder);
                if (Objects.isNull(workOrder)) {
                    importResult.append("系统不存在该工单；");
                    canImport = false;
                }
            }
            if (Objects.isNull(importDTO.getPlanDate())) {
                importResult.append("计划日期不能为空；");
                canImport = false;
            }
            if (Objects.isNull(importDTO.getPlanQuantity()) || importDTO.getPlanQuantity() < 0) {
                importResult.append("计划数量不能为空或不能为负数；");
                canImport = false;
            }
            if (!isCoverBeforePlan && Objects.nonNull(importDTO.getPlanDate()) && nowDate.compareTo(importDTO.getPlanDate()) > 0) {
                importResult.append("不能覆盖今日之前的生产工单日计划；");
                canImport = false;
            }

            importDTO.setVerifyPass(canImport);
            if (importDTO.getVerifyPass()) {
                canImports.add(importDTO);
                importDTO.setImportResult("数据校验通过");
            } else {
                importDTO.setImportResult(importResult.toString());
            }
        }
        return canImports;
    }

    @Override
    public ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.WORK_ORDER_PLAN_IMPORT_PROGRESS);
    }

    @Override
    public List<String> scheduleWriteBack(WorkOrderPlanScheduleWriteBackDTO dto) {
        DeviceService deviceService = SpringUtil.getBean(DeviceService.class);
        ProductionLineService lineService = SpringUtil.getBean(ProductionLineService.class);
        WorkOrderTeamRelevanceService workOrderTeamRelevanceService = SpringUtil.getBean(WorkOrderTeamRelevanceService.class);
        WorkOrderDeviceRelevanceService workOrderDeviceRelevanceService = SpringUtil.getBean(WorkOrderDeviceRelevanceService.class);
        WorkOrderLineRelevanceService workOrderLineRelevanceService = SpringUtil.getBean(WorkOrderLineRelevanceService.class);
        WorkCenterService workCenterService = SpringUtil.getBean(WorkCenterService.class);
        SysTeamService teamService = SpringUtil.getBean(SysTeamService.class);
        // 所有的工单集合
        List<WorkOrderPlanScheduleWriteBackDetailDTO> details = dto.getDetail();
        List<String> originWorkOrderNumbers =  details.stream().map(WorkOrderPlanScheduleWriteBackDetailDTO::getWorkOrderNumber).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(originWorkOrderNumbers)) {
            return Collections.emptyList();
        }
        Date now = new Date();
        String username = userAuthenService.getUsername();
        // 结果
        List<String> results = new ArrayList<>();
        // 校验
        // 1. 校验工单
        List<WorkOrderEntity> workOrders = workOrderMapper.selectList(
                Wrappers.lambdaQuery(WorkOrderEntity.class).in(WorkOrderEntity::getWorkOrderNumber, originWorkOrderNumbers)
        );
        Map<String, WorkOrderEntity> workOrderMap = workOrders.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, Function.identity()));
        // 1.1真正存在的工单
        List<WorkOrderPlanScheduleWriteBackDetailDTO> filter1Details = details.stream().filter(e -> {
            if(!e.getIsDelete() && e.getRecordDate() == null) {
                results.add(String.format("单据:[%s]-[%s]保存时, 日期需填写", e.getWorkOrderNumber(), e.getProductionBasicUnitId()));
                return false;
            }
            // 工单
            WorkOrderEntity workOrder = workOrderMap.get(e.getWorkOrderNumber());
            if (workOrder == null) {
                results.add(String.format("工单号:[%s]不存在", e.getWorkOrderNumber()));
                return false;
            }
            if(workOrder.getWorkCenterId() == null) {
                results.add(String.format("工单号:[%s]没有工作中心", e.getWorkOrderNumber()));
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filter1Details)) {
            return results;
        }

        // 2. 校验是否属于工单下的基本单元或关联资源
        // 2.1 工作中心
        List<Integer> workCenterIds = workOrders.stream().map(WorkOrderEntity::getWorkCenterId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<WorkCenterEntity> workCenters = workCenterService.lambdaQuery().in(WorkCenterEntity::getId, workCenterIds).list();
        Map<Integer, WorkCenterEntity> workCenterMap = workCenters.stream().collect(Collectors.toMap(WorkCenterEntity::getId, Function.identity()));
        // 2.2 工单的生产基本单元
        WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService = SpringUtil.getBean(WorkOrderBasicUnitRelationService.class);
        List<WorkOrderBasicUnitRelationEntity> existUnitRelations =  workOrderBasicUnitRelationService.lambdaQuery().in(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderMap.keySet()).list();
        Map<String, WorkOrderBasicUnitRelationEntity> existUnitRelationMap = existUnitRelations.stream().collect(Collectors.toMap(WorkOrderBasicUnitRelationEntity::getUniCode, Function.identity()));

        // 过滤掉不满足条件的
        List<WorkOrderPlanEntity> needSavePlans = new ArrayList<>();
        List<WorkOrderPlanScheduleWriteBackDetailDTO> needDeletePlans = new ArrayList<>();
        for (WorkOrderPlanScheduleWriteBackDetailDTO e : filter1Details) {
            if(e.getIsDelete()) {
                needDeletePlans.add(e);
                continue;
            }
            // 工单
            WorkOrderEntity workOrder = workOrderMap.get(e.getWorkOrderNumber());
            // 工作中心
            WorkCenterEntity workCenter = workCenterMap.get(workOrder.getWorkCenterId());
            // 入库的数据
            Integer productionBasicUnitId = e.getProductionBasicUnitId();
            String productionBasicUnitName;
            // 校验主资源
            if(e.getMain()) {
                // 工作中心相关
                String workCenterType = workCenter.getType();
                e.setProductionBasicUnitType(workCenter.getType());
                WorkOrderBasicUnitRelationEntity relation = existUnitRelationMap.get(e.getRelationUniCode());
                if (relation == null) {
                    results.add(String.format("工单号:[%s], 工作中心类型:[%s], 生产基本单元id:[%s]不存在", e.getWorkOrderNumber(), workCenterType, productionBasicUnitId));
                    continue;
                }
                productionBasicUnitName = relation.getProductionBasicUnitName();
            }else {
                String relevanceType = workCenter.getRelevanceType();
                WorkCenterTypeEnum relevanceTypeEnum = WorkCenterTypeEnum.getByCode(relevanceType);
                if(relevanceTypeEnum == null) {
                    results.add(String.format("工单号:[%s], 对应的工作中心:[%s]没有关联资源", e.getWorkOrderNumber(), workCenter.getName()));
                    continue;
                }
                Integer workOrderId = workOrder.getWorkOrderId();
                e.setProductionBasicUnitType(relevanceType);
                switch (relevanceTypeEnum) {
                    case LINE:
                        boolean existsLine = workOrderLineRelevanceService.lambdaQuery().eq(WorkOrderLineRelevanceEntity::getWorkOrderId, workOrderId)
                                .eq(WorkOrderLineRelevanceEntity::getLineId, productionBasicUnitId).exists();
                        if(!existsLine) {
                            results.add(String.format("工单号:[%s], 关联资源类型:[%s], 没有资源id:[%s]", e.getWorkOrderNumber(), relevanceType, productionBasicUnitId));
                            continue;
                        }
                        ProductionLineEntity line = lineService.getById(productionBasicUnitId);
                        if(line == null) {
                            results.add(String.format("工单号:[%s], 关联资源类型:[%s], 没有资源id:[%s]没有对应实例", e.getWorkOrderNumber(), relevanceType, productionBasicUnitId));
                            continue;
                        }
                        productionBasicUnitName = line.getName();
                        break;
                    case DEVICE:
                        boolean existsDevice = workOrderDeviceRelevanceService.lambdaQuery().eq(WorkOrderDeviceRelevanceEntity::getWorkOrderId, workOrderId)
                                .eq(WorkOrderDeviceRelevanceEntity::getDeviceId, productionBasicUnitId).exists();
                        if(!existsDevice) {
                            results.add(String.format("工单号:[%s], 关联资源类型:[%s], 没有资源id:[%s]", e.getWorkOrderNumber(), relevanceType, productionBasicUnitId));
                            continue;
                        }
                        DeviceEntity device = deviceService.getById(productionBasicUnitId);
                        if(device == null) {
                            results.add(String.format("工单号:[%s], 关联资源类型:[%s], 没有资源id:[%s]没有对应实例", e.getWorkOrderNumber(), relevanceType, productionBasicUnitId));
                            continue;
                        }
                        productionBasicUnitName = device.getDeviceName();
                        break;
                    case TEAM:
                        boolean existsTeam = workOrderTeamRelevanceService.lambdaQuery().eq(WorkOrderTeamRelevanceEntity::getWorkOrderId, workOrderId)
                                .eq(WorkOrderTeamRelevanceEntity::getTeamId, productionBasicUnitId).exists();
                        if(!existsTeam) {
                            results.add(String.format("工单号:[%s], 关联资源类型:[%s], 没有资源id:[%s]", e.getWorkOrderNumber(), relevanceType, productionBasicUnitId));
                            continue;
                        }
                        SysTeamEntity team = teamService.getById(productionBasicUnitId);
                        if(team == null) {
                            results.add(String.format("工单号:[%s], 关联资源类型:[%s], 没有资源id:[%s]没有对应实例", e.getWorkOrderNumber(), relevanceType, productionBasicUnitId));
                            continue;
                        }
                        productionBasicUnitName = team.getTeamName();
                        break;
                    default:
                        continue;
                }
            }
            needSavePlans.add(
                    WorkOrderPlanEntity.builder()
                            .workOrderNumber(e.getWorkOrderNumber())
                            .productionBasicUnitType(e.getProductionBasicUnitType())
                            .isMain(e.getIsMain())
                            .productionBasicUnitId(e.getProductionBasicUnitId())
                            .productionBasicUnitName(productionBasicUnitName)
                            .time(e.getRecordDate())
                            .planQuantity(e.getPlanQuantity())
                            .build()
            );
        }

        // 需要存在的计划
        List<WorkOrderPlanEntity> existPlans = this.lambdaQuery().in(WorkOrderPlanEntity::getWorkOrderNumber, workOrderMap.keySet()).list();
        Map<String, Integer> existUniCodeIdMap = existPlans.stream().collect(Collectors.toMap(WorkOrderPlanEntity::buildUniCode, WorkOrderPlanEntity::getId, (v1, v2) -> v1));
        // 判断是更新还是插入
        for (WorkOrderPlanEntity needSavePlan : needSavePlans) {
            Integer id = existUniCodeIdMap.get(needSavePlan.buildUniCode());
            if(id == null) {
                needSavePlan.setCreateDate(now);
                needSavePlan.setCreateBy(username);
            }else {
                needSavePlan.setId(id);
                needSavePlan.setUpdateDate(now);
                needSavePlan.setUpdateBy(username);
            }
        }
        this.saveOrUpdateBatch(needSavePlans);

        // 删除
        for (WorkOrderPlanScheduleWriteBackDetailDTO e : needDeletePlans) {
            this.lambdaUpdate().eq(WorkOrderPlanEntity::getWorkOrderNumber, e.getWorkOrderNumber())
                    .eq(StringUtils.isNotEmpty(e.getProductionBasicUnitType()), WorkOrderPlanEntity::getProductionBasicUnitType, e.getProductionBasicUnitType())
                    .eq(null != e.getProductionBasicUnitId(), WorkOrderPlanEntity::getProductionBasicUnitId, e.getProductionBasicUnitId())
                    .eq(e.getRecordDate() != null, WorkOrderPlanEntity::getTime, e.getRecordDate())
                    .eq(null != e.getIsMain(), WorkOrderPlanEntity::getIsMain, e.getIsMain()).remove();
        }
        return results;
    }
}
