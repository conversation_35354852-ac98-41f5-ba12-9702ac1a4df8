package com.yelink.dfs.service.impl.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.model.ExcelTask;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.yelink.dfs.common.CodeFactory;
import com.yelink.dfs.common.UserAuthenService;
import com.yelink.dfs.constant.AppendixTypeEnum;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.NumberRedisKeyPrefix;
import com.yelink.dfs.constant.RedisKeyPrefix;
import com.yelink.dfs.constant.common.ModelUploadFileEnum;
import com.yelink.dfs.constant.defect.WorkOrderUnqualifiedSourceEnum;
import com.yelink.dfs.constant.model.VersionModelIdEnum;
import com.yelink.dfs.constant.order.AssignmentStateEnum;
import com.yelink.dfs.constant.order.CompletionRateEnum;
import com.yelink.dfs.constant.order.InvestCheckResultEnum;
import com.yelink.dfs.constant.order.OrderMergeStateEnum;
import com.yelink.dfs.constant.order.OrderStateEnum;
import com.yelink.dfs.constant.order.WorkOrderCirculationStateEnum;
import com.yelink.dfs.constant.order.WorkOrderExecutionStateEnum;
import com.yelink.dfs.constant.order.WorkOrderMsgConfEnum;
import com.yelink.dfs.constant.order.WorkOrderPlanStateEnum;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.constant.product.CraftProcedureInspectMethodEnum;
import com.yelink.dfs.constant.product.CraftStateEnum;
import com.yelink.dfs.constant.product.CraftTypeEnum;
import com.yelink.dfs.constant.product.InspectTriggerConditionEnum;
import com.yelink.dfs.constant.product.MaterialInspectMethodEnum;
import com.yelink.dfs.constant.product.MaterialMatchingEnum;
import com.yelink.dfs.constant.product.TimeoutThresholdTypeEnum;
import com.yelink.dfs.controller.order.WorkOrderListExportHandler;
import com.yelink.dfs.entity.approve.config.dto.ApproveFullPathCodeDTO;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.capacity.CapacityEntity;
import com.yelink.dfs.entity.capacity.dto.CapacityGetDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitSelectDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitTypeDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionResourceDTO;
import com.yelink.dfs.entity.capacity.vo.CapacityVO;
import com.yelink.dfs.entity.code.ProductFlowCodeRecordEntity;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.common.VersionChangeRecordSelectDTO;
import com.yelink.dfs.entity.common.config.dto.AutoReportAfterFinishWorkConfigDTO;
import com.yelink.dfs.entity.common.config.dto.DefectConfigDTO;
import com.yelink.dfs.entity.common.config.dto.InvestCheckVerifyConfigDTO;
import com.yelink.dfs.entity.common.config.dto.ProductFlowCodeConfigDTO;
import com.yelink.dfs.entity.common.config.dto.ShowConfigDTO;
import com.yelink.dfs.entity.common.config.dto.WorkOrderAssignmentConfigDTO;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.FacilitiesEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceEntity;
import com.yelink.dfs.entity.model.WorkCenterDeviceRelevanceEntity;
import com.yelink.dfs.entity.model.WorkCenterLineRelevanceEntity;
import com.yelink.dfs.entity.model.WorkCenterTeamEntity;
import com.yelink.dfs.entity.model.WorkCenterTeamRelevanceEntity;
import com.yelink.dfs.entity.model.dto.RelevanceResourceDTO;
import com.yelink.dfs.entity.model.vo.FacCountVo;
import com.yelink.dfs.entity.order.OrderWorkOrderEntity;
import com.yelink.dfs.entity.order.TakeOutApplicationMaterialEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderDeviceRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderFileEntity;
import com.yelink.dfs.entity.order.WorkOrderInvestCheckResultEntity;
import com.yelink.dfs.entity.order.WorkOrderLineRelevanceEntity;
import com.yelink.dfs.entity.order.WorkOrderMaterialCheckMaterialEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderProductLineRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderTeamEntity;
import com.yelink.dfs.entity.order.WorkOrderTeamRelevanceEntity;
import com.yelink.dfs.entity.order.dto.BatchUpdateWorkOrderDTO;
import com.yelink.dfs.entity.order.dto.FieldInfoDTO;
import com.yelink.dfs.entity.order.dto.OperationOrderDTO;
import com.yelink.dfs.entity.order.dto.ShowExtraNameDTO;
import com.yelink.dfs.entity.order.dto.UpdateTipsDTO;
import com.yelink.dfs.entity.order.dto.UpdateWorkOrderPackageSchemeDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderBasicUnitQueryDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderCreateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderExcelDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderFacDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderInspectionPackageSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderOpenSelectExtendDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderPageDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderQuantityVerifyDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSmartUpdateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderWrapperDTO;
import com.yelink.dfs.entity.order.vo.JudgeOrderVO;
import com.yelink.dfs.entity.order.vo.PrintSourceOrderVO;
import com.yelink.dfs.entity.order.vo.WorkOrderSimpleVO;
import com.yelink.dfs.entity.pack.PackageSchemeEntity;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.CraftEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.CraftProcedureInspectControllerEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureControllerConfigEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.entity.product.ProcedureFileEntity;
import com.yelink.dfs.entity.product.ProcedureMaterialUsedEntity;
import com.yelink.dfs.entity.product.ProcedureRelationWorkHoursEntity;
import com.yelink.dfs.entity.product.ProductEntity;
import com.yelink.dfs.entity.product.dto.CheckInspectSchemeDTO;
import com.yelink.dfs.entity.product.dto.CraftFileDTO;
import com.yelink.dfs.entity.product.dto.CraftFileNewDTO;
import com.yelink.dfs.entity.product.dto.MaterialSkuEntitySelectDTO;
import com.yelink.dfs.entity.receipt.ReceiptProjectContractEntity;
import com.yelink.dfs.entity.screen.vo.WorkOrderEntityVO;
import com.yelink.dfs.entity.statement.dto.MaterialTypeConfigFieldExportDTO;
import com.yelink.dfs.entity.statistics.WorkOrderStateDTO;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.target.record.dto.CheckReportBackDTO;
import com.yelink.dfs.entity.task.dto.WorkOrderTaskDTO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.barcode.BarCodeMapper;
import com.yelink.dfs.mapper.code.ProductFlowCodeRecordMapper;
import com.yelink.dfs.mapper.maintain.MaintainRecordMapper;
import com.yelink.dfs.mapper.manufacture.ProductionLineMapper;
import com.yelink.dfs.mapper.model.WorkCenterMapper;
import com.yelink.dfs.mapper.order.RecordWorkOrderDayCountMapper;
import com.yelink.dfs.mapper.order.WorkOrderMapper;
import com.yelink.dfs.mapper.order.WorkOrderPlanMapper;
import com.yelink.dfs.mapper.order.WorkOrderProcedureRelationMapper;
import com.yelink.dfs.mapper.product.CraftProcedureInspectControllerMapper;
import com.yelink.dfs.mapper.product.ProductMapper;
import com.yelink.dfs.mapper.target.record.ReportLineMapper;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseReceiptInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.open.v1.aksk.dto.OpenApiDTO;
import com.yelink.dfs.open.v1.aksk.dto.WorkOrderInvestCheckResultDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderUpdateStateDTO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderRelevanceVO;
import com.yelink.dfs.provider.FastDfsClientService;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.pushdown.writeback.WriteBackProductOrder2WorkOrder;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.approve.config.ApproveNodeConfigService;
import com.yelink.dfs.service.approve.config.ApproveTemplateService;
import com.yelink.dfs.service.barcode.BarCodeAnalysisService;
import com.yelink.dfs.service.barcode.LabelService;
import com.yelink.dfs.service.barcode.LabelTypeConfigService;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.code.ProductFlowCodeService;
import com.yelink.dfs.service.common.AppendixService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.ModelUploadFileService;
import com.yelink.dfs.service.common.OperationLogService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.common.config.BusinessConfigValueService;
import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.common.config.OrderTypeConfigService;
import com.yelink.dfs.service.common.config.OrderTypeItemService;
import com.yelink.dfs.service.device.DeviceCalendarService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.impl.common.WorkPropertise;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.manufacture.ProductionLineService;
import com.yelink.dfs.service.model.AreaService;
import com.yelink.dfs.service.model.FacilitiesService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterDeviceRelevanceService;
import com.yelink.dfs.service.model.WorkCenterDeviceService;
import com.yelink.dfs.service.model.WorkCenterLineRelevanceService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.model.WorkCenterTeamRelevanceService;
import com.yelink.dfs.service.model.WorkCenterTeamService;
import com.yelink.dfs.service.node.NodeAutoReportService;
import com.yelink.dfs.service.notice.InfoNoticeConfigService;
import com.yelink.dfs.service.open.OpenApiConfigService;
import com.yelink.dfs.service.order.CustomerService;
import com.yelink.dfs.service.order.IsolationService;
import com.yelink.dfs.service.order.OrderExecuteSeqService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderMaterialPlanService;
import com.yelink.dfs.service.order.RecordWorkOrderResumeService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.TakeOutApplicationService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitInputRecordService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderDeviceRelevanceService;
import com.yelink.dfs.service.order.WorkOrderExtendService;
import com.yelink.dfs.service.order.WorkOrderFileService;
import com.yelink.dfs.service.order.WorkOrderInvestCheckResultService;
import com.yelink.dfs.service.order.WorkOrderLineRelevanceService;
import com.yelink.dfs.service.order.WorkOrderMaterialCheckMaterialService;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderProductLineRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.order.WorkOrderSubcontractService;
import com.yelink.dfs.service.order.WorkOrderTeamRelevanceService;
import com.yelink.dfs.service.order.WorkOrderTeamService;
import com.yelink.dfs.service.pack.PackageSchemeService;
import com.yelink.dfs.service.product.BomRawMaterialService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.CraftProcedureService;
import com.yelink.dfs.service.product.CraftService;
import com.yelink.dfs.service.product.MaterialInspectMethodService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureControllerConfigService;
import com.yelink.dfs.service.product.ProcedureFileService;
import com.yelink.dfs.service.product.ProcedureMaterialUsedService;
import com.yelink.dfs.service.product.ProcedureRelationWorkHoursService;
import com.yelink.dfs.service.product.ProcedureService;
import com.yelink.dfs.service.product.ProductService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.receipt.ReceiptProjectContractService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.target.record.RecordWorkOrderStateService;
import com.yelink.dfs.service.target.record.ReportCountService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysTeamService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.api.assignment.AssignmentInterface;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.common.excel.BusinessCodeEnum;
import com.yelink.dfscommon.common.sort.SortDTO;
import com.yelink.dfscommon.common.unit.UnitUtil;
import com.yelink.dfscommon.constant.CategoryTypeEnum;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ModelEnum;
import com.yelink.dfscommon.constant.OpenApiEnum;
import com.yelink.dfscommon.constant.OperationType;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.AmsEventTypeEnum;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.approve.config.IsApproveEnum;
import com.yelink.dfscommon.constant.dfs.OutsourcingProcedureEnum;
import com.yelink.dfscommon.constant.dfs.UploadFileCodeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.BarCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.LabelInfoEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeRecordStateEnum;
import com.yelink.dfscommon.constant.dfs.code.ProductFlowCodeTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.notice.NoticeTypeEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderScheduleStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownItemVerifyEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.constant.pms.ReceiptTypePMSEnum;
import com.yelink.dfscommon.constant.wms.InputOrOutputRelateTypeEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.BatchGenerateCodeDTO;
import com.yelink.dfscommon.dto.BatchGenerateCodeReqDTO;
import com.yelink.dfscommon.dto.BillVo;
import com.yelink.dfscommon.dto.ExcelTemplateSetDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.IsolationDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.MyPage;
import com.yelink.dfscommon.dto.StateEnumDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.SaleOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.purchase.PurchaseReceiptStackAmountDTO;
import com.yelink.dfscommon.dto.common.config.BusinessTypeListVO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeInfoVO;
import com.yelink.dfscommon.dto.common.config.OrderTypeSelectDTO;
import com.yelink.dfscommon.dto.dfs.BomKeyDTO;
import com.yelink.dfscommon.dto.dfs.BomRawMaterialsQuantityDTO;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuDTO;
import com.yelink.dfscommon.dto.dfs.NoticeConfigBuilder;
import com.yelink.dfscommon.dto.dfs.PrintInfoDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderBasicUnitInsertDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderBasicUnitRelationInsertDTO;
import com.yelink.dfscommon.dto.pms.ProjectOrderSelect2DTO;
import com.yelink.dfscommon.dto.pms.ProjectOrderSelectDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownItemSelectDTO;
import com.yelink.dfscommon.dto.pushdown.writeback.StateChangeDTO;
import com.yelink.dfscommon.dto.wms.StockInventorySelectDTO;
import com.yelink.dfscommon.entity.AppendixEntity;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.ProductOrderReportRemarkEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderEntity;
import com.yelink.dfscommon.entity.ams.SaleOrderMaterialEntity;
import com.yelink.dfscommon.entity.ams.dto.SaleOrderVO;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.entity.dfs.OrderTypeItemEntity;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.SysTeamEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import com.yelink.dfscommon.entity.wms.StockInAndOutEntity;
import com.yelink.dfscommon.entity.wms.StockInventoryDetailEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.pojo.Result;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.FieldUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.dfscommon.utils.PathUtils;
import com.yelink.dfscommon.utils.Rational;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.dfscommon.vo.pushdown.AbstractOrderPushDownItemVO;
import com.yelink.inner.service.KafkaWebSocketPublisher;
import com.yelink.notice.constant.TopicEnum;
import com.yelink.notice.entity.MessageContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
@Slf4j
public class WorkOrderServiceImpl extends ServiceImpl<WorkOrderMapper, WorkOrderEntity> implements WorkOrderService {
    protected static final String NORMAL = "正常";
    /**
     * 不是批量更改
     */
    private static final int NO_BATCH = 0;

    @Resource
    private SupplierService supplierService;
    @Resource
    protected CustomerService customerService;
    @Resource
    protected ProductMapper productMapper;
    @Resource
    protected ProcedureService procedureService;
    @Resource
    @Lazy
    protected ProductionLineMapper productionLineMapper;
    @Resource
    @Lazy
    protected ProductionLineService productionLineService;
    @Resource
    private WorkCenterLineRelevanceService workCenterLineRelevanceService;
    @Resource
    protected CodeFactory codeFactory;
    @Resource
    protected OrderWorkOrderService orderWorkOrderService;
    @Resource
    protected WorkPropertise workPropertise;
    @Resource
    protected MaterialService materialService;
    @Resource
    protected MaterialInspectMethodService materialInspectMethodService;
    @Resource
    protected StockInAndOutInterface inAndOutInterface;
    @Resource
    protected SysUserService userService;
    @Resource
    protected WorkOrderPlanMapper workOrderPlanMapper;
    @Resource
    protected WorkOrderPlanService workOrderPlanService;
    @Resource
    protected StockInventoryDetailInterface inventoryDetailInterface;
    @Resource
    protected BomService bomService;
    @Resource
    private BomRawMaterialService bomRawMaterialService;
    @Resource
    protected RedisTemplate<String, Object> redisTemplate;
    @Resource
    protected ReportLineMapper reportLineMapper;
    @Resource
    protected RecordWorkOrderDayCountMapper recordWorkOrderDayCountMapper;
    @Resource
    protected RecordWorkOrderMaterialPlanService recordWorkOrderMaterialPlanService;
    @Resource
    protected ApproveConfigService approveConfigService;
    @Resource
    protected KafkaWebSocketPublisher kafkaWebSocketPublisher;
    @Resource
    protected FastDfsClientService fastDfsClientService;
    @Resource
    protected WorkOrderFileService workOrderFileService;
    @Resource
    protected UploadService uploadService;
    @Resource
    protected RecordWorkOrderStateService workOrderStateService;
    @Resource
    protected MessagePushToKafkaService messagePushToKafkaService;
    @Resource
    protected AssignmentInterface assignmentInterface;
    @Resource
    protected CraftService craftService;
    @Resource
    protected CraftProcedureInspectControllerMapper craftProcedureInspectControllerMapper;
    @Resource
    protected ImportDataRecordService importDataRecordService;
    @Resource
    protected AppendixService appendixService;
    @Resource
    protected ProcedureFileService procedureFileService;
    @Resource
    private FacilitiesService facilitiesService;
    @Resource
    protected DeviceService deviceService;
    @Resource
    protected WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Resource
    private NodeAutoReportService nodeAutoReportService;
    @Resource
    private ProductFlowCodeRecordMapper productFlowCodeRecordMapper;
    @Lazy
    @Resource
    private CraftProcedureService craftProcedureService;
    @Resource
    @Lazy
    private WorkOrderProductLineRelationService workOrderProductLineRelationService;
    @Resource
    private TakeOutApplicationService takeOutApplicationService;
    @Resource
    private ProcedureControllerConfigService procedureControllerConfigService;
    @Resource
    private ProcedureRelationWorkHoursService procedureRelationWorkHoursService;
    @Resource
    private InfoNoticeConfigService infoNoticeConfigService;
    @Resource
    protected SysTeamService sysTeamService;
    @Resource
    private WorkOrderTeamService workOrderTeamService;
    @Resource
    private WorkOrderDeviceRelevanceService workOrderDeviceRelevanceService;
    @Resource
    private WorkOrderTeamRelevanceService workOrderTeamRelevanceService;
    @Resource
    private WorkCenterMapper workCenterMapper;
    @Resource
    private OrderTypeConfigService orderTypeConfigService;
    @Resource
    private OrderTypeItemService orderTypeItemService;
    @Resource
    protected WorkCenterService workCenterService;
    @Resource
    private WorkCenterDeviceService workCenterDeviceService;
    @Resource
    private WorkCenterDeviceRelevanceService workCenterDeviceRelevanceService;
    @Resource
    private WorkOrderLineRelevanceService workOrderLineRelevanceService;
    @Resource
    private OrderExecuteSeqService orderExecuteSeqService;
    @Resource
    private IsolationService isolationService;
    @Resource
    private ImportProgressService importProgressService;
    @Resource
    protected ExtSaleOrderInterface extSaleOrderInterface;
    @Resource
    protected ExtProductOrderInterface extProductOrderInterface;
    @Resource
    private ExtPurchaseReceiptInterface extPurchaseReceiptInterface;
    @Resource
    protected SkuService skuService;
    @Resource
    private LabelService labelService;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    private PackageSchemeService packageSchemeService;
    @Resource
    private ModelService modelService;
    @Resource
    private ProductFlowCodeService productFlowCodeService;
    @Resource
    private BarCodeMapper barCodeMapper;
    @Resource
    private ModelUploadFileService modelUploadFileService;
    @Resource
    private ExcelService excelService;
    @Resource
    private WorkOrderMaterialCheckMaterialService checkMaterialService;
    @Resource
    private WorkOrderProcedureRelationMapper workOrderProcedureRelationMapper;
    @Resource
    private ProcedureMaterialUsedService procedureMaterialUsedService;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    private ReceiptProjectContractService receiptProjectContractService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private WorkCenterTeamService workCenterTeamService;
    @Resource
    private WorkCenterTeamRelevanceService workCenterTeamRelevanceService;
    @Resource
    private MaintainRecordMapper maintainRecordMapper;
    @Resource
    private OpenApiConfigService openApiConfigService;
    @Resource
    private FormFieldRuleConfigService formFieldRuleConfigService;
    @Resource
    @Lazy
    private RecordWorkOrderResumeService recordWorkOrderResumeService;
    @Resource
    private WorkOrderInvestCheckResultService investCheckResultService;
    @Resource
    private CapacityService capacityService;
    @Resource
    private ProductService productService;
    @Resource
    protected UserAuthenService userAuthenService;
    @Resource
    @Lazy
    private WorkOrderSubcontractService workOrderSubcontractService;
    @Resource
    private WriteBackProductOrder2WorkOrder writeBackProductOrder2WorkOrder;
    @Resource
    private OrderPushDownItemService orderPushDownItemService;
    @Resource
    private BusinessConfigValueService businessConfigValueService;
    @Resource
    @Lazy
    private OperationLogService operationLogService;
    @Lazy
    @Resource
    private CommonService commonService;
    @Resource
    @Lazy
    private VersionChangeRecordService versionChangeRecordService;
    @Resource
    @Lazy
    private MaintainRecordService maintainRecordService;
    @Resource
    private LabelTypeConfigService labelTypeConfigService;
    @Resource
    private ApproveNodeConfigService approveNodeConfigService;
    @Resource
    private ApproveTemplateService approveTemplateService;
    @Resource
    private WorkOrderBasicUnitRelationService basicUnitRelationService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;
    @Resource
    private OrderPushDownIdentifierService orderPushDownIdentifierService;
    @Resource
    private WorkOrderBasicUnitInputRecordService workOrderBasicUnitInputRecordService;
    @Lazy
    @Resource
    private WorkOrderServiceImpl workOrderService;
    @Resource
    private WorkOrderMaterialListService workOrderMaterialListService;

    /**
     * 打印时需要替换的占位符 ${ }
     */
    private static final Pattern PATTERN = Pattern.compile("\\$\\{(.*?)\\}");

    public static final String BATCH_UPDATE_WORK_ORDER_LOCK = "BATCH_UPDATE_WORK_ORDER_LOCK_";


    @Override
    public Page<WorkOrderEntity> getList(WorkOrderSelectDTO workOrderSelectDTO, String username) {
        Page<WorkOrderEntity> page = getWorkOrderEntityPage(workOrderSelectDTO, username);
        //给前端展示状态名称和创建人真实名字
        showStateAndName(page.getRecords());
        //设置下推标识信息
        setPushDownIdentifierInfos(page.getRecords());
        return page;
    }

    /**
     * 获取工单列表精简版
     *
     * @param workOrderSelectDTO
     * @param username
     * @return
     */
    @Override
    public Page<WorkOrderEntity> getWorkOrderEntityPage(WorkOrderSelectDTO workOrderSelectDTO, String username) {
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<WorkOrderEntity> wrapper = queryWrapper.lambda();
        // 条件查询
        conditionQuery(workOrderSelectDTO, wrapper);

        if (username != null) {
            // 数据隔离，只能查询角色绑定工作中心下的工单
            Boolean hasPermission = isolationService.dataIsolation(username, wrapper);
            if (!hasPermission) {
                throw new ResponseException(RespCodeEnum.NO_WORK_CENTER_PERMISSION);
            }
        }

        Integer current = workOrderSelectDTO.getCurrent();
        Integer size = workOrderSelectDTO.getSize();
        Page<WorkOrderEntity> page = new Page<>();
        if (current == null || size == null) {
            List<WorkOrderEntity> list = list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = workOrderSelectDTO.limitPage(WorkOrderEntity.class);
            this.page(page, wrapper);
        }
        // 这个不受ShowSimpleInfo影响
        if (Boolean.TRUE.equals(workOrderSelectDTO.getIsShowReportLineInfo())) {
            showReportLineInfo(page.getRecords());
        }
        // 这个不受ShowSimpleInfo影响
        if (Boolean.TRUE.equals(workOrderSelectDTO.getIsShowPushDownInfo())) {
            showPushDownInfo(page.getRecords());
        }
        if (Boolean.TRUE.equals(workOrderSelectDTO.getIsShowBarCodeInfo())) {
            showBarCodeInfo(page.getRecords());
        }
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Objects.nonNull(workOrderSelectDTO.getIsShowSimpleInfo()) && workOrderSelectDTO.getIsShowSimpleInfo()) {
            return page;
        }
        // 展示详情信息
        List<WorkOrderEntity> workOrderRecords = showDetailInfo(workOrderSelectDTO, page);

        //工单列表页数据汇总
        WorkOrderPageDTO pageDTO = getSumForWorkOrders(queryWrapper);
        //入库数
        List<String> numbers = getWorkOrderNumbers(wrapper);
        Double inventoryQuantity = JacksonUtil.getResponseObject(inAndOutInterface.getInventoryQuantityByWorkOrders(numbers), Double.class);
        //设置下推标识信息
        setPushDownIdentifierInfos(page.getRecords());
        pageDTO.setInputCount(inventoryQuantity);
        MyPage<WorkOrderEntity> myPage = new MyPage<>(page.getCurrent(), page.getSize());
        myPage.setRecords(workOrderRecords).setTotal(page.getTotal());
        myPage.setDto(pageDTO);
        setProjectContract(myPage.getRecords());
        return myPage;
    }

    private void showBarCodeInfo(List<WorkOrderEntity> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        List<String> workOrderNumbers = records.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        List<BarCodeEntity> barCodes = barCodeMapper.selectList(
                Wrappers.lambdaQuery(BarCodeEntity.class)
                        .eq(BarCodeEntity::getRuleType, BarCodeTypeEnum.FINISHED.getCode())
                        .in(BarCodeEntity::getRelateNumber, workOrderNumbers)
                        .select(BarCodeEntity::getRelateNumber)
        );
        Map<String, Long> barCodeCountMap = barCodes.stream().collect(Collectors.groupingBy(BarCodeEntity::getRelateNumber, Collectors.counting()));
        for (WorkOrderEntity workOrder : records) {
            workOrder.setBarCodeCount(barCodeCountMap.getOrDefault(workOrder.getWorkOrderNumber(), 0L));
        }
    }

    private void showPushDownInfo(List<WorkOrderEntity> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        List<String> workOrderNumbers = records.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        List<OrderPushDownRecordEntity> pushDownRecords = orderPushDownRecordService.lambdaQuery()
                .eq(OrderPushDownRecordEntity::getSourceOrderType, OrderNumTypeEnum.WORK_ORDER.getTypeCode())
                .in(OrderPushDownRecordEntity::getSourceOrderNumber, workOrderNumbers)
                .isNotNull(OrderPushDownRecordEntity::getPushDownQuantity)
                .list();
        Map<String, Double> pushDownQuantityMap = pushDownRecords.stream().collect(Collectors.groupingBy(
                OrderPushDownRecordEntity::getSourceOrderNumber,
                Collectors.summingDouble(OrderPushDownRecordEntity::getPushDownQuantity))
        );
        for (WorkOrderEntity workOrder : records) {
            workOrder.setPushDownQuantity(pushDownQuantityMap.getOrDefault(workOrder.getWorkOrderNumber(), 0d));
        }
    }

    private void showReportLineInfo(List<WorkOrderEntity> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        ReportLineService reportLineService = SpringUtil.getBean(ReportLineService.class);
        assert reportLineService != null;
        List<String> workOrderNumbers = records.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        List<ReportLineEntity> allReportLines = reportLineService.lambdaQuery().in(ReportLineEntity::getWorkOrder, workOrderNumbers).list();
        Map<String, List<ReportLineEntity>> reportLineGroup = allReportLines.stream().collect(Collectors.groupingBy(ReportLineEntity::getWorkOrder));
        for (WorkOrderEntity workOrder : records) {
            List<ReportLineEntity> reportLines = reportLineGroup.getOrDefault(workOrder.getWorkOrderNumber(), Collections.emptyList());
            workOrder.setReportLineCount((long) reportLines.size());
            // 报工中批次的计数，需处理空批次
            workOrder.setReportLineBarCodeCount(reportLines.stream().map(e -> StringUtils.isBlank(e.getBatch()) ? "" : e.getBatch()).distinct().count());
        }
    }

    /**
     * 展示工单详情信息
     */
    @Override
    public List<WorkOrderEntity> showDetailInfo(WorkOrderSelectDTO workOrderSelectDTO, Page<WorkOrderEntity> page) {
        // 获取用户相关中文名称
        Map<String, String> nickNames = new HashMap<>();
        Map<String, String> signatureUrlMap = new HashMap<>();
        if (Objects.isNull(workOrderSelectDTO.getIsShowUserInfo()) || workOrderSelectDTO.getIsShowUserInfo()) {
            List<SysUserEntity> sysUserEntities = userService.selectList();
            nickNames = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
            signatureUrlMap = userService.getSignatureUrlMap(null);
        }
        // 查询单据类型和业务类型
        List<BusinessTypeListVO> vos = orderTypeConfigService.getOrderTypeListByOrderCategory(OrderTypeSelectDTO.builder().categoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode()).build());
        Map<String, String> businessTypeMap = vos.stream().collect(Collectors.toMap(BusinessTypeListVO::getBusinessTypeCode, BusinessTypeListVO::getBusinessTypeName));
        Map<String, String> ordertTypeMap = vos.stream().flatMap(v -> v.getOrderTypeListVOList().stream()).collect(Collectors.toMap(BusinessTypeListVO.OrderTypeListVO::getOrderType, BusinessTypeListVO.OrderTypeListVO::getOrderTypeName));

        List<WorkOrderEntity> workOrderRecords = page.getRecords();
        if (CollectionUtils.isNotEmpty(workOrderRecords)) {
            List<String> workOrderNumbers = workOrderRecords.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            Map<String, List<WorkOrderBasicUnitRelationEntity>> productBasicUnitMap = basicUnitRelationService.lambdaQuery()
                    .in(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderNumbers)
                    .list().stream()
                    .collect(Collectors.groupingBy(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber));
            Set<Integer> workCenterIdSet = workOrderRecords.stream().map(WorkOrderEntity::getWorkCenterId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, WorkCenterEntity> workCenterIdEntityMap = CollectionUtils.isEmpty(workCenterIdSet) ? new HashMap<>(4) : workCenterMapper.selectBatchIds(workCenterIdSet).stream().collect(Collectors.toMap(WorkCenterEntity::getId, v -> v));
            List<Integer> lineIds = basicUnitRelationService.lambdaQuery()
                    .in(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderNumbers)
                    .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.LINE.getCode())
                    .list().stream()
                    .map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId)
                    .distinct()
                    .collect(Collectors.toList());
            List<ProductionLineEntity> lines = CollectionUtils.isEmpty(lineIds) ? Collections.emptyList() : productionLineService.listByIds(lineIds);
            Map<Integer, ProductionLineEntity> lineIdEntityMap = lines.stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, v -> v));
            // 制造单元下模型
            Set<Integer> lineModelIds = lines.stream().map(ProductionLineEntity::getModelId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<ModelEntity> lineModels = CollectionUtils.isEmpty(lineModelIds) ? Collections.emptyList() : modelService.listByIds(lineModelIds);
            Map<Integer, ModelEntity> lineModelIdMap = lineModels.stream().collect(Collectors.toMap(ModelEntity::getId, Function.identity()));

            // 包装方案
            Map<String, List<PackageSchemeEntity>> packageCodeSchemesMap = new HashMap<>();
            if (Objects.isNull(workOrderSelectDTO.getIsShowPackageSchemeInfo()) || workOrderSelectDTO.getIsShowPackageSchemeInfo()) {
                List<String> packageSchemeCodes = workOrderRecords.stream().map(WorkOrderEntity::getPackageSchemeCode).distinct().collect(Collectors.toList());
                List<PackageSchemeEntity> packageSchemes = packageSchemeService.getDetailByCodes(packageSchemeCodes);
                packageCodeSchemesMap = packageSchemes.stream().collect(Collectors.groupingBy(PackageSchemeEntity::getSchemeCode));
            }
            // 设置表单拓展字段名称
            Map<String, String> fieldRuleConfMap = formFieldRuleConfigService.getExtendFieldNameMap("workOrder.list");

            // 批量物料字段
            List<MaterialEntity> materialFields = materialService.listSimpleMaterialByCodesAndSkuIds(workOrderRecords.stream().map(e ->
                    MaterialCodeAndSkuIdSelectDTO.builder()
                            .materialCode(e.getMaterialCode())
                            .skuId(e.getSkuId())
                            .build()
            ).collect(Collectors.toList()));
            Map<String, MaterialEntity> materialSkuMap = materialFields.stream()
                    .collect(Collectors.toMap(e -> ColumnUtil.getMaterialSku(e.getCode(), e.getSkuEntity()), Function.identity(), (v1, v2) -> v2));
            // 班组
            Map<String, List<WorkOrderTeamEntity>> workOrderTeamsMap = workOrderTeamService.getWorkOrderTeams(workOrderNumbers);
            // 供应商
            Set<String> supplierCodeSet = workOrderRecords.stream().map(WorkOrderEntity::getSupplierCode).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            Map<String, String> supplierCodeNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(supplierCodeSet)) {
                supplierCodeNameMap = supplierService.lambdaQuery()
                        .in(SupplierEntity::getCode, supplierCodeSet)
                        .list().stream().collect(Collectors.toMap(SupplierEntity::getCode, SupplierEntity::getName));
            }
            // 投产结果明细
            Map<String, String> investCheckResultDetailMap = investCheckResultService.lambdaQuery().in(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, workOrderNumbers).list()
                    .stream().filter(o -> StringUtils.isNotBlank(o.getInvestCheckResultDetail()))
                    .collect(Collectors.toMap(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, WorkOrderInvestCheckResultEntity::getInvestCheckResultDetail));
            // 查询工单关联的工艺信息
            List<Integer> craftIds = workOrderRecords.stream().map(WorkOrderEntity::getCraftId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<CraftEntity> craftEntities = CollectionUtils.isEmpty(craftIds) ? Collections.emptyList() : craftService.listByIds(craftIds);
            // 根据craftId分组,如果isTemplate字段为true代表 "关联工艺模板"，为false代表"关联工艺"，为null代表 "无工艺配置"
            Map<Integer, String> relateCraftInfoMap = craftEntities.stream()
                    .collect(Collectors.toMap(CraftEntity::getCraftId, craftEntity -> craftEntity.getIsTemplate() ? "关联工艺模板" : "关联工艺"));

            for (WorkOrderEntity entity : workOrderRecords) {
                entity.setStateName(WorkOrderStateEnum.getNameByCode(entity.getState()));
                entity.setSchedulingStateName(WorkOrderScheduleStateEnum.getNameByCode(entity.getSchedulingState()));
                entity.setAssignmentStateName(AssignmentStateEnum.getNameByType(entity.getAssignmentState()));
                entity.setInvestCheckResultName(InvestCheckResultEnum.getNameByCode(entity.getInvestCheckResult()));
                // 查询工艺工序
                if (Objects.isNull(workOrderSelectDTO.getIsShowCraftProcedureInfo()) || workOrderSelectDTO.getIsShowCraftProcedureInfo()) {
                    setCraftProcedure(entity);
                }
                // 设置入库数量
//                if (Objects.isNull(workOrderSelectDTO.getIsShowWarehouseInfo()) || workOrderSelectDTO.getIsShowWarehouseInfo()) {
//                    entity.setInputCount(getInventoryQuantity(entity.getWorkOrderNumber(), entity.getMaterialCode()));
                entity.setInputCount(entity.getInventoryQuantity());
//                }
                // 获取包装方案
                if (Objects.isNull(workOrderSelectDTO.getIsShowPackageSchemeInfo()) || workOrderSelectDTO.getIsShowPackageSchemeInfo()) {
                    entity.setPackageSchemeEntities(packageCodeSchemesMap.getOrDefault(entity.getPackageSchemeCode(), Collections.emptyList()));
                }
                // 展示单据类型名称
                entity.setBusinessTypeName(businessTypeMap.get(entity.getBusinessType()));
                entity.setOrderTypeName(ordertTypeMap.get(entity.getOrderType()));
                // 供应商名称
                entity.setSupplierName(supplierCodeNameMap.get(entity.getSupplierCode()));
                //展示工单执行状态
                entity.setExecutionStatusName(WorkOrderExecutionStateEnum.getNameByCode(entity.getExecutionStatus()));
                // 展示审核状态名称
                entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
                entity.setActualApproverName(nickNames.getOrDefault(entity.getActualApprover(), entity.getActualApprover()));
                entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
                entity.setApproverName(nickNames.getOrDefault(entity.getApprover(), entity.getApprover()));
                entity.setCreateName(nickNames.getOrDefault(entity.getCreateBy(), entity.getCreateBy()));
                entity.setUpdateByName(nickNames.getOrDefault(entity.getUpdateBy(), entity.getUpdateBy()));
                //设置物料字段
                if (Objects.isNull(workOrderSelectDTO.getIsShowMaterialFieldInfo()) || workOrderSelectDTO.getIsShowMaterialFieldInfo()) {
                    entity.setMaterialFields(materialSkuMap.get(ColumnUtil.getMaterialSku(entity.getMaterialCode(), entity.getSkuId())));
//                    entity.setMaterialFields(materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId()));
                }
                // 工单投产班组成员列表
                entity.setWorkOrderTeamEntities(workOrderTeamsMap.getOrDefault(entity.getWorkOrderNumber(), Collections.emptyList()));
                //拿到工作中心type
                WorkCenterEntity workCenterEntity = workCenterIdEntityMap.get(entity.getWorkCenterId());
                entity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
                entity.setWorkCenterTypeName(workCenterEntity == null ? null : WorkCenterTypeEnum.getNameByCode(workCenterEntity.getType()));
                entity.setWorkCenterRelevanceType(workCenterEntity == null ? null : workCenterEntity.getRelevanceType());
                // 展示生产基本单元名称
                List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = productBasicUnitMap.get(entity.getWorkOrderNumber());
                if (CollectionUtils.isNotEmpty(basicUnitRelationEntities)) {
                    String productBasicNames = basicUnitRelationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).collect(Collectors.joining(Constants.SEP));
                    entity.setTeamName(workCenterEntity.getType().equals(WorkCenterTypeEnum.TEAM.getCode()) ? productBasicNames : "");
                    entity.setDeviceName(workCenterEntity.getType().equals(WorkCenterTypeEnum.DEVICE.getCode()) ? productBasicNames : "");
                    entity.setLineName(workCenterEntity.getType().equals(WorkCenterTypeEnum.LINE.getCode()) ? productBasicNames : "");
                }
                entity.setProductBasicUnits(basicUnitRelationEntities);
                // 合格率
                double finishCount = entity.getFinishCount() == null ? 0 : entity.getFinishCount();
                double unqualified = entity.getUnqualified() == null ? 0 : entity.getUnqualified();
                if (finishCount == 0 && unqualified == 0) {
                    entity.setPassRate(0.0);
                } else {
                    entity.setPassRate(MathUtil.divideDouble(finishCount, unqualified + finishCount, 4));
                }
                // 展示制造单元模型编码
                if (entity.getLineId() != null) {
                    ProductionLineEntity productionLineEntity = lineIdEntityMap.get(entity.getLineId());
                    if (productionLineEntity != null) {
                        ModelEntity lineModel = lineModelIdMap.get(productionLineEntity.getModelId());
                        entity.setLineModelCode(lineModel == null ? null : lineModel.getCode());
                    }
                }
                entity.setMaterialCheckTypeName(MaterialMatchingEnum.getNameByType(entity.getMaterialCheckType()));
                // 设置拓展字段中文名
                setWorkOrderExtendFieldName(fieldRuleConfMap, entity);
                // 展示投产结果明细
                entity.setInvestCheckResultDetail(investCheckResultDetailMap.get(entity.getWorkOrderNumber()));
                // 查询关联资源
                String workCenterRelevanceType = entity.getWorkCenterRelevanceType();
                if (Objects.nonNull(workOrderSelectDTO.getIsShowAssociatedResourcesInfo()) && workOrderSelectDTO.getIsShowAssociatedResourcesInfo()
                        && StringUtils.isNotBlank(workCenterRelevanceType)) {
                    if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterRelevanceType)) {
                        // 获取工单关联制造单元资源
                        this.getRelevanceLineById(entity);
                    } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterRelevanceType)) {
                        // 获取工单关联设备资源
                        this.getRelevanceDeviceById(entity);
                    } else {
                        // 获取工单关联班组资源
                        this.getRelevanceTeamById(entity);
                    }
                }
                // 工艺信息应该读取工单本身关联的信息，而不是物料关联的工艺信息
                entity.setHaveCraft(relateCraftInfoMap.getOrDefault(entity.getCraftId(), "无工艺配置"));
            }
        }
        // 查询直通信息
        if (Objects.nonNull(workOrderSelectDTO.getIsShowDirectAccessInfo()) && workOrderSelectDTO.getIsShowDirectAccessInfo()) {
            showDirectAccessInfo(workOrderRecords);
        }
        // 单件理论工时
        Map<String, Double> theoryHourMap = workOrderTheoryHourMap(workOrderRecords);
        workOrderRecords.forEach(e -> {
            Double theoryHour = theoryHourMap.get(e.getWorkOrderNumber());
            e.setTheoryHour(theoryHour);
            e.setProduceTheoryHour(NullableDouble.of(theoryHour).mul(e.getFinishCount()).scale(2).cal());
            e.setPlanTheoryHour(NullableDouble.of(theoryHour).mul(e.getPlanQuantity()).scale(2).cal());
            e.setCapacityUnitName((String) redisTemplate.opsForValue().get(RedisKeyPrefix.WORK_ORDER_CAPACITY_UNIT_NAME_ + e.getWorkOrderNumber()));
        });
        return workOrderRecords;
    }

    /**
     * 设置拓展字段中文名
     */
    @Override
    public void setWorkOrderExtendFieldName(Map<String, String> fieldRuleConfMap, WorkOrderEntity entity) {
        entity.setWorkOrderExtendFieldOneName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldOneName" + entity.getWorkOrderExtendFieldOne(), entity.getWorkOrderExtendFieldOne()));
        entity.setWorkOrderExtendFieldTwoName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldTwoName" + entity.getWorkOrderExtendFieldTwo(), entity.getWorkOrderExtendFieldTwo()));
        entity.setWorkOrderExtendFieldThreeName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldThreeName" + entity.getWorkOrderExtendFieldThree(), entity.getWorkOrderExtendFieldThree()));
        entity.setWorkOrderExtendFieldFourName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldFourName" + entity.getWorkOrderExtendFieldFour(), entity.getWorkOrderExtendFieldFour()));
        entity.setWorkOrderExtendFieldFiveName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldFiveName" + entity.getWorkOrderExtendFieldFive(), entity.getWorkOrderExtendFieldFive()));
        entity.setWorkOrderExtendFieldSixName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldSixName" + entity.getWorkOrderExtendFieldSix(), entity.getWorkOrderExtendFieldSix()));
        entity.setWorkOrderExtendFieldSevenName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldSevenName" + entity.getWorkOrderExtendFieldSeven(), entity.getWorkOrderExtendFieldSeven()));
        entity.setWorkOrderExtendFieldEightName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldEightName" + entity.getWorkOrderExtendFieldEight(), entity.getWorkOrderExtendFieldEight()));
        entity.setWorkOrderExtendFieldNineName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldNineName" + entity.getWorkOrderExtendFieldNine(), entity.getWorkOrderExtendFieldNine()));
        entity.setWorkOrderExtendFieldTenName(fieldRuleConfMap.getOrDefault("workOrderExtendFieldTenName" + entity.getWorkOrderExtendFieldTen(), entity.getWorkOrderExtendFieldTen()));
        entity.setWorkOrderMaterialExtendFieldOneName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldOneName" + entity.getWorkOrderMaterialExtendFieldOne(), entity.getWorkOrderMaterialExtendFieldOne()));
        entity.setWorkOrderMaterialExtendFieldTwoName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldTwoName" + entity.getWorkOrderMaterialExtendFieldTwo(), entity.getWorkOrderMaterialExtendFieldTwo()));
        entity.setWorkOrderMaterialExtendFieldThreeName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldThreeName" + entity.getWorkOrderMaterialExtendFieldThree(), entity.getWorkOrderMaterialExtendFieldThree()));
        entity.setWorkOrderMaterialExtendFieldFourName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldFourName" + entity.getWorkOrderMaterialExtendFieldFour(), entity.getWorkOrderMaterialExtendFieldFour()));
        entity.setWorkOrderMaterialExtendFieldFiveName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldFiveName" + entity.getWorkOrderMaterialExtendFieldFive(), entity.getWorkOrderMaterialExtendFieldFive()));
        entity.setWorkOrderMaterialExtendFieldSixName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldSixName" + entity.getWorkOrderMaterialExtendFieldSix(), entity.getWorkOrderMaterialExtendFieldSix()));
        entity.setWorkOrderMaterialExtendFieldSevenName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldSevenName" + entity.getWorkOrderMaterialExtendFieldSeven(), entity.getWorkOrderMaterialExtendFieldSeven()));
        entity.setWorkOrderMaterialExtendFieldEightName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldEightName" + entity.getWorkOrderMaterialExtendFieldEight(), entity.getWorkOrderMaterialExtendFieldEight()));
        entity.setWorkOrderMaterialExtendFieldNineName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldNineName" + entity.getWorkOrderMaterialExtendFieldNine(), entity.getWorkOrderMaterialExtendFieldNine()));
        entity.setWorkOrderMaterialExtendFieldTenName(fieldRuleConfMap.getOrDefault("workOrderMaterialExtendFieldTenName" + entity.getWorkOrderMaterialExtendFieldTen(), entity.getWorkOrderMaterialExtendFieldTen()));
    }

    private List<Integer> listRelationId(String projectDefineName, String contractName, String type) {
        LambdaQueryWrapper<ReceiptProjectContractEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReceiptProjectContractEntity::getType, type);
        wrapper.like(StrUtil.isNotEmpty(projectDefineName), ReceiptProjectContractEntity::getProjectDefineName, projectDefineName);
        wrapper.like(StrUtil.isNotEmpty(contractName), ReceiptProjectContractEntity::getContractName, contractName);
        List<ReceiptProjectContractEntity> list = receiptProjectContractService.list(wrapper);
        if (list.isEmpty()) {
            return new ArrayList<>();
        }
        return list.stream().map(ReceiptProjectContractEntity::getRelationId).collect(Collectors.toList());
    }

    @Override
    public void setProjectContract(List<WorkOrderEntity> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<String> workOrderNumbers = list.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        ProjectOrderSelectDTO dto = ProjectOrderSelectDTO.builder()
                .relationType(ReceiptTypePMSEnum.PRUDUCT_WORK_ORDER.getCode())
                .relationNumbers(workOrderNumbers).build();
        List<ReceiptProjectContractEntity> receiptList = receiptProjectContractService.getListByOrder(dto);
        if (CollectionUtil.isEmpty(receiptList)) {
            return;
        }
        Map<String, ReceiptProjectContractEntity> receiptMap = receiptList.stream().collect(Collectors.toMap(ReceiptProjectContractEntity::getRelationNumber, Function.identity(), (k1, k2) -> k1));
        for (WorkOrderEntity item : list) {
            if (!receiptMap.containsKey(item.getWorkOrderNumber())) {
                continue;
            }
            ReceiptProjectContractEntity receiptProjectContractEntity = receiptMap.get(item.getWorkOrderNumber());
            item.setProjectDefineId(ObjectUtil.isEmpty(receiptProjectContractEntity.getProjectDefineId()) ? "" : receiptProjectContractEntity.getProjectDefineId() + "");
            item.setContractId(ObjectUtil.isEmpty(receiptProjectContractEntity.getContractId()) ? "" : receiptProjectContractEntity.getContractId() + "");
            item.setProjectDefineName(receiptProjectContractEntity.getProjectDefineName());
            item.setContractName(receiptProjectContractEntity.getContractName());
            item.setProjectNodeIds(receiptProjectContractEntity.getProjectNodeIds());
            item.setProjectNodeName(receiptProjectContractEntity.getProjectNodeName());
        }
    }

    /**
     * 生产工单条件查询
     *
     * @param selectDTO 查询条件
     * @param wrapper
     */
    private void conditionQuery(WorkOrderSelectDTO selectDTO, LambdaQueryWrapper<WorkOrderEntity> wrapper) {
        wrapper
                // 工单执行状态
                .eq(StringUtils.isNotEmpty(selectDTO.getExecutionStatus()), WorkOrderEntity::getExecutionStatus, selectDTO.getExecutionStatus())
                // 审批人
                .eq(StringUtils.isNotEmpty(selectDTO.getApprover()), WorkOrderEntity::getApprover, selectDTO.getApprover())
                // 是否为父工单
                .eq(!Objects.isNull(selectDTO.getIsParent()), WorkOrderEntity::getIsPid, selectDTO.getIsParent())
                // 工单名称
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderName()), WorkOrderEntity::getWorkOrderName, selectDTO.getWorkOrderName())
                // 工单号
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderNumber()), WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber())
                .in(CollectionUtils.isNotEmpty(selectDTO.getWorkOrderNumbers()), WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumbers())
                // 销售订单（模糊匹配）
                .like(StringUtils.isNotEmpty(selectDTO.getSaleOrderNumber()), WorkOrderEntity::getSaleOrderNumber, selectDTO.getSaleOrderNumber())
                // 销售订单(精准匹配)
                .eq(StringUtils.isNotEmpty(selectDTO.getFullSaleOrderNumber()), WorkOrderEntity::getSaleOrderNumber, selectDTO.getFullSaleOrderNumber())
                // 生产订单
                .like(StringUtils.isNotEmpty(selectDTO.getProductOrderNumber()), WorkOrderEntity::getProductOrderNumber, selectDTO.getProductOrderNumber())
                // 备注
                .like(StringUtils.isNotEmpty(selectDTO.getRemark()), WorkOrderEntity::getRemark, selectDTO.getRemark())
                //创建时间
                .between(StringUtils.isNoneBlank(selectDTO.getStartTime(), selectDTO.getEndTime()), WorkOrderEntity::getCreateDate, selectDTO.getStartTime(), selectDTO.getEndTime())
                //修改时间
                .between(StringUtils.isNoneBlank(selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime()), WorkOrderEntity::getUpdateDate, selectDTO.getUpdateStartTime(), selectDTO.getUpdateEndTime())
                // 计划开始时间
                .between(StringUtils.isNoneBlank(selectDTO.getPlanStartTime(), selectDTO.getPlanEndTime()), WorkOrderEntity::getStartDate, selectDTO.getPlanStartTime(), selectDTO.getPlanEndTime())
                // 实际开始时间
                .between(StringUtils.isNoneBlank(selectDTO.getActualStartTime(), selectDTO.getActualEndTime()), WorkOrderEntity::getActualStartDate, selectDTO.getActualStartTime(), selectDTO.getActualEndTime())
                // 计划完成时间
                .between(StringUtils.isNoneBlank(selectDTO.getPlanCompleteStartTime(), selectDTO.getPlanCompleteEndTime()), WorkOrderEntity::getEndDate, selectDTO.getPlanCompleteStartTime(), selectDTO.getPlanCompleteEndTime())
                // 实际完成时间
                .between(StringUtils.isNoneBlank(selectDTO.getActualCompleteStartTime(), selectDTO.getActualCompleteEndTime()), WorkOrderEntity::getActualEndDate, selectDTO.getActualCompleteStartTime(), selectDTO.getActualCompleteEndTime())
                // 排产状态
                .eq(!Objects.isNull(selectDTO.getPlanState()), WorkOrderEntity::getPlanState, selectDTO.getPlanState())
                // 打印状态
                .eq(!ObjectUtils.isEmpty(selectDTO.getIsPrint()), WorkOrderEntity::getIsPrint, selectDTO.getIsPrint())
                // 排程顺序
                .eq(!Objects.isNull(selectDTO.getSchedulingSequence()), WorkOrderEntity::getSchedulingSequence, selectDTO.getSchedulingSequence())
                //排程状态
                .eq(!Objects.isNull(selectDTO.getSchedulingState()), WorkOrderEntity::getSchedulingState, selectDTO.getSchedulingState())
                .eq(StringUtils.isNotEmpty(selectDTO.getMaterialCheckType()), WorkOrderEntity::getMaterialCheckType, selectDTO.getMaterialCheckType())
                .eq(!Objects.isNull(selectDTO.getMaterialCheckReplace()), WorkOrderEntity::getMaterialCheckReplace, selectDTO.getMaterialCheckReplace())
                // 领料状态名称
                .eq(StringUtils.isNotEmpty(selectDTO.getPickingStateName()), WorkOrderEntity::getPickingStateName, selectDTO.getPickingStateName())
                // 投产检查结果（true--通过  false--不通过）
                .eq(Objects.nonNull(selectDTO.getInvestCheckResult()), WorkOrderEntity::getInvestCheckResult, selectDTO.getInvestCheckResult())
                //业务单元查询
                .eq(StringUtils.isNotEmpty(selectDTO.getBusinessUnitCode()), WorkOrderEntity::getBusinessUnitCode, selectDTO.getBusinessUnitCode())
                .in(CollectionUtils.isNotEmpty(selectDTO.getBusinessUnitCodeList()), WorkOrderEntity::getBusinessUnitCode, selectDTO.getBusinessUnitCodeList())
                .like(StringUtils.isNotEmpty(selectDTO.getBusinessUnitName()), WorkOrderEntity::getBusinessUnitName, selectDTO.getBusinessUnitName())
                //客户编码
                .like(StringUtils.isNotEmpty(selectDTO.getCustomerCode()), WorkOrderEntity::getCustomerCode, selectDTO.getCustomerCode())
                //客户名称
                .like(StringUtils.isNotEmpty(selectDTO.getCustomerName()), WorkOrderEntity::getCustomerName, selectDTO.getCustomerName())
                //客户物料编码
                .like(StringUtils.isNotEmpty(selectDTO.getCustomerMaterialCode()), WorkOrderEntity::getCustomerMaterialCode, selectDTO.getCustomerMaterialCode())
                //客户物料名称
                .like(StringUtils.isNotEmpty(selectDTO.getCustomerMaterialName()), WorkOrderEntity::getCustomerMaterialName, selectDTO.getCustomerMaterialName())
                //客户物料规格
                .like(StringUtils.isNotEmpty(selectDTO.getCustomerSpecification()), WorkOrderEntity::getCustomerSpecification, selectDTO.getCustomerSpecification())
                // 工单id
                .in(CollectionUtils.isNotEmpty(selectDTO.getWorkOrderIds()), WorkOrderEntity::getWorkOrderId, selectDTO.getWorkOrderIds())
                //设备id
//                .eq(selectDTO.getDeviceId() != null, WorkOrderEntity::getDeviceId, selectDTO.getDeviceId())
                // 生产基本单元id
//                .eq(Objects.nonNull(selectDTO.getProductionBasicUnitId()), WorkOrderEntity::getProductionBasicUnitId, selectDTO.getProductionBasicUnitId())
                // 工单拓展字段
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldOne()), WorkOrderEntity::getWorkOrderExtendFieldOne, selectDTO.getWorkOrderExtendFieldOne())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldTwo()), WorkOrderEntity::getWorkOrderExtendFieldTwo, selectDTO.getWorkOrderExtendFieldTwo())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldThree()), WorkOrderEntity::getWorkOrderExtendFieldThree, selectDTO.getWorkOrderExtendFieldThree())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldFour()), WorkOrderEntity::getWorkOrderExtendFieldFour, selectDTO.getWorkOrderExtendFieldFour())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldFive()), WorkOrderEntity::getWorkOrderExtendFieldFive, selectDTO.getWorkOrderExtendFieldFive())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldSix()), WorkOrderEntity::getWorkOrderExtendFieldSix, selectDTO.getWorkOrderExtendFieldSix())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldSeven()), WorkOrderEntity::getWorkOrderExtendFieldSeven, selectDTO.getWorkOrderExtendFieldSeven())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldEight()), WorkOrderEntity::getWorkOrderExtendFieldEight, selectDTO.getWorkOrderExtendFieldEight())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldNine()), WorkOrderEntity::getWorkOrderExtendFieldNine, selectDTO.getWorkOrderExtendFieldNine())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderExtendFieldTen()), WorkOrderEntity::getWorkOrderExtendFieldTen, selectDTO.getWorkOrderExtendFieldTen())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldOne()), WorkOrderEntity::getWorkOrderMaterialExtendFieldOne, selectDTO.getWorkOrderMaterialExtendFieldOne())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldTwo()), WorkOrderEntity::getWorkOrderMaterialExtendFieldTwo, selectDTO.getWorkOrderMaterialExtendFieldTwo())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldThree()), WorkOrderEntity::getWorkOrderMaterialExtendFieldThree, selectDTO.getWorkOrderMaterialExtendFieldThree())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldFour()), WorkOrderEntity::getWorkOrderMaterialExtendFieldFour, selectDTO.getWorkOrderMaterialExtendFieldFour())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldFive()), WorkOrderEntity::getWorkOrderMaterialExtendFieldFive, selectDTO.getWorkOrderMaterialExtendFieldFive())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldSix()), WorkOrderEntity::getWorkOrderMaterialExtendFieldSix, selectDTO.getWorkOrderMaterialExtendFieldSix())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldSeven()), WorkOrderEntity::getWorkOrderMaterialExtendFieldSeven, selectDTO.getWorkOrderMaterialExtendFieldSeven())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldEight()), WorkOrderEntity::getWorkOrderMaterialExtendFieldEight, selectDTO.getWorkOrderMaterialExtendFieldEight())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldNine()), WorkOrderEntity::getWorkOrderMaterialExtendFieldNine, selectDTO.getWorkOrderMaterialExtendFieldNine())
                .like(StringUtils.isNotEmpty(selectDTO.getWorkOrderMaterialExtendFieldTen()), WorkOrderEntity::getWorkOrderMaterialExtendFieldTen, selectDTO.getWorkOrderMaterialExtendFieldTen())
                .notIn(Boolean.TRUE.equals(selectDTO.getIsHiddenCreatedState()), WorkOrderEntity::getState, WorkOrderStateEnum.CREATED.getCode(), WorkOrderStateEnum.RELEASED.getCode())

        ;
        //区间时间筛选
        if (StringUtils.isNotBlank(selectDTO.getIntervalEndTime())) {
            wrapper.le(WorkOrderEntity::getStartDate, selectDTO.getIntervalEndTime());
        }
        if (StringUtils.isNotBlank(selectDTO.getIntervalStartTime())) {
            wrapper.ge(WorkOrderEntity::getEndDate, selectDTO.getIntervalStartTime());
        }
        // 项目合同
        //if (StrUtil.isNotEmpty(selectDTO.getProjectDefineName()) || StrUtil.isNotEmpty(selectDTO.getContractName())) {
        //    List<Integer> relationIds = listRelationId(selectDTO.getProjectDefineName(), selectDTO.getContractName(), ReceiptTypePMSEnum.PRUDUCT_WORK_ORDER.getCode());
        //    if (relationIds.isEmpty()) {
        //        wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
        //    } else {
        //        wrapper.in(WorkOrderEntity::getWorkOrderId, relationIds);
        //    }
        //}
        // 工单派工状态
        if (StringUtils.isNotEmpty(selectDTO.getAssignmentState())) {
            List<String> strings = Arrays.asList(selectDTO.getAssignmentState().split(Constants.SEP));
            wrapper.in(WorkOrderEntity::getAssignmentState, strings);
        }
        // 单据类型
        if (StringUtils.isNotEmpty(selectDTO.getOrderType())) {
            List<String> types = Arrays.stream(selectDTO.getOrderType().split(Constants.SEP)).collect(Collectors.toList());
            wrapper.in(WorkOrderEntity::getOrderType, types);
        }
        // 业务类型
        if (StringUtils.isNotEmpty(selectDTO.getBusinessType())) {
            List<String> types = Arrays.stream(selectDTO.getBusinessType().split(Constants.SEP)).collect(Collectors.toList());
            wrapper.in(WorkOrderEntity::getBusinessType, types);
        }
        // 班组成员账号
        if (StringUtils.isNotBlank(selectDTO.getMemberName())) {
            List<String> workOrderNumbers = workOrderTeamService.lambdaQuery().select(WorkOrderTeamEntity::getWorkOrderNumber)
                    .eq(WorkOrderTeamEntity::getMemberName, selectDTO.getMemberName())
                    .list().stream().map(WorkOrderTeamEntity::getWorkOrderNumber).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }
        // 生产基本单元相关查询
        productBasicUnitConditionQuery(selectDTO, wrapper);
        // 基本生产单元类型
        if (StringUtils.isNotBlank(selectDTO.getWorkCenterType())) {
            List<String> workCenterTypes = Stream.of(selectDTO.getWorkCenterType().split(Constant.SEP)).collect(Collectors.toList());
            List<Integer> workCenterTypeIds = workCenterService.lambdaQuery().in(WorkCenterEntity::getType, workCenterTypes).list().stream().map(WorkCenterEntity::getId).collect(Collectors.toList());
            wrapper.in(WorkOrderEntity::getProductionBasicUnitId, workCenterTypeIds);
        }
        //作业工单筛选
        if (selectDTO.getIsAssignment() != null) {
            LambdaQueryWrapper<WorkCenterEntity> workCenterQueryWrapper = new LambdaQueryWrapper<>();
            workCenterQueryWrapper.eq(WorkCenterEntity::getIsOperation, selectDTO.getIsAssignment());
            List<WorkCenterEntity> workCenterEntityList = workCenterService.list(workCenterQueryWrapper);
            if (CollectionUtils.isEmpty(workCenterEntityList)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getWorkCenterId, workCenterEntityList.stream().map(WorkCenterEntity::getId).collect(Collectors.toList()));
            }
        }
        // 审批状态
        if (StringUtils.isNotBlank(selectDTO.getApprovalStatus())) {
            List<Integer> stateList = Arrays.stream(selectDTO.getApprovalStatus().split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(WorkOrderEntity::getApprovalStatus, stateList);
        }
        // 前端多状态查询有的传state,有的states
        // 多个状态过滤
        if (StringUtils.isNotBlank(selectDTO.getState()) || StringUtils.isNotBlank(selectDTO.getStates())) {
            Set<Integer> states = StringUtils.isNotBlank(selectDTO.getState()) ? Arrays.stream(selectDTO.getState().split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toSet()) : new HashSet<>();
            Set<Integer> stateList = StringUtils.isNotBlank(selectDTO.getStates()) ? Arrays.stream(selectDTO.getStates().split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toSet()) : new HashSet<>();
            stateList.addAll(states);
            wrapper.in(WorkOrderEntity::getState, stateList);
        }
        // 排除状态过滤
        if (StringUtils.isNotBlank(selectDTO.getExcludeStates())) {
            Set<Integer> excludeStates = Arrays.stream(selectDTO.getExcludeStates().split(Constant.SEP)).map(Integer::parseInt).collect(Collectors.toSet());
            wrapper.notIn(WorkOrderEntity::getState, excludeStates);
        }

        // 完成率过滤
        completionRateFilter(selectDTO.getCompletionRate(), wrapper);
        if (CollectionUtils.isNotEmpty(selectDTO.getSortList())) {
            for (SortDTO sortDTO : selectDTO.getSortList()) {
                // 完成率排序
                wrapper.orderBy("completionRate".equals(sortDTO.getColumn()), !sortDTO.getDesc(), WorkOrderEntity::getProgress);
            }
        }

        //使用物料条件查询
        MaterialSkuEntitySelectDTO skuSelectDTO = MaterialSkuEntitySelectDTO.builder().materialFields(selectDTO.getMaterialFields())
                .materialSkus(selectDTO.getMaterialSkus()).build();
        MaterialSkuDTO skuDTO = materialService.codesAndSkuIdsByMaterialFields(skuSelectDTO);
        if (skuDTO.getUseConditions()) {
            List<String> materialCodes = skuDTO.getMaterialCodes();
            List<Integer> skuIds = skuDTO.getSkuIds();
            if (CollectionUtils.isEmpty(materialCodes) && CollectionUtils.isEmpty(skuIds)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(CollectionUtils.isNotEmpty(materialCodes), WorkOrderEntity::getMaterialCode, materialCodes);
                wrapper.in(CollectionUtils.isNotEmpty(skuIds), WorkOrderEntity::getSkuId, skuIds);
            }
        }
        // 多个生产订单过滤
        if (CollectionUtils.isNotEmpty(selectDTO.getProductOrderIds())) {
            List<OrderWorkOrderEntity> workOrderRelations = orderWorkOrderService.listOrderWorkOrderRelations(selectDTO.getProductOrderIds(), OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
            if (CollectionUtils.isEmpty(workOrderRelations)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<Integer> workOrderIds = workOrderRelations.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
                wrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
            }
        }
        //支持多个工作中心id查询
        //支持多个工序id查询
        buildWrapper(wrapper, WorkOrderWrapperDTO.builder()
                .workCenterIds(selectDTO.getWorkCenterId())
                .procedureIds(selectDTO.getFinalProcedureIds())
                .build()
        );
        // 工序名称过滤
        if (StringUtils.isNotEmpty(selectDTO.getProcedureName())) {
            List<WorkOrderProcedureRelationEntity> workOrderProcedureRelations = workOrderProcedureRelationService.lambdaQuery()
                    .like(WorkOrderProcedureRelationEntity::getProcedureName, selectDTO.getProcedureName())
                    .list();
            if (CollectionUtils.isEmpty(workOrderProcedureRelations)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<String> workOrderNumbers = workOrderProcedureRelations.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }
        // 工序名称过滤
        if (StringUtils.isNotEmpty(selectDTO.getProcedureNames())) {
            List<String> procedureNames = Arrays.asList(selectDTO.getProcedureNames().split(Constant.SEP));
            List<WorkOrderProcedureRelationEntity> workOrderProcedureRelations = workOrderProcedureRelationService.lambdaQuery()
                    .in(WorkOrderProcedureRelationEntity::getProcedureName, procedureNames)
                    .list();
            if (CollectionUtils.isEmpty(workOrderProcedureRelations)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<String> workOrderNumbers = workOrderProcedureRelations.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }
        // 工序编码过滤
        if (StringUtils.isNotEmpty(selectDTO.getProcedureCode())) {
            ProcedureEntity procedureEntity = procedureService.lambdaQuery().eq(ProcedureEntity::getProcedureCode, selectDTO.getProcedureCode()).one();
            List<WorkOrderProcedureRelationEntity> workOrderProcedureRelations = workOrderProcedureRelationService.lambdaQuery()
                    .eq(WorkOrderProcedureRelationEntity::getProcedureId, procedureEntity.getProcedureId())
                    .list();
            if (CollectionUtils.isEmpty(workOrderProcedureRelations)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<String> workOrderNumbers = workOrderProcedureRelations.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }
        // 工艺工序过滤
        if (CollectionUtils.isNotEmpty(selectDTO.getCraftProcedureIds())) {
            List<WorkOrderProcedureRelationEntity> workOrderProcedureRelations = workOrderProcedureRelationService.lambdaQuery()
                    .in(WorkOrderProcedureRelationEntity::getCraftProcedureId, selectDTO.getCraftProcedureIds())
                    .list();
            if (CollectionUtils.isEmpty(workOrderProcedureRelations)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<Integer> workOrderIds = workOrderProcedureRelations.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderId).distinct().collect(Collectors.toList());
                wrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
            }
        }
        // 创建人、更新人过滤
        if (StringUtils.isNotBlank(selectDTO.getCreateName())) {
            List<String> usernames = userService.listKeyword(selectDTO.getCreateName()).stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(usernames)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getCreateBy, usernames);
            }
        }
        if (StringUtils.isNotBlank(selectDTO.getUpdateName())) {
            List<String> usernames = userService.listKeyword(selectDTO.getUpdateName()).stream().map(SysUserEntity::getUsername).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(usernames)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getUpdateBy, usernames);
            }
        }
        // 通过关联资源id和类型找到对应的工单
        if (StringUtils.isNoneBlank(selectDTO.getRelevanceResourceType(), selectDTO.getRelevanceResourceIds())) {
            List<Integer> workOrderIds = new ArrayList<>();
            List<String> relevanceResourceIds = Arrays.asList(selectDTO.getRelevanceResourceIds().split(Constant.SEP));
            switch (WorkCenterTypeEnum.getByCode(selectDTO.getRelevanceResourceType())) {
                // 制造单元
                case LINE:
                    workOrderIds = workOrderLineRelevanceService.lambdaQuery()
                            .in(WorkOrderLineRelevanceEntity::getLineId, relevanceResourceIds)
                            .list().stream()
                            .map(WorkOrderLineRelevanceEntity::getWorkOrderId).collect(Collectors.toList());
                    break;
                // 班组
                case TEAM:
                    workOrderIds = workOrderTeamRelevanceService.lambdaQuery()
                            .in(WorkOrderTeamRelevanceEntity::getTeamId, relevanceResourceIds)
                            .list().stream()
                            .map(WorkOrderTeamRelevanceEntity::getWorkOrderId).collect(Collectors.toList());
                    break;
                // 设备
                case DEVICE:
                    workOrderIds = workOrderDeviceRelevanceService.lambdaQuery()
                            .in(WorkOrderDeviceRelevanceEntity::getDeviceId, relevanceResourceIds)
                            .list().stream()
                            .map(WorkOrderDeviceRelevanceEntity::getWorkOrderId).collect(Collectors.toList());
                    break;
            }
            wrapper.in(CollectionUtils.isNotEmpty(workOrderIds), WorkOrderEntity::getWorkOrderId, workOrderIds);
        }

        wrapper.orderByDesc(WorkOrderEntity::getCreateDate).orderByDesc(WorkOrderEntity::getWorkOrderId);
    }

    private void productBasicUnitConditionQuery(WorkOrderSelectDTO selectDTO, LambdaQueryWrapper<WorkOrderEntity> wrapper) {
        if (CollectionUtils.isNotEmpty(selectDTO.getInvestBasicUnits())) {
            List<String> workOrderNumbers = new ArrayList<>();
            List<ProductionResourceDTO> lineQuerys = selectDTO.getInvestBasicUnits().stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode())).collect(Collectors.toList());
            List<ProductionResourceDTO> deviceQuerys = selectDTO.getInvestBasicUnits().stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.DEVICE.getCode())).collect(Collectors.toList());
            List<ProductionResourceDTO> teamQuerys = selectDTO.getInvestBasicUnits().stream().filter(o -> o.getWorkCenterType().equals(WorkCenterTypeEnum.TEAM.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lineQuerys)) {
                List<Integer> productionBasicUnitIds = lineQuerys.stream().map(ProductionResourceDTO::getProductionBasicUnitId).collect(Collectors.toList());
                workOrderNumbers.addAll(basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.LINE.getCode())
                        .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, productionBasicUnitIds)
                        .eq(WorkOrderBasicUnitRelationEntity::getIsProducing, true)
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(deviceQuerys)) {
                List<Integer> productionBasicUnitIds = deviceQuerys.stream().map(ProductionResourceDTO::getProductionBasicUnitId).collect(Collectors.toList());
                workOrderNumbers.addAll(basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.DEVICE.getCode())
                        .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, productionBasicUnitIds)
                        .eq(WorkOrderBasicUnitRelationEntity::getIsProducing, true)
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(teamQuerys)) {
                List<Integer> productionBasicUnitIds = teamQuerys.stream().map(ProductionResourceDTO::getProductionBasicUnitId).collect(Collectors.toList());
                workOrderNumbers.addAll(basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.TEAM.getCode())
                        .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, productionBasicUnitIds)
                        .eq(WorkOrderBasicUnitRelationEntity::getIsProducing, true)
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }

        if (Objects.nonNull(selectDTO.getProductionBasicUnitId())) {
            List<String> workOrderNumbers = basicUnitRelationService.lambdaQuery()
                    .eq(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, selectDTO.getProductionBasicUnitId())
                    .list().stream()
                    .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }
        // 生产基本单元相关查询
        if (Objects.nonNull(selectDTO.getDeviceId())) {
            List<String> workOrderNumbers = basicUnitRelationService.lambdaQuery()
                    .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.DEVICE.getCode())
                    .eq(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, selectDTO.getDeviceId())
                    .list().stream()
                    .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }
        if (StringUtils.isNotBlank(selectDTO.getDeviceCode())) {
            List<DeviceEntity> devices = deviceService.getByDeviceCodes(selectDTO.getDeviceCode());
            if (CollectionUtils.isEmpty(devices)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<String> workOrderNumbers = basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.DEVICE.getCode())
                        .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, devices.stream().map(DeviceEntity::getDeviceId).collect(Collectors.toList()))
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(workOrderNumbers)) {
                    wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
                } else {
                    wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
                }
            }
        }
        if (StringUtils.isNotBlank(selectDTO.getDeviceCodes())) {
            List<String> deviceCodes = Arrays.asList(selectDTO.getDeviceCodes().split(Constant.SEP));
            List<Integer> deviceIds = deviceService.lambdaQuery().in(DeviceEntity::getDeviceCode, deviceCodes)
                    .list().stream().map(DeviceEntity::getDeviceId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deviceIds)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<String> workOrderNumbers = basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.DEVICE.getCode())
                        .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, deviceIds)
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(workOrderNumbers)) {
                    wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
                } else {
                    wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
                }
            }
        }
        // 支持多个制造单元查询
        if (StringUtils.isNotBlank(selectDTO.getLineId()) || StringUtils.isNotBlank(selectDTO.getLineModelId())) {
            List<Integer> lineIds = StringUtils.isNotBlank(selectDTO.getLineId()) ? Arrays.stream(selectDTO.getLineId().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList()) : new ArrayList<>();
            if (StringUtils.isNotBlank(selectDTO.getLineModelId())) {
                List<Integer> modelLineIds = productionLineService.lambdaQuery()
                        .in(ProductionLineEntity::getModelId, Arrays.stream(selectDTO.getLineModelId().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList()))
                        .list().stream()
                        .map(ProductionLineEntity::getProductionLineId)
                        .collect(Collectors.toList());
                if (StringUtils.isNotBlank(selectDTO.getLineId())) {
                    // 通过制造单元模型查询出来的制造单元,应该与直接制造单元筛选取交集
                    lineIds.retainAll(modelLineIds);
                } else {
                    lineIds = modelLineIds;
                }
            }
            if (CollectionUtils.isEmpty(lineIds)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                List<String> workOrderNumbers = basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.LINE.getCode())
                        .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, lineIds)
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(workOrderNumbers)) {
                    wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
                } else {
                    wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
                }
            }
        }
        if (StringUtils.isNotBlank(selectDTO.getLineCodes())) {
            List<String> lineCodes = StringUtils.isNotBlank(selectDTO.getLineCodes()) ? Arrays.stream(selectDTO.getLineCodes().split(Constant.SEP)).collect(Collectors.toList()) : new ArrayList<>();
            List<ProductionLineEntity> lineEntities = productionLineService.lambdaQuery()
                    .in(ProductionLineEntity::getProductionLineCode, lineCodes)
                    .list();
            if (CollectionUtils.isEmpty(lineEntities)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                // 走索引
                List<Integer> lineIds = lineEntities.stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
                List<String> workOrderNumbers = basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.LINE.getCode())
                        .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, lineIds)
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(workOrderNumbers)) {
                    wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
                } else {
                    wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
                }
            }
        }
        if (StringUtils.isNotBlank(selectDTO.getTeamIds())) {
            List<Integer> teamIds = Arrays.stream(selectDTO.getTeamIds().split(Constants.SEP)).map(Integer::valueOf).collect(Collectors.toList());
            List<String> workOrderNumbers = basicUnitRelationService.lambdaQuery()
                    .eq(WorkOrderBasicUnitRelationEntity::getWorkCenterType, WorkCenterTypeEnum.TEAM.getCode())
                    .in(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId, teamIds)
                    .list().stream()
                    .map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
            } else {
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }

        // 下推状态过滤
        if (StringUtils.isNotBlank(selectDTO.getPushDownState())) {
            LambdaQueryWrapper<OrderPushDownIdentifierEntity> identifierWrapper = Wrappers.lambdaQuery();
            identifierWrapper.eq(OrderPushDownIdentifierEntity::getOrderType, OrderNumTypeEnum.WORK_ORDER.getTypeCode())
                    .eq(OrderPushDownIdentifierEntity::getState, selectDTO.getPushDownState());

            // 如果指定了目标单据类型，则添加目标单据类型过滤
            if (StringUtils.isNotBlank(selectDTO.getTargetOrderType())) {
                identifierWrapper.eq(OrderPushDownIdentifierEntity::getTargetOrderType, selectDTO.getTargetOrderType());
            }

            if (PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode().equals(selectDTO.getPushDownState())) {
                // 查询未下推的工单：不在下推标识表中的工单
                List<OrderPushDownIdentifierEntity> identifiers = orderPushDownIdentifierService.list(identifierWrapper);
                if (CollectionUtils.isNotEmpty(identifiers)) {
                    List<Integer> pushedWorkOrderIds = identifiers.stream()
                            .map(identifier -> Integer.valueOf(identifier.getOrderMaterialId()))
                            .distinct()
                            .collect(Collectors.toList());
                    wrapper.notIn(WorkOrderEntity::getWorkOrderId, pushedWorkOrderIds);
                }
                // 如果没有下推记录，则所有工单都是未下推状态，不需要额外过滤
            } else {
                // 查询部分下推或已下推的工单：在下推标识表中且状态匹配的工单
                List<OrderPushDownIdentifierEntity> identifiers = orderPushDownIdentifierService.list(identifierWrapper);
                if (CollectionUtils.isEmpty(identifiers)) {
                    wrapper.isNull(WorkOrderEntity::getWorkOrderId);
                } else {
                    List<Integer> workOrderIds = identifiers.stream()
                            .map(identifier -> Integer.valueOf(identifier.getOrderMaterialId()))
                            .distinct()
                            .collect(Collectors.toList());
                    wrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
                }
            }
        }
    }

    /**
     * 获取入库数量
     * 根据生产工单 入库单(类型为成品入库)
     */
    @Override
    public Double getInventoryQuantity(String workOrderNumber, String materialCode) {
        // 生产工单对应 成品入库单关联的物料记录
        List<StockMaterialDetailEntity> stockMaterialDetails = JacksonUtil.getResponseArray(inAndOutInterface.listStockMaterialDetailsByType(workOrderNumber, InputOrOutputRelateTypeEnum.IN_WORK_ORDER), StockMaterialDetailEntity.class);
        // 物料对应的入库数量
        Map<String, Double> materialCodeInventoryQuantityMap = new HashMap<>(8);
        stockMaterialDetails.stream()
                .filter(record -> !Objects.isNull(record.getActualAmount()))
                .forEach(record -> materialCodeInventoryQuantityMap.merge(record.getProductCode(), record.getActualAmount(), Double::sum));
        return materialCodeInventoryQuantityMap.getOrDefault(materialCode, 0.0);
    }

    /**
     * 获取工单号
     *
     * @param wrapper
     * @return
     */
    private List<String> getWorkOrderNumbers(LambdaQueryWrapper<WorkOrderEntity> wrapper) {
        wrapper.select(WorkOrderEntity::getWorkOrderNumber);
        List<WorkOrderEntity> list = this.list(wrapper);
        return list.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
    }

    /**
     * 生产工单统计汇总
     *
     * @param queryWrapper
     * @return
     */
    private WorkOrderPageDTO getSumForWorkOrders(QueryWrapper<WorkOrderEntity> queryWrapper) {
        queryWrapper.select("IFNULL(sum(plan_quantity),0) AS plan_quantity",
                "IFNULL(sum(finish_count),0) AS finish_count",
                "IFNULL(sum(in_stock_count),0) AS in_stock_count",
                "IFNULL(sum(circulation_duration),0) AS circulation_duration",
                "IFNULL(sum(actual_working_hours),0) AS actual_working_hours");
        WorkOrderEntity workOrderEntity = this.getOne(queryWrapper);
        WorkOrderPageDTO build = WorkOrderPageDTO.builder().build();
        if (workOrderEntity == null) {
            return build;
        }
        //计划数量
        build.setPlanQuantity(workOrderEntity.getPlanQuantity());
        //完成数量
        build.setFinishCount(workOrderEntity.getFinishCount());
        //流转数量
        build.setInStockCount(workOrderEntity.getInStockCount());
        //流转时长
        build.setCirculationDuration(workOrderEntity.getCirculationDuration());
        //生产时长
        build.setActualWorkingHours(workOrderEntity.getActualWorkingHours());
        return build;
    }

    /**
     * 完成率过滤
     */
    private void completionRateFilter(String completionRate, LambdaQueryWrapper<WorkOrderEntity> wrapper) {
        if (StringUtils.isNotEmpty(completionRate) && Objects.nonNull(CompletionRateEnum.getByType(completionRate))) {
            switch (CompletionRateEnum.getByType(completionRate)) {
                // (0%,50%]
                case BETWEEN_ZERO_AND_FIFTY_PERCENT:
                    wrapper.gt(WorkOrderEntity::getProgress, 0)
                            .le(WorkOrderEntity::getProgress, 0.5);
                    break;
                // (50%,100)
                case BETWEEN_FIFTY_AND_ONE_HUNDRED_PERCENT:
                    wrapper.gt(WorkOrderEntity::getProgress, 0.5)
                            .lt(WorkOrderEntity::getProgress, 1);
                    break;
                // >=100%
                case GREATER_THAN_OR_EQUAL_TO_ONE_HUNDRED_PERCENT:
                    wrapper.ge(WorkOrderEntity::getProgress, 1);
                    break;
                // <=0%或者完成率为空的
                default:
                    wrapper.and(o -> o.isNull(WorkOrderEntity::getProgress)
                            .or().le(WorkOrderEntity::getProgress, 0));
            }
        }
    }

    @Override
    public WorkOrderEntity getWorkOrderByNumber(String workOrderNumber) {
        if (StringUtils.isNotEmpty(workOrderNumber)) {
            WorkOrderEntity workOrderEntity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).one();
            if (workOrderEntity == null) {
                return null;
            }
            WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderNumber);
            workOrderEntity = this.getWorkOrderById(detailDTO);
            return workOrderEntity;
        }
        return null;
    }

    /**
     * 获取工单简单记录
     *
     * @param workOrderNumber
     * @return
     */
    @Override
    public WorkOrderEntity getSimpleWorkOrderByNumber(String workOrderNumber) {
        WorkOrderEntity workOrderEntity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).one();
        return workOrderEntity;
    }

    @Override
    public void showStateAndName(List<WorkOrderEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Set<String> usernameSet = records.stream().map(WorkOrderEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toSet());
        usernameSet.addAll(records.stream().map(WorkOrderEntity::getUpdateBy).filter(Objects::nonNull).collect(Collectors.toSet()));
        usernameSet.addAll(records.stream().map(WorkOrderEntity::getApprover).filter(Objects::nonNull).collect(Collectors.toSet()));
        usernameSet.addAll(records.stream().map(WorkOrderEntity::getActualApprover).filter(Objects::nonNull).collect(Collectors.toSet()));
        usernameSet.addAll(records.stream().map(WorkOrderEntity::getMagName).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String, String> userNameNickMap = userService.getUserNameNickMap(new ArrayList<>(usernameSet));
        Map<String, String> signatureUrlMap = userService.getSignatureUrlMap(new ArrayList<>(usernameSet));

        // 获取关联的工序名称
        Map<String, String> procedureNameMap = workOrderProcedureRelationService.getProcedureNameMap(records);
        // 查询单据类型和业务类型
        List<BusinessTypeListVO> vos = orderTypeConfigService.getOrderTypeListByOrderCategory(OrderTypeSelectDTO.builder().categoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode()).build());
        Map<String, String> businessTypeMap = vos.stream().collect(Collectors.toMap(BusinessTypeListVO::getBusinessTypeCode, BusinessTypeListVO::getBusinessTypeName));
        Map<String, String> ordertTypeMap = vos.stream().flatMap(v -> v.getOrderTypeListVOList().stream()).collect(Collectors.toMap(BusinessTypeListVO.OrderTypeListVO::getOrderType, BusinessTypeListVO.OrderTypeListVO::getOrderTypeName));

        for (WorkOrderEntity entity : records) {
            // 展示状态名称
            entity.setStateName(WorkOrderStateEnum.getNameByCode(entity.getState()));
            // 展示派工状态名称
            entity.setAssignmentStateName(AssignmentStateEnum.getNameByType(entity.getAssignmentState()));
            // 展示单据类型名称
            entity.setBusinessTypeName(businessTypeMap.get(entity.getBusinessType()));
            entity.setOrderTypeName(ordertTypeMap.get(entity.getOrderType()));
            // 展示人名
            entity.setCreateName(userNameNickMap.getOrDefault(entity.getCreateBy(), entity.getCreateBy()));
            entity.setUpdateByName(userNameNickMap.getOrDefault(entity.getUpdateBy(), entity.getUpdateBy()));
            entity.setApproverName(userNameNickMap.getOrDefault(entity.getApprover(), entity.getApprover()));
            entity.setActualApproverName(userNameNickMap.getOrDefault(entity.getActualApprover(), entity.getActualApprover()));
            entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
            entity.setMagNickname(userNameNickMap.getOrDefault(entity.getMagName(), entity.getMagName()));
            // 展示审核状态名称
            entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
            //展示工单执行状态
            entity.setExecutionStatusName(WorkOrderExecutionStateEnum.getNameByCode(entity.getExecutionStatus()));
            //展示流转状态和排产状态
            entity.setPlanStateName(WorkOrderPlanStateEnum.getNameByCode(entity.getPlanState()));
            entity.setCirculationStateName(WorkOrderCirculationStateEnum.getNameByCode(entity.getCirculationState()));
            List<WorkOrderTeamEntity> workOrderTeams = workOrderTeamService.getWorkOrderTeams(entity.getWorkOrderNumber());
            entity.setWorkOrderTeamEntities(workOrderTeams);
            WorkCenterEntity workCenterEntity = workCenterService.getById(entity.getWorkCenterId());
            entity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
            entity.setWorkCenterRelevanceType(workCenterEntity == null ? null : workCenterEntity.getRelevanceType());
            // 展示投产检查结果
            entity.setInvestCheckResultName(InvestCheckResultEnum.getNameByCode(entity.getInvestCheckResult()));
            entity.setProcedureName(procedureNameMap.get(entity.getWorkOrderNumber()));
            // 设置生产基本单元
            setProductionBasicUnit(entity);
            entity.setMaterialCheckTypeName(MaterialMatchingEnum.getNameByType(entity.getMaterialCheckType()));
            // 完成数如果后台根据计算为负数时，需要对外展示为0
            entity.setFinishCount(entity.getFinishCount() < 0 ? 0 : entity.getFinishCount());
            // 排程状态名称
            entity.setSchedulingStateName(WorkOrderScheduleStateEnum.getNameByCode(entity.getSchedulingState()));
        }
    }

    /**
     * 设置工单的MaterialFields
     */
    @Override
    public void setMaterialFieldsForWorkOrderEntity(WorkOrderEntity entity) {
        // 展示物料相关字段
        MaterialEntity materialEntity = materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId());
        entity.setMaterialFields(materialEntity);
    }

    private void setMaterialFieldsForWorkOrderEntity(List<WorkOrderEntity> workOrders) {
        // 批量物料字段
        List<MaterialEntity> materialFields = materialService.listSimpleMaterialByCodesAndSkuIds(workOrders.stream().map(e ->
                MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(e.getMaterialCode())
                        .skuId(e.getSkuId())
                        .build()
        ).collect(Collectors.toList()));
        Map<String, MaterialEntity> materialSkuMap = materialFields.stream()
                .collect(Collectors.toMap(e -> ColumnUtil.getMaterialSku(e.getCode(), e.getSkuEntity()), Function.identity(), (v1, v2) -> v2));
        workOrders.forEach(entity -> entity.setMaterialFields(materialSkuMap.get(ColumnUtil.getMaterialSku(entity.getMaterialCode(), entity.getSkuId()))));
    }

    @Override
    public PrintDTO print(WorkOrderSelectDTO selectDTO) {
        List<PrintDTO.ParamDto> printDTOS = new ArrayList<>();
        // 必填项如果未填，则直接返回，防止前端误传导致CPU飙高、以及未打印的数据显示已打印
        boolean pageParams = Objects.isNull(selectDTO.getCurrent()) || Objects.isNull(selectDTO.getSize());
        if (StringUtils.isBlank(selectDTO.getWorkOrderNumber()) && pageParams) {
            throw new ResponseException(RespCodeEnum.PRINT_IS_REQUIRE);
        }
        List<WorkOrderEntity> workOrderEntities = getWorkOrderEntities(selectDTO);

        // 新版本通过前端选择的标签规则进行打印
        LabelEntity labelEntity = labelService.lambdaQuery().eq(LabelEntity::getRuleId, selectDTO.getRuleId()).one();
        if (labelEntity == null) {
            throw new ResponseException(RespCodeEnum.NOT_FIND_BAR_CODE_RULE);
        }
        LabelTypeConfigEntity labelTypeConfigEntity = labelTypeConfigService.detail(labelEntity.getCodeType());
        BarCodeAnalysisService barCodeAnalysisService = SpringUtil.getBean(BarCodeAnalysisService.class);
        List<String> relateNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
        // 查询标签单据打印信息
        Map<String, PrintInfoDTO> printInfoMap = barCodeAnalysisService.getPrintInfoMap(labelTypeConfigEntity.getRelateType(), relateNumbers);
        // 每次打印，序号初始化为1
        int i = 1;
        // 获取打印模板, 替换打印模板里${ }的数据
        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            PrintDTO printDTO = JSON.parseObject(labelEntity.getContent(), PrintDTO.class);
            List<PrintDTO.ParamDto.Content> printElements = printDTO.getPanels().get(0).getPrintElements();
            for (PrintDTO.ParamDto.Content content : printElements) {
                String data = content.getOptions().getTitle();
                if (StringUtils.isBlank(data)) {
                    continue;
                }
                // 替换占位符, 替换值为查询对象的某个属性值
                Matcher matcher = PATTERN.matcher(data);
                while (matcher.find()) {
                    String placeholder = matcher.group();
                    // 序号加1
                    if (placeholder.equals(LabelInfoEnum.SERIAL_NO.getPlaceholder())) {
                        data = String.valueOf(i);
                        i++;
                        continue;
                    }
                    CodeInfoSelectDTO build = CodeInfoSelectDTO.builder()
                            .ruleType(labelEntity.getCodeType())
                            .relateType(labelTypeConfigEntity.getRelateType())
                            .materialCode(workOrderEntity.getMaterialCode())
                            .relateNumber(workOrderEntity.getWorkOrderNumber())
                            .placeholder(placeholder)
                            .sliceDigits(content.getOptions().getSliceDigits())
                            .printInfoDto(printInfoMap.getOrDefault(workOrderEntity.getWorkOrderNumber(), new PrintInfoDTO()))
                            .build();
                    // 判断该标签是否存在公式，如果存在运算公式，将对数据进行四则运算
                    // 如果公式为空，则直接进行真实值替换占位符的逻辑
                    String placeholderValue = barCodeAnalysisService.replacePlaceholder(content, build);
                    data = data.replace(placeholder, placeholderValue);
                }
                content.getOptions().setTitle(data);
                // 字体大小如果为空,则设置默认值
                if (content.getOptions().getFontSize() == null) {
                    content.getOptions().setFontSize(9.0);
                }
            }
            printDTOS.addAll(printDTO.getPanels());
        }
        PrintDTO printDTO = PrintDTO.builder().ruleType(labelEntity.getCodeType()).ruleCode(labelEntity.getRuleCode()).panels(printDTOS).build();
        PrintDTO dto = labelService.getPrintDtoByOpenApi(printDTO);
        if (Objects.nonNull(selectDTO.getIsPreview()) && selectDTO.getIsPreview()) {
            return dto;
        }
        // 将查询到的数据标记为已打印
        List<String> printWorkOrderNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        this.lambdaUpdate().in(WorkOrderEntity::getWorkOrderNumber, printWorkOrderNumbers).set(WorkOrderEntity::getIsPrint, true).update();
        // 记录打印的编号
        dto.setPrintCodes(StringUtils.join(printWorkOrderNumbers, Constant.SEP));
        return dto;
    }


    /**
     * 设置物料当前库存数量 和 BOM清单中物料的领料申请数量、领料数量、退料数量、当前领用数量、当前库存数量。
     * <p>
     * 领料申请数量：关联的生产领料单，对应数量求和，除创建和取消状态的单据。
     * 领料数量：关联的出库单(类型为工单领取物料)，对应的实际数量求和，除创建和取消状态的单据，单据通过生产工单来关联。
     * 退料数量：关联的入库单(类型为工单退物料)，对应的实际数量求和，除创建和取消状态的单据，单据通过生产工单来关联。
     * 当前领用数量：对应的 领料数量 – 退料数量
     * 当前库存数量：对应物料的当前即时库存。(基本信息和物料信息都要)
     */
    private void setMaterialInventoryQuantity(WorkOrderEntity workOrder) {
        if (Objects.isNull(workOrder)) {
            return;
        }
        String workOrderNumber = workOrder.getWorkOrderNumber();
        List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
        MaterialEntity materialFields = workOrder.getMaterialFields();
        if (materialFields == null) {
            return;
        }
        materialDetails.add(StockMaterialDetailEntity.builder().productCode(workOrder.getMaterialCode()).skuId(workOrder.getSkuId()).businessUnitCode(workOrder.getBusinessUnitCode()).build());
        List<BomEntity> bomEntities = materialFields.getBomEntities();

        // 查询物料当前库存数量
        Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(inventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new com.alibaba.fastjson.TypeReference<Map<String, BigDecimal>>() {
        }, null);
        materialCodeStockMap = materialCodeStockMap == null ? new HashMap<>() : materialCodeStockMap;
        BigDecimal stockQuantity = materialCodeStockMap.get(ColumnUtil.getMaterialSku(workOrder.getMaterialCode(), workOrder.getSkuId()));
        workOrder.setStockQuantity(Objects.isNull(stockQuantity) ? 0 : stockQuantity.doubleValue());

        if (CollectionUtils.isEmpty(bomEntities)) {
            return;
        }
        for (BomEntity bom : bomEntities) {
            List<BomRawMaterialsQuantityDTO> req = bom.getBomRawMaterialEntities().stream().map(res ->
                            BomRawMaterialsQuantityDTO.builder()
                                    .bomRawMaterialId(res.getId())
                                    .bomRawMaterialCode(res.getCode())
                                    .skuId(res.getSkuId())
                                    .build())
                    .collect(Collectors.toList());
            // 获取BOM清单中物料的领料申请数量、领料数量、退料数量、当前领用数量。
            List<BomRawMaterialsQuantityDTO> results = bomRawMaterialService.getBomRawMaterialsQuantity(workOrderNumber, req);
            Map<Integer, BomRawMaterialsQuantityDTO> idQuantityMap = results.stream().collect(Collectors.toMap(BomRawMaterialsQuantityDTO::getBomRawMaterialId, v -> v));
            for (BomRawMaterialEntity bomRawMaterial : bom.getBomRawMaterialEntities()) {
                materialDetails.add(StockMaterialDetailEntity.builder().productCode(bomRawMaterial.getCode()).skuId(bomRawMaterial.getSkuId()).build());
                BomRawMaterialsQuantityDTO quantityDTO = idQuantityMap.get(bomRawMaterial.getId());
                // 领料申请数量
                bomRawMaterial.setApplyQuantity(Objects.isNull(quantityDTO) ? 0 : quantityDTO.getApplyQuantity());
                // 领料数量
                bomRawMaterial.setTakeOutQuantity(Objects.isNull(quantityDTO) ? 0 : quantityDTO.getTakeOutQuantity());
                // 退料数量
                bomRawMaterial.setReturnQuantity(Objects.isNull(quantityDTO) ? 0 : quantityDTO.getReturnQuantity());
                // 当前领用数量
                bomRawMaterial.setCurUseQuantity(Objects.isNull(quantityDTO) ? 0 : quantityDTO.getCurUseQuantity());
                // 设置当前库存数量
                stockQuantity = materialCodeStockMap.get(ColumnUtil.getMaterialSku(bomRawMaterial.getCode(), bomRawMaterial.getSkuId()));
                bomRawMaterial.setStockQuantity(Objects.isNull(stockQuantity) ? 0 : stockQuantity.doubleValue());
            }
        }

    }

    @Override
    public List<CommonState> getWorkOrderState() {
        WorkOrderStateEnum[] values = WorkOrderStateEnum.values();
        List<CommonState> states = new ArrayList<>();
        for (WorkOrderStateEnum stateEnum : values) {
            states.add(CommonState.builder().code(stateEnum.getCode()).name(stateEnum.getName()).build());
        }
        return states;
    }

    @Override
    public List<WorkOrderEntity> getGrantList() {
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(WorkOrderEntity::getState,
                WorkOrderStateEnum.RELEASED.getCode(),
                WorkOrderStateEnum.HANG_UP.getCode(),
                WorkOrderStateEnum.INVESTMENT.getCode(),
                WorkOrderStateEnum.FINISHED.getCode());
        return this.list(wrapper);

    }

    @Override
    public List<WorkOrderEntity> getAppointList() {
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(WorkOrderEntity::getState,
                WorkOrderStateEnum.RELEASED.getCode(),
                WorkOrderStateEnum.INVESTMENT.getCode(),
                WorkOrderStateEnum.HANG_UP.getCode());
        return this.list(wrapper);
    }

    @Override
    public List<WorkOrderEntity> getProgressWorkOrder() {
        //获取状态为投入、挂起的工单
        QueryWrapper<WorkOrderEntity> qw = new QueryWrapper<>();
        qw.lambda().in(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
        return list(qw);
    }


    @Override
    public WorkOrderEntity getWorkOrderById(WorkOrderDetailDTO dto) {
        WorkOrderEntity workOrderEntity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, dto.getWorkOrderNumber()).one();
        if (workOrderEntity == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_SELECT_FAIL.fmtDes(dto.getWorkOrderNumber()));
        }
        // 先查询必要的信息，防止下面信息获取字段值时为空
        showStateAndName(Stream.of(workOrderEntity).collect(Collectors.toList()));
        String workOrderNumber = workOrderEntity.getWorkOrderNumber();
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Objects.nonNull(dto.getIsShowSimpleInfo()) && dto.getIsShowSimpleInfo()) {
            return workOrderEntity;
        }
        // 查询物料信息
        if (Objects.nonNull(dto.getIsShowMaterial()) && dto.getIsShowMaterial()) {
            // 以下两个方法有写逻辑重复，为了保证代码优化不影响业务逻辑，这里不做修改
            // 生产工单BOM清单和多级BOM功能整合成一起,避免功能重复
//            getMaterialEntityList(workOrderEntity);
            setMaterialFieldsForWorkOrderEntity(workOrderEntity);
            workOrderEntity.setMaterialId(CollectionUtils.isEmpty(workOrderEntity.getMaterialEntityList()) ? null : workOrderEntity.getMaterialEntityList().get(0).getId());
        }
        // 查询仓库相关信息
        if (Objects.nonNull(dto.getIsShowWarehouseInfo()) && dto.getIsShowWarehouseInfo()) {
            // 设置物料当前库存数量 和 BOM清单中物料的领料申请数量、领料数量、退料数量、当前领用数量、当前库存数量。
            setMaterialInventoryQuantity(workOrderEntity);
            // 领料出库单列表(允许查询生产工单用料清单的领料出库单)
            // 工单领料出库单
            List<StockInAndOutEntity> takeOutList = JacksonUtil.getResponseArray(inAndOutInterface.getStockByRelateOrder(workOrderNumber, InputOrOutputRelateTypeEnum.WORK_ORDER_TAKE_OUT_WORK_ORDER.getTypeCode(), InputOrOutputRelateTypeEnum.WORK_ORDER_TAKE_OUT_WORK_ORDER.getRelateOrderType()), StockInAndOutEntity.class);
            // 生产工单用料清单的领料出库单
            List<WorkOrderMaterialListEntity> workOrderMaterialListEntities = workOrderMaterialListService.listMaterialListsByOrderNumber(workOrderNumber);
            for (WorkOrderMaterialListEntity workOrderMaterialListEntity : workOrderMaterialListEntities) {
                takeOutList.addAll(JacksonUtil.getResponseArray(inAndOutInterface.getStockByRelateOrder(workOrderMaterialListEntity.getMaterialListCode(), InputOrOutputRelateTypeEnum.WORK_ORDER_TAKEOUT_WORK_MATERIAL.getTypeCode(), InputOrOutputRelateTypeEnum.WORK_ORDER_TAKEOUT_WORK_MATERIAL.getRelateOrderType()), StockInAndOutEntity.class));
            }
            //入库单列表
            List<StockInAndOutEntity> inputList = JacksonUtil.getResponseArray(inAndOutInterface.getStockByRelateOrder(workOrderNumber, InputOrOutputRelateTypeEnum.IN_WORK_ORDER.getTypeCode(), InputOrOutputRelateTypeEnum.IN_WORK_ORDER.getRelateOrderType()), StockInAndOutEntity.class);
            //拿到工单交付入库数量
//        workOrderEntity.setInputCount(getInventoryQuantity(workOrderNumber, workOrderEntity.getMaterialCode()));
            workOrderEntity.setInputCount(workOrderEntity.getInventoryQuantity());
            //退料单列表
            List<StockInAndOutEntity> returnList = JacksonUtil.getResponseArray(inAndOutInterface.getStockByRelateOrder(workOrderNumber, InputOrOutputRelateTypeEnum.APPLICATION_RETURN_WORK_ORDER.getTypeCode(), InputOrOutputRelateTypeEnum.APPLICATION_RETURN_WORK_ORDER.getRelateOrderType()), StockInAndOutEntity.class);
            workOrderEntity.setInputList(inputList);
            workOrderEntity.setReturnList(returnList);
            workOrderEntity.setTakeOutList(takeOutList);
        }
        // 查询关联订单(销售订单和生产订单)
        if (Objects.nonNull(dto.getIsShowRelateOrder()) && dto.getIsShowRelateOrder()) {
            getOrderDetailList(workOrderEntity);
        }
        // 查询附件信息
        if (Objects.nonNull(dto.getIsShowFileInfo()) && dto.getIsShowFileInfo()) {
            List<WorkOrderFileEntity> workOrderFileEntities = workOrderFileService.getEntityByWorkOrderId(workOrderEntity.getWorkOrderId());
            workOrderEntity.setFile(workOrderFileEntities);
            //查询工单关联的附件表数据
            List<AppendixEntity> listAppendix = appendixService.listByRelateId(String.valueOf(workOrderEntity.getWorkOrderId()), AppendixTypeEnum.WORKORDER_APPENDIX.getCode());
            workOrderEntity.setAppendixEntities(listAppendix);
        }
        // 查询生产基本单元投产记录列表
        if (Objects.nonNull(dto.getIsShowProductBasicUnitInvestRecordInfo()) && dto.getIsShowProductBasicUnitInvestRecordInfo()) {
            setProductBasicUnitInvestRecord(workOrderEntity);
        }
        // 查询工艺工序
        if (Objects.nonNull(dto.getIsShowCraftProcedureInfo()) && dto.getIsShowCraftProcedureInfo()) {
            setCraftProcedure(workOrderEntity);
        }
        // 查询关联的工作中心及生产基本单元信息
        if (Objects.nonNull(dto.getIsShowWorkCenterAndProductBasicUnitInfo()) && dto.getIsShowWorkCenterAndProductBasicUnitInfo()) {
            WorkCenterEntity workCenterEntity = workCenterMapper.selectById(workOrderEntity.getWorkCenterId());
            if (Objects.nonNull(workCenterEntity)) {
                workOrderEntity.setWorkCenterType(workCenterEntity.getType());
                workOrderEntity.setWorkCenterRelevanceType(workCenterEntity.getRelevanceType());
                workOrderEntity.setWorkCenterCode(workCenterEntity.getCode());
            }
        }
        // 查询关联资源
        if (Objects.nonNull(dto.getIsShowAssociatedResourcesInfo()) && dto.getIsShowAssociatedResourcesInfo()) {
            //获取工单关联设备资源
            getRelevanceDeviceById(workOrderEntity);
            //获取工单关联班组资源
            getRelevanceTeamById(workOrderEntity);
            //获取工单关联制造单元资源
            getRelevanceLineById(workOrderEntity);
        }
        // 获取包装方案
        if (Objects.nonNull(dto.getIsShowPackageSchemeInfo()) && dto.getIsShowPackageSchemeInfo()) {
            PackageSchemeEntity packageSchemeEntity = packageSchemeService.getDetailByCode(workOrderEntity.getPackageSchemeCode());
            workOrderEntity.setPackageSchemeEntities(packageSchemeEntity == null ? new ArrayList<>() : Stream.of(packageSchemeEntity).collect(Collectors.toList()));
        }
        // 获取工单计划
        if (Objects.nonNull(dto.getIsShowWorkOrderPlanInfo()) && dto.getIsShowWorkOrderPlanInfo()) {
            workOrderEntity.setWorkOrderPlanList(workOrderPlanService.getByWorkOrderNumber(workOrderNumber, true));
        }
        // 设置批次计划总数
        if (Objects.nonNull(dto.getIsShowBatchInfo()) && dto.getIsShowBatchInfo()) {
            setBarCodeWithWorkOrderNumber(workOrderEntity);
        }
        // 展示投产结果明细
        if (Objects.nonNull(dto.getIsShowWorkOrderInvestCheckResultInfo()) && dto.getIsShowWorkOrderInvestCheckResultInfo()) {
            WorkOrderInvestCheckResultEntity investCheckResultEntity = investCheckResultService.lambdaQuery().eq(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, workOrderNumber).one();
            workOrderEntity.setInvestCheckResultDetail(Objects.isNull(investCheckResultEntity) ? null : investCheckResultEntity.getInvestCheckResultDetail());
        }
        // 查询项目合同
        if (Objects.nonNull(dto.getIsShowProjectContractInfo()) && dto.getIsShowProjectContractInfo()) {
            setProjectContract(Stream.of(workOrderEntity).collect(Collectors.toList()));
        }
        // 获取维修数量
        if (Objects.nonNull(dto.getIsShowMaintainInfo()) && dto.getIsShowMaintainInfo()) {
            LambdaQueryWrapper<MaintainRecordEntity> maintainRecordQueryWrapper = new LambdaQueryWrapper<>();
            maintainRecordQueryWrapper.eq(MaintainRecordEntity::getWorkOrder, workOrderNumber)
                    .groupBy(MaintainRecordEntity::getBarCode);
            List<MaintainRecordEntity> recordList = maintainRecordMapper.selectList(maintainRecordQueryWrapper);
            workOrderEntity.setMaintainCount(recordList.size());
        }
        // 查询理论工时
        if (Objects.nonNull(dto.getIsShowPlanTheoryHourInfo()) && dto.getIsShowPlanTheoryHourInfo()) {
            Double theoryHour = workOrderTheoryHour(workOrderEntity);
            workOrderEntity.setTheoryHour(theoryHour);
            workOrderEntity.setProduceTheoryHour(NullableDouble.of(theoryHour).mul(workOrderEntity.getFinishCount()).scale(2).cal());
            workOrderEntity.setPlanTheoryHour(NullableDouble.of(theoryHour).mul(workOrderEntity.getPlanQuantity()).scale(2).cal());
            workOrderEntity.setCapacityUnitName((String) redisTemplate.opsForValue().get(RedisKeyPrefix.WORK_ORDER_CAPACITY_UNIT_NAME_ + workOrderNumber));
        }
        // 查询直通信息
        if (Objects.nonNull(dto.getIsShowDirectAccessInfo()) && dto.getIsShowDirectAccessInfo()) {
            showDirectAccessInfo(Collections.singletonList(workOrderEntity));
        }
        return workOrderEntity;
    }

    private void setProductBasicUnitInvestRecord(WorkOrderEntity workOrderEntity) {
        List<WorkOrderBasicUnitInputRecordEntity> productBasicUnitInvestRecordEntities = workOrderBasicUnitInputRecordService.lambdaQuery()
                .eq(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber()).list();
        workOrderEntity.setProductBasicUnitInputRecords(productBasicUnitInvestRecordEntities);
    }

    private void showDirectAccessInfo(List<WorkOrderEntity> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return;
        }
        List<String> workOrderNumbers = workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
        List<MaintainRecordEntity> maintainRecords = maintainRecordService.lambdaQuery().in(MaintainRecordEntity::getWorkOrder, workOrderNumbers).list();
        Map<String, List<MaintainRecordEntity>> workOrderMaintainGroup = maintainRecords.stream().collect(Collectors.groupingBy(MaintainRecordEntity::getWorkOrder));
        for (WorkOrderEntity workOrder : workOrders) {
            long maintainCount = workOrderMaintainGroup.getOrDefault(workOrder.getWorkOrderNumber(), Collections.emptyList()).stream().map(MaintainRecordEntity::getBarCode).distinct().count();
            Double finishCount = workOrder.getFinishCount();
            //直通数 =  完成数 - 有维修过的（流水码去重）
            Double directAccessQuantity = NullableDouble.of(finishCount).sub(maintainCount).scale(2).cal();
            workOrder.setDirectAccessQuantity(directAccessQuantity);
            workOrder.setDirectAccessRate(NullableDouble.of(directAccessQuantity).div(finishCount).scale(2).cal());
        }
    }

    /**
     * 设置批次计划总数
     */
    private void setBarCodeWithWorkOrderNumber(WorkOrderEntity workOrderEntity) {
        LambdaQueryWrapper<BarCodeEntity> barCodeWrapper = new LambdaQueryWrapper<>();
        barCodeWrapper.eq(BarCodeEntity::getRelateNumber, workOrderEntity.getWorkOrderNumber())
                .eq(BarCodeEntity::getRuleType, BarCodeTypeEnum.FINISHED.getCode());
        List<BarCodeEntity> barCodeEntities = barCodeMapper.selectList(barCodeWrapper);
        BigDecimal sum = barCodeEntities.stream().map(BarCodeEntity::getCount).map(BigDecimal::valueOf).reduce(BigDecimal.ZERO, BigDecimal::add);
        workOrderEntity.setRelatedBarCodePlanSum(sum.doubleValue());
    }

    /**
     * 设置工艺工序
     *
     * @param workOrderEntity
     */
    @Override
    public void setCraftProcedure(WorkOrderEntity workOrderEntity) {
        List<CraftProcedureEntity> craftProcedureEntities = workOrderProcedureRelationService.getCraftProcedureListByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
        if (CollectionUtils.isNotEmpty(craftProcedureEntities)) {
            String procedureIds = craftProcedureEntities.stream().map(CraftProcedureEntity::getProcedureId).map(String::valueOf).collect(Collectors.joining(Constant.SEP));
            String craftProcedureIds = craftProcedureEntities.stream().map(CraftProcedureEntity::getId).map(String::valueOf).collect(Collectors.joining(Constant.SEP));
//            String procedures = craftProcedureEntities.stream().map(CraftProcedureEntity::getProcedureName).collect(Collectors.joining(Constant.SEP));
            // 优先使用工序别名，如果别名为空则使用工序名称
            String procedures = craftProcedureEntities.stream().map(CraftProcedureEntity::getProcedureName).collect(Collectors.joining(Constant.SEP));
            String alias = craftProcedureEntities.stream().map(CraftProcedureEntity::getAlias).filter(Objects::nonNull).collect(Collectors.joining(Constant.SEP));
            workOrderEntity.setProcedureIds(procedureIds);
            workOrderEntity.setCraftProcedureId(craftProcedureEntities.size() > 1 ? null : Integer.valueOf(craftProcedureIds));
            workOrderEntity.setProcedureName(procedures);
            workOrderEntity.setProcedureAlias(alias);
            workOrderEntity.setCraftProcedureEntities(craftProcedureEntities);
        }
    }

    /**
     * 获取工单关联设备资源
     *
     * @param workOrderEntity
     * @return
     */
    @Override
    public void getRelevanceDeviceById(WorkOrderEntity workOrderEntity) {
        //查询关联设备资源
        LambdaQueryWrapper<WorkOrderDeviceRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkOrderDeviceRelevanceEntity::getWorkOrderId, workOrderEntity.getWorkOrderId());
        List<WorkOrderDeviceRelevanceEntity> deviceRelevanceList = workOrderDeviceRelevanceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(deviceRelevanceList)) {
            List<Integer> deviceIds = deviceRelevanceList.stream().map(WorkOrderDeviceRelevanceEntity::getDeviceId).collect(Collectors.toList());
            workOrderEntity.setRelevanceDeviceIds(deviceIds);
            List<DeviceEntity> deviceEntityList = deviceService.listByIds(deviceIds);
            workOrderEntity.setRelevanceDeviceNames(deviceEntityList.stream().map(DeviceEntity::getDeviceName).collect(Collectors.toList()));
            // 设置通用对象
            Map<Integer, String> map = deviceEntityList.stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
            workOrderEntity.setWorkOrderRelevanceVOS(deviceRelevanceList.stream().map(o -> WorkOrderRelevanceVO.builder()
                    .relevanceType(workOrderEntity.getWorkCenterRelevanceType())
                    .relevanceId(o.getDeviceId())
                    .relevanceName(map.get(o.getDeviceId()))
                    .build()).collect(Collectors.toList()));
        }
    }

    /**
     * 获取工单关联班组资源
     *
     * @param workOrderEntity
     * @return
     */
    @Override
    public void getRelevanceTeamById(WorkOrderEntity workOrderEntity) {
        //查询关联班组资源
        LambdaQueryWrapper<WorkOrderTeamRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkOrderTeamRelevanceEntity::getWorkOrderId, workOrderEntity.getWorkOrderId());
        List<WorkOrderTeamRelevanceEntity> teamRelevanceList = workOrderTeamRelevanceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(teamRelevanceList)) {
            List<Integer> teamIds = teamRelevanceList.stream().map(WorkOrderTeamRelevanceEntity::getTeamId).collect(Collectors.toList());
            workOrderEntity.setRelevanceTeamIds(teamIds);
            List<SysTeamEntity> sysTeamEntityList = sysTeamService.listByIds(teamIds);
            workOrderEntity.setRelevanceTeamNames(sysTeamEntityList.stream().map(SysTeamEntity::getTeamName).collect(Collectors.toList()));

            // 设置通用对象
            Map<Integer, String> map = sysTeamEntityList.stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamName));
            workOrderEntity.setWorkOrderRelevanceVOS(teamRelevanceList.stream().map(o -> WorkOrderRelevanceVO.builder()
                    .relevanceType(workOrderEntity.getWorkCenterRelevanceType())
                    .relevanceId(o.getTeamId())
                    .relevanceName(map.get(o.getTeamId()))
                    .build()).collect(Collectors.toList()));
        }
    }

    /**
     * 获取工单关联班组资源
     *
     * @param workOrderEntity
     * @return
     */
    @Override
    public void getRelevanceLineById(WorkOrderEntity workOrderEntity) {
        //查询关联制造单元资源
        LambdaQueryWrapper<WorkOrderLineRelevanceEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(WorkOrderLineRelevanceEntity::getWorkOrderId, workOrderEntity.getWorkOrderId());
        List<WorkOrderLineRelevanceEntity> lineRelevanceList = workOrderLineRelevanceService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(lineRelevanceList)) {
            List<Integer> lineIds = lineRelevanceList.stream().map(WorkOrderLineRelevanceEntity::getLineId).collect(Collectors.toList());
            workOrderEntity.setRelevanceLineIds(lineIds);
            List<ProductionLineEntity> lineEntityList = productionLineMapper.selectBatchIds(lineIds);
            workOrderEntity.setRelevanceLineNames(lineEntityList.stream().map(ProductionLineEntity::getName).collect(Collectors.toList()));

            // 设置通用对象
            Map<Integer, String> map = lineEntityList.stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName));
            workOrderEntity.setWorkOrderRelevanceVOS(lineRelevanceList.stream().map(o -> WorkOrderRelevanceVO.builder()
                    .relevanceType(workOrderEntity.getWorkCenterRelevanceType())
                    .relevanceId(o.getLineId())
                    .relevanceName(map.get(o.getLineId()))
                    .build()).collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderEntity add(WorkOrderEntity workOrderEntity) {
        // 防止并发操作，导致生成重复的工单号
        String key = NumberRedisKeyPrefix.WORK_ORDER_NUMBER + workOrderEntity.getWorkOrderNumber();
        Boolean lockStatus = redisTemplate.opsForValue().setIfAbsent(key, new Date(), 1, TimeUnit.MINUTES);
        if (lockStatus == null || !lockStatus) {
            throw new ResponseException("该单据号并发占用，稍后再试");
        }
        try {
            // 如果传入的工单号为空，则工单号由编码规则生成
            if (StringUtils.isBlank(workOrderEntity.getWorkOrderNumber())) {
                if (Objects.isNull(workOrderEntity.getNumberRuleId())) {
                    throw new ResponseException(RespCodeEnum.NEED_NUMBER_RULE);
                }
                NumberCodeDTO seqById = numberRuleService.getSeqById(
                        RuleSeqDTO.builder()
                                .id(workOrderEntity.getNumberRuleId())
                                .build()
                );
                workOrderEntity.setWorkOrderNumber(seqById.getCode());
            }
            if (StringUtils.isBlank(workOrderEntity.getWorkOrderName())) {
                workOrderEntity.setWorkOrderName(workOrderEntity.getWorkOrderNumber());
            }
            //获取物料当前库存
            Double currentInventory = delMaterialCurrentInventory(workOrderEntity.getMaterialCode());
            MaterialEntity one = materialService.lambdaQuery().eq(MaterialEntity::getCode, workOrderEntity.getMaterialCode()).one();
            if (one == null) {
                throw new ResponseException(RespCodeEnum.MATERIAL_INFORMATION_NOT_FOUND);
            }
            //工单编号判断去重
            notRepeat(workOrderEntity.getWorkOrderNumber());
            workOrderEntity.setState(WorkOrderStateEnum.CREATED.getCode());
            workOrderEntity.setAssignmentState(AssignmentStateEnum.TO_BE_ASSIGNED.getType());
            workOrderEntity.setType(ModelEnum.WORK_ORDER.getType());
            workOrderEntity.setFinishCount(0.0);
            workOrderEntity.setProgress(0.0);
            workOrderEntity.setProductCount(0.0);
            //不是批次物料直接赋值为0，
            workOrderEntity.setPlannedBatches(Boolean.FALSE.equals(one.getIsBatchMag()) ? 0 : workOrderEntity.getPlannedBatches() == null ? 1 : workOrderEntity.getPlannedBatches());
            workOrderEntity.setPlansPerBatch(Boolean.FALSE.equals(one.getIsBatchMag()) ? 0 : workOrderEntity.getPlansPerBatch() == null ? workOrderEntity.getPlanQuantity() : workOrderEntity.getPlansPerBatch());
            workOrderEntity.setPendentQuantity(workOrderEntity.getPlanQuantity());
            workOrderEntity.setCurrentInventory(currentInventory);
            workOrderEntity.setWorkOrderId(null);
            Date createTime = Optional.ofNullable(workOrderEntity.getCreateDate()).orElse(Optional.ofNullable(workOrderEntity.getFakerTime()).orElse(new Date()));
            workOrderEntity.setCreateDate(createTime);
            workOrderEntity.setUpdateDate(createTime);
            // 防止前端漏传参数
            if (Objects.nonNull(workOrderEntity.getCraftId()) && StringUtils.isBlank(workOrderEntity.getCraftCode())) {
                CraftEntity craftEntity = craftService.getById(workOrderEntity.getCraftId());
                workOrderEntity.setCraftCode(craftEntity.getCraftCode());
            }
            // 由于工单允许绑定半成品物料，工艺允许绑定成品工艺，所以有可能前端渲染不出来半成品对应的工艺，导致工艺id为空，所以这里针对此现象做特殊处理
            if (StringUtils.isNotBlank(workOrderEntity.getCraftCode()) && Objects.isNull(workOrderEntity.getCraftId())) {
                CraftEntity craftEntity = craftService.lambdaQuery().eq(CraftEntity::getCraftCode, workOrderEntity.getCraftCode()).one();
                workOrderEntity.setCraftId(craftEntity.getCraftId());
            }

            // 是否提交外部系统审批
            boolean submitOtherApprove = false;
            // 如果需要审批，则缺省为待提交状态
            Integer approveConfig = approveNodeConfigService.getApproveConfig(ApproveModuleEnum.WORK_ORDER.getReleasedCode());
            if (IsApproveEnum.isApprove(approveConfig)) {
                // 如果提交审批，则状态为待审批
                if (Boolean.TRUE.equals(workOrderEntity.getIsSubmitApprove())) {
                    workOrderEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    // 外部系统审批
                    if (!IsApproveEnum.SYSTEM.getCode().equals(approveConfig)) {
                        submitOtherApprove = true;
                    }
                } else {
                    workOrderEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_SUBMIT.getCode());
                }
            }
            //计算计划工时
            workOrderEntity.setPlannedWorkingHours(calculatePlannedWorkHours(workOrderEntity));

            if (workOrderEntity.getLineId() != null) {
                // 获取制造单元名称
                ProductionLineEntity lineEntity = productionLineMapper.selectById(workOrderEntity.getLineId());
                workOrderEntity.setLineCode(lineEntity.getProductionLineCode());
                workOrderEntity.setLineName(lineEntity.getName());
                workOrderEntity.setWorkCenterId(lineEntity.getWorkCenterId() != null ? lineEntity.getWorkCenterId() : workOrderEntity.getWorkCenterId());
                workOrderEntity.setWorkCenterName(StringUtils.isNotBlank(lineEntity.getWorkCenterName()) ? lineEntity.getWorkCenterName() : workOrderEntity.getWorkCenterName());
                workOrderEntity.setMaterialCheckType(lineEntity.getMaterialCheckType());
            }
            if (workOrderEntity.getWorkCenterId() != null) {
                // 获取工作中心名称
                WorkCenterEntity workCenterEntity = workCenterMapper.selectById(workOrderEntity.getWorkCenterId());
                workOrderEntity.setWorkCenterName(workCenterEntity.getName());
            }
            // 设置生产基本单元id和隔离ID
            setProductionBasicUnitIdAndIsolationId(workOrderEntity);
            // 如果单据类型未赋值，则需根据单据类型配置动态获取默认值，并且自动赋值关联的业务类型
            setOrderTypeAndBusinessType(workOrderEntity);

            boolean save = save(workOrderEntity);
            if (!save) {
                throw new ResponseException(RespCodeEnum.OPERATION_FAIL);
            }

            // 项目定义ID和合同ID均不空时，创建关联关系
            if (ObjectUtil.isNotEmpty(workOrderEntity.getProjectDefineId()) || ObjectUtil.isNotEmpty(workOrderEntity.getContractId())) {
                ReceiptProjectContractEntity receiptProjectContract = ReceiptProjectContractEntity.builder()
                        .contractId(ObjectUtil.isNotEmpty(workOrderEntity.getContractId()) ? Integer.valueOf(workOrderEntity.getContractId()) : null)
                        .contractName(workOrderEntity.getContractName())
                        .projectDefineId(ObjectUtil.isNotEmpty(workOrderEntity.getProjectDefineId()) ? Integer.valueOf(workOrderEntity.getProjectDefineId()) : null)
                        .projectDefineName(workOrderEntity.getProjectDefineName())
                        .relationId(workOrderEntity.getWorkOrderId())
                        .type(ReceiptTypePMSEnum.PRUDUCT_WORK_ORDER.getCode())
                        .createBy(workOrderEntity.getCreateBy())
                        .createTime(workOrderEntity.getCreateDate())
                        .projectNodeIds(workOrderEntity.getProjectNodeIds())
                        .relationNumber(workOrderEntity.getWorkOrderNumber())
                        .build();
                receiptProjectContractService.saveRPC(receiptProjectContract);
            }
            // 成功保存单据后，需要将编码规则相对应的所有序列号加1
            if (Objects.nonNull(workOrderEntity.getNumberRuleId())) {
                ruleSeqService.updateSeqEntity(null, workOrderEntity.getNumberRuleId());
            }

            //关联多个订单
            saveOrderRelation(workOrderEntity);
            //保存计划数量对应得重量记录
            recordWorkOrderMaterialPlanService.addMaterialPlan(workOrderEntity);
            //保存工单附件 V1.12.1 一个工单对应多个附件
            saveNewFile(workOrderEntity, true);
            // 插入到工单工序关联表
            workOrderProcedureRelationService.insertWorkOrderProcedureRelation(workOrderEntity);
            //保存工单投产班组成员信息
            workOrderTeamService.add(workOrderEntity);
            //保存工单关联资源
            saveRelevanceResource(workOrderEntity);
            //更新制造单元工单关联关系
            updateProductLineRelation(workOrderEntity);
            // 更新到工单-生产基本单元关联表
            WorkOrderBasicUnitInsertDTO basicUnitInsertDTO = WorkOrderBasicUnitInsertDTO.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                    .productBasicUnits(JacksonUtil.convertArray(workOrderEntity.getProductBasicUnits(), WorkOrderBasicUnitRelationInsertDTO.class))
                    .build();
            basicUnitRelationService.batchSaveWorkOrderBasicUnits(Collections.singletonList(basicUnitInsertDTO));
            //生产工单计划
            workOrderPlanService.saveWorkOrderPlan(workOrderEntity);
            // 外部系统审批
            if (submitOtherApprove) {
                WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderEntity.getWorkOrderNumber());
                WorkOrderEntity orderObject = getWorkOrderById(detailDTO);
                ApproveFullPathCodeDTO approveDto = ApproveFullPathCodeDTO.builder()
                        .fullPathCode(ApproveModuleEnum.WORK_ORDER.getReleasedCode())
                        .orderNumber(orderObject.getWorkOrderNumber())
                        .orderObject(orderObject)
                        .userName(workOrderEntity.getCreateBy())
                        .build();
                // 提交企微审批
                String outerApproveNumber = approveTemplateService.wechatApprove(approveDto);
                this.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                        .set(WorkOrderEntity::getOuterApproveNumber, outerApproveNumber)
                        .update();
            }

            //删除扫码缓存
            redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.SCANNER_WORK_ORDER + workOrderEntity.getProductOrderNumber() + "*"));

            //事务回调方法，事务成功提交之后执行
            if (TransactionSynchronizationManager.isActualTransactionActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        WorkOrderServiceImpl bean = SpringUtil.getBean(WorkOrderServiceImpl.class);
                        bean.dealAfterAdd(workOrderEntity);
                    }
                });
            }

            return WorkOrderEntity.builder().workOrderId(workOrderEntity.getWorkOrderId()).workOrderNumber(workOrderEntity.getWorkOrderNumber()).build();
        } finally {
            redisTemplate.delete(key);
        }
    }

    /**
     * 设置单位类型。如果单据类型未赋值，则需根据单据类型配置动态获取默认值，并且自动赋值关联的业务类型
     */
    @Override
    public void setOrderTypeAndBusinessType(WorkOrderEntity workOrderEntity) {
        if (StringUtils.isBlank(workOrderEntity.getOrderType())) {
            OrderTypeInfoVO defaultOrderTypeVO = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());
            workOrderEntity.setOrderType(defaultOrderTypeVO.getOrderType());
            workOrderEntity.setBusinessType(defaultOrderTypeVO.getBusinessTypeCode());
        } else {
            workOrderEntity.setOrderType(workOrderEntity.getOrderType());
            OrderTypeInfoVO orderTypeInfoVO = orderTypeConfigService.getBusinessTypeByCategoryCodeAndOrderTypeCode(CategoryTypeEnum.WORK_ORDER.getTypeCode(), workOrderEntity.getOrderType());
            workOrderEntity.setBusinessType(orderTypeInfoVO.getBusinessTypeCode());
        }
    }

    @Async
    @Override
    public void dealAfterAdd(WorkOrderEntity workOrderEntity) {
        //根据生产工单查询对应的销售订单
        WorkOrderDetailDTO detailDTO = getConditionByBusinessConfig(ConfigConstant.WORK_ORDER_ADD_MSG_CONTENT, new WorkOrderDetailDTO(workOrderEntity.getWorkOrderNumber()));
        workOrderEntity = this.getWorkOrderById(detailDTO);
        // 推送给ams，计算ams的相关数量
        messagePushToKafkaService.pushNewMessage(workOrderEntity, Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.BACK_FILL_QUANTITY);
        // 调用新增工单的方法时，推送消息
        sendValueChainMsg(workOrderEntity, ConfigConstant.WORK_ORDER_ADD_MSG_CONTENT, KafkaMessageTypeEnum.WORK_ORDER_ADD_MESSAGE);
        // 发送任务
        WorkOrderExtendService workOrderExtendService = SpringUtil.getBean(WorkOrderExtendService.class);
        WorkOrderTaskDTO taskDTO = WorkOrderTaskDTO.builder()
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .build();
        workOrderExtendService.pushToTask(true, taskDTO);
        // 生产订单物料排程状态更改
        if (StringUtils.isNotBlank(workOrderEntity.getProductOrderNumber())) {
            messagePushToKafkaService.pushNewMessage(workOrderEntity.getProductOrderNumber(), Constants.KAFKA_VALUE_CHAIN_TOPIC,
                    AmsEventTypeEnum.PRODUCT_ORDER_MATERIAL_SCHEDULING_STATUS_UPDATE);
        }
        // 刷新生产订单工序进度
        List<ProductOrderEntity> productOrderList = workOrderEntity.getProductOrderList();
        if (CollectionUtil.isEmpty(productOrderList)) {
            productOrderList = orderWorkOrderService.productOrderListByWorkId(workOrderEntity);
        }
        if (CollectionUtils.isNotEmpty(productOrderList)) {
            // 计算生产订单的工序进度
            messagePushToKafkaService.pushNewMessage(productOrderList, Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.PRODUCT_ORDER_PROCEDURE_PROCESS_CAL);
        }
        // 获取物料对象、工序名称，用于组装消息通知对象
        if (CollectionUtils.isEmpty(workOrderEntity.getCraftProcedureEntities())) {
            setCraftProcedure(workOrderEntity);
        }
        List<CraftProcedureEntity> craftProcedureEntities = workOrderEntity.getCraftProcedureEntities();
        String procedureIds = null;
        String procedureNames = null;
        if (CollectionUtils.isNotEmpty(craftProcedureEntities)) {
            procedureIds = craftProcedureEntities.stream().map(o -> String.valueOf(o.getProcedureId())).collect(Collectors.joining(Constant.SEP));
            procedureNames = craftProcedureEntities.stream().map(CraftProcedureEntity::getProcedureName).collect(Collectors.joining(Constant.SEP));
        }

        // 推送精制通知
        NoticeConfigBuilder noticeConfigBuilder = NoticeConfigBuilder.builder()
                .sendUsername(workOrderEntity.getCreateBy())
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .lineId(workOrderEntity.getLineId())
                .lineName(workOrderEntity.getLineName())
                .createTime(DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT))
                .noticeType(NoticeTypeEnum.ADD_WORK_ORDER_NOTICE.getCode())
                .procedureId(procedureIds)
                .procedureName(procedureNames)
                .productOrderNumber(workOrderEntity.getProductOrderNumber())
                .materialCode(workOrderEntity.getMaterialCode())
                .materialName(workOrderEntity.getMaterialFields().getName())
                .workCenterName(workOrderEntity.getWorkCenterName())
                .productionBasicUnitName(workOrderEntity.getProductionBasicUnitName())
                .redisKey(NoticeTypeEnum.ADD_WORK_ORDER_NOTICE.getCode() + workOrderEntity.getWorkOrderNumber())
                .build();
        infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
    }

    /**
     * 设置生产基本单元id和隔离ID和隔离ID，方便后续过滤查询
     *
     * @param workOrderEntity
     */
    @Override
    public void setProductionBasicUnitIdAndIsolationId(WorkOrderEntity workOrderEntity) {
        Integer workCenterId = workOrderEntity.getWorkCenterId();
        if (workCenterId == null) {
            return;
        }
        WorkCenterEntity workCenterEntity = workCenterService.getById(workCenterId);
        if (workCenterEntity == null) {
            return;
        }
        String type = workCenterEntity.getType();
        Integer productionBasicUnitId = null;
        if (WorkCenterTypeEnum.LINE.getCode().equals(type)) {
            productionBasicUnitId = workOrderEntity.getLineId();
        } else if (WorkCenterTypeEnum.TEAM.getCode().equals(type)) {
            productionBasicUnitId = workOrderEntity.getTeamId();
        } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(type)) {
            productionBasicUnitId = workOrderEntity.getDeviceId();
        }
        workOrderEntity.setProductionBasicUnitId(productionBasicUnitId);
        // 工作中心ID+“-”+生产基本单元id
        String isolationId = workCenterId.toString();
        if (productionBasicUnitId != null) {
            isolationId = isolationId + Constants.CROSSBAR + productionBasicUnitId;
        }
        workOrderEntity.setIsolationId(isolationId);
        workOrderEntity.setWorkCenterType(type);
    }

    /**
     * //保存工单关联资源
     *
     * @param workOrderEntity
     */
    @Override
    public void saveRelevanceResource(WorkOrderEntity workOrderEntity) {
        //关联设备
        LambdaUpdateWrapper<WorkOrderDeviceRelevanceEntity> deviceUpdateWrapper = new LambdaUpdateWrapper<>();
        deviceUpdateWrapper.eq(WorkOrderDeviceRelevanceEntity::getWorkOrderId, workOrderEntity.getWorkOrderId());
        workOrderDeviceRelevanceService.remove(deviceUpdateWrapper);
        if (CollectionUtils.isNotEmpty(workOrderEntity.getRelevanceDeviceIds())) {
            for (Integer deviceId : workOrderEntity.getRelevanceDeviceIds()) {
                WorkOrderDeviceRelevanceEntity workOrderDeviceRelevanceEntity = WorkOrderDeviceRelevanceEntity.builder()
                        .workOrderId(workOrderEntity.getWorkOrderId()).deviceId(deviceId).build();
                workOrderDeviceRelevanceService.save(workOrderDeviceRelevanceEntity);
            }
        }

        //关联班组
        LambdaUpdateWrapper<WorkOrderTeamRelevanceEntity> teamUpdateWrapper = new LambdaUpdateWrapper<>();
        teamUpdateWrapper.eq(WorkOrderTeamRelevanceEntity::getWorkOrderId, workOrderEntity.getWorkOrderId());
        workOrderTeamRelevanceService.remove(teamUpdateWrapper);
        if (CollectionUtils.isNotEmpty(workOrderEntity.getRelevanceTeamIds())) {
            for (Integer teamId : workOrderEntity.getRelevanceTeamIds()) {
                WorkOrderTeamRelevanceEntity workOrderTeamRelevanceEntity = WorkOrderTeamRelevanceEntity.builder()
                        .workOrderId(workOrderEntity.getWorkOrderId()).teamId(teamId).build();
                workOrderTeamRelevanceService.save(workOrderTeamRelevanceEntity);
            }
        }
        //关联制造单元
        LambdaUpdateWrapper<WorkOrderLineRelevanceEntity> lineUpdateWrapper = new LambdaUpdateWrapper<>();
        lineUpdateWrapper.eq(WorkOrderLineRelevanceEntity::getWorkOrderId, workOrderEntity.getWorkOrderId());
        workOrderLineRelevanceService.remove(lineUpdateWrapper);
        if (CollectionUtils.isNotEmpty(workOrderEntity.getRelevanceLineIds())) {
            for (Integer lineId : workOrderEntity.getRelevanceLineIds()) {
                WorkOrderLineRelevanceEntity workOrderLineRelevanceEntity = WorkOrderLineRelevanceEntity.builder()
                        .workOrderId(workOrderEntity.getWorkOrderId()).lineId(lineId).build();
                workOrderLineRelevanceService.save(workOrderLineRelevanceEntity);
            }
        }

        redisTemplate.delete(RedisKeyPrefix.getWorkOrderRelevanceDevice(workOrderEntity.getWorkOrderId()));
    }

    /**
     * 新增生效工单
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderEntity addReleasedWorkOrder(WorkOrderEntity entity, String username) {
        // 判断是否需要审批
        if (approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER.getCode())) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        WorkOrderEntity orderEntity = workOrderService.add(entity);

        WorkOrderEntity workOrderById = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, entity.getWorkOrderNumber()).one();
        // 由于新增是固定赋值为待派工，前端无法控制派工状态，更新时需要通过配置文件进行赋值，所以需要设置为null
        workOrderById.setAssignmentState(null);
        workOrderById.setState(WorkOrderStateEnum.RELEASED.getCode());
        workOrderService.updateByWorkId(WorkOrderSmartUpdateDTO.onlyState(workOrderById, username));

        return orderEntity;
    }

    @Override
    public Page<WorkOrderEntityVO> workOrderByPage(Integer current, Integer size, Integer gridId) {
        // 分页获取设备的生产工单列表
        // 条件如下：
        // 1. 生产工单状态为生效、投产、挂起；
        // 2. 生产工单状态为完成，完成时间为当天(0-24);
        Page<WorkOrderEntityVO> page = this.getBaseMapper().workOrderByPage(new Page<>(current, size), gridId, new Date());
        page.getRecords().forEach(workOrderEntityVO -> {
            if (null != workOrderEntityVO.getOrderId()) {
                ProductOrderEntity productOrder = extProductOrderInterface.selectProductOrderById(workOrderEntityVO.getOrderId());

                if (null != productOrder) {
                    workOrderEntityVO.setOrderNumber(productOrder.getProductOrderNumber());
                }
            }
        });
        return page;
    }

    @Override
    public int producedTotal(Integer gridId) {
        return this.getBaseMapper().producedTotal(gridId);
    }

    @Override
    public int producingTotal(Integer gridId) {
        return this.getBaseMapper().producingTotal(gridId);
    }

    @Override
    public int completedTotal(Integer gridId) {
        return this.getBaseMapper().completedTotal(gridId, new Date());
    }

    /**
     * 计算计划工时
     * 根据工单内物料、制造单元在【产能列表】内找到对应的产能值（若找不到对应匹配的，计划工时为0），
     * 方法内部判断是否有无制造单元和工序
     * 若有制造单元并且有工序,计算计划工时,判断是否计划工时超时,超时多久
     * 若没有制造单元或者没有工序,直接返回超时0
     * <p>
     * 1.有工序时，(工序标准调试+工序加工工时*工单计划数量*工序难度系数）的累加和
     * 2.无工序时，工单计划数量/产品制造单元标准产能
     *
     * @param workOrderEntity
     * @return
     */
    @Override
    public double calculatePlannedWorkHours(WorkOrderEntity workOrderEntity) {
        WorkOrderExtendService workOrderExtendService = SpringUtil.getBean(WorkOrderExtendService.class);
        AreaService areaService = SpringUtil.getBean(AreaService.class);
        //工单的计划时长
        double planTimeDuration = 0.0;
        //如果工单保存的物料单位和产能保存的单位相同 则直接计算
        // 查询工单下的工艺工序
        List<CraftProcedureEntity> craftProcedureEntities = workOrderEntity.getCraftProcedureEntities();
        //判断有无工序
        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
            if (workOrderEntity.getLineId() != null) {
                List<ProductionResourceDTO> mainResourceDTOS = basicUnitRelationService.lambdaQuery()
                        .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                        .list().stream()
                        .map(entity -> ProductionResourceDTO.builder()
                                .workCenterType(entity.getWorkCenterType())
                                .productionBasicUnitId(entity.getProductionBasicUnitId())
                                .build()).collect(Collectors.toList());
                // 查询工单关联的关联资源
                Map<Integer, List<ProductionResourceDTO>> relatedResourceMap = capacityService.getRelatedResourceByWorkOrder(Collections.singletonList(workOrderEntity.getWorkOrderNumber()));
                CapacityGetDTO selectDTO = CapacityGetDTO.builder()
                        .materialCode(workOrderEntity.getMaterialCode())
                        .workCenterId(workOrderEntity.getWorkCenterId())
                        .mainResourceDTOS(mainResourceDTOS)
                        .relatedResourceDTOS(relatedResourceMap.getOrDefault(workOrderEntity.getWorkOrderId(), new ArrayList<>()))
                        .build();
                CapacityEntity capacity = capacityService.getCapacity(selectDTO);
                //            无工序时，工单计划数量/产品制造单元标准产能
                double value = capacity == null ? 0.0 : Double.parseDouble(capacity.getCapacity().toString());
                //产品制造单元标准产能
                planTimeDuration = MathUtil.divideDouble(workOrderEntity.getPlanQuantity(), value, 1);
            }
        } else {
            // 有工序时，(工序标准调试工时+工序加工工时*工单计划数量*工序难度系数）的累加和
            for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
                ProcedureRelationWorkHoursEntity entity = procedureRelationWorkHoursService.lambdaQuery()
                        .eq(ProcedureRelationWorkHoursEntity::getCraftId, craftProcedureEntity.getCraftId())
                        .eq(ProcedureRelationWorkHoursEntity::getProcedureId, craftProcedureEntity.getId())
                        .one();
                if (Objects.isNull(entity)) {
                    continue;
                }
                // 标准调试工时
                double preparationTimeSum = entity.getPreparationTime();
                if (Constant.MINUTES.equals(entity.getPreparationTimeUnit())) {
                    // 分钟转换为小时
                    preparationTimeSum = MathUtil.divideDouble(preparationTimeSum, 60, 2);
                }
                if (Constant.SECOND.equals(entity.getPreparationTimeUnit())) {
                    // 秒转换为小时
                    preparationTimeSum = MathUtil.divideDouble(preparationTimeSum, 60 * 60, 2);
                }
                // 加工工时
                double processingHours = entity.getProcessingHours();
                // 工序难度系数
                double degreeOfDifficultySum = entity.getDegreeOfDifficulty();
                // 工序加工工时*工单计划数量*工序难度系数
                double processingSum = MathUtil.mulDouble(processingHours, workOrderEntity.getPlanQuantity(), degreeOfDifficultySum);
                if (Constant.MINUTES.equals(entity.getProcessingHoursUnit())) {
                    // 分钟转换为小时
                    processingSum = MathUtil.divideDouble(processingSum, 60, 2);
                }
                if (Constant.SECOND.equals(entity.getProcessingHoursUnit())) {
                    // 秒转换为小时
                    processingSum = MathUtil.divideDouble(processingSum, 60 * 60, 2);
                }
                planTimeDuration += MathUtil.add(preparationTimeSum, processingSum);
            }
        }
        //保留1位小数
        planTimeDuration = MathUtil.round(planTimeDuration, 2);
        return planTimeDuration;
    }


    /**
     * 保存工单附件列表
     *
     * @param workOrderEntity
     * @param isAdd
     */
    private void saveNewFile(WorkOrderEntity workOrderEntity, boolean isAdd) {
        boolean deleteFlag = false;
        if (!isAdd) {
            appendixService.removeByRelateId(String.valueOf(workOrderEntity.getWorkOrderId()), AppendixTypeEnum.WORKORDER_APPENDIX.getCode());
            deleteFlag = true;
        }
        boolean saveFlag = CollectionUtils.isNotEmpty(workOrderEntity.getAppendixEntities());
        if (saveFlag || deleteFlag) {
            kafkaWebSocketPublisher.sendMessage(TopicEnum.WORK_ORDER_APPENDIX_TOPIC.getTopic(), MessageContent.builder()
                    .time(new Date())
                    .message(String.format("工单: workOrderNumber:%s 附件更新, 请知悉", workOrderEntity.getWorkOrderNumber()))
                    .build());
        }
        if (!saveFlag) {
            return;
        }
        workOrderEntity.getAppendixEntities().forEach(appendixEntity -> {
            appendixEntity.setIsUsed(true);
            appendixEntity.setCreateUser(userAuthenService.getUsername());
            appendixEntity.setCreateTime(new Date());
            appendixEntity.setRelateId(String.valueOf(workOrderEntity.getWorkOrderId()));
            appendixEntity.setType(AppendixTypeEnum.WORKORDER_APPENDIX.getCode());
            appendixEntity.setRelateName(workOrderEntity.getWorkOrderNumber());
            // 附件上传标记地址
            uploadService.markUploadFile(appendixEntity.getFilePath(), appendixEntity);
        });
        // 将文件改为引用
        appendixService.saveOrUpdateBatch(workOrderEntity.getAppendixEntities());
    }

    /**
     * 上传附件
     *
     * @param workOrderEntity
     * @throws Exception
     */
    protected void saveFile(WorkOrderEntity workOrderEntity, Boolean isAdd) {
        Integer workOrderId = workOrderEntity.getWorkOrderId();
        List<WorkOrderFileEntity> fileList = workOrderEntity.getFile();
        //新增
        if (CollectionUtils.isNotEmpty(fileList) && isAdd) {
            //保存工单附件
            for (WorkOrderFileEntity file : fileList) {
                WorkOrderFileEntity build = WorkOrderFileEntity.builder()
                        .workOrderId(workOrderId)
                        .file(file.getFile())
                        .fileName(file.getFileName())
                        .build();
                workOrderFileService.save(build);
                uploadService.markUploadFile(file.getFile(), build);

            }
        } else if (CollectionUtils.isNotEmpty(fileList) && !isAdd) {
            //编辑
            //删除附件并删除绑定关系
            removeFile(workOrderId);
            //保存工单附件
            for (WorkOrderFileEntity file : fileList) {
                WorkOrderFileEntity build = WorkOrderFileEntity.builder()
                        .workOrderId(workOrderId)
                        .file(file.getFile())
                        .fileName(file.getFileName())
                        .build();
                workOrderFileService.save(build);
                uploadService.markUploadFile(file.getFile(), build);
            }
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderEntity batchAdd(WorkOrderEntity workOrderEntity, String username) {
        //生成工单号
        //工单号由编码规则生成，前端传入
        if (StringUtils.isBlank(workOrderEntity.getWorkOrderNumber())) {
            String num = codeFactory.getOrderNumber(workPropertise.getWorkOrderHeader());
            workOrderEntity.setWorkOrderNumber(num);
        }
        if (StringUtils.isBlank(workOrderEntity.getWorkOrderName())) {
            workOrderEntity.setWorkOrderName(workOrderEntity.getWorkOrderNumber());
        }
        //工单编号判断去重
        notRepeat(workOrderEntity.getWorkOrderNumber());
        //获取物料当前库存
        Double currentInventory = delMaterialCurrentInventory(workOrderEntity.getMaterialCode());
        workOrderEntity.setState(WorkOrderStateEnum.CREATED.getCode());
        workOrderEntity.setType(ModelEnum.WORK_ORDER.getType());
        workOrderEntity.setFinishCount(0.0);
        workOrderEntity.setProgress(0.0);
        workOrderEntity.setLineCode(null);
        workOrderEntity.setCreateDate(new Date());
        workOrderEntity.setCreateBy(username);
        workOrderEntity.setUpdateDate(new Date());
        workOrderEntity.setCurrentInventory(currentInventory);
        workOrderEntity.setProductCount(0.0);
        workOrderEntity.setPendentQuantity(workOrderEntity.getPlanQuantity());
        // 如果需要审批，则缺省为待审核状态
        Boolean approveConfig = approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER.getCode());
        if (approveConfig) {
            workOrderEntity.setApprovalStatus(ApprovalStatusEnum.TO_BE_SUBMIT.getCode());
        }
        boolean save = save(workOrderEntity);
        //根据生产工单查询对应的销售订单
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderEntity.getWorkOrderNumber());
        workOrderEntity = this.getWorkOrderById(detailDTO);
        if (save) {
            //父工单关联多个订单
            saveOrderRelation(workOrderEntity);
            //保存计划数量对应得重量记录
            recordWorkOrderMaterialPlanService.addMaterialPlan(workOrderEntity);
            //保存工单附件
            saveFile(workOrderEntity, true);
            //保存工单投产班组成员信息
            workOrderTeamService.add(workOrderEntity);
            // 自动创建流水码
            this.autoGenerate(Stream.of(workOrderEntity).collect(Collectors.toList()));
        }
        // 消息发送
        workOrderService.dealAfterAdd(workOrderEntity);
        return null;
    }

    /**
     * 订单工单关联
     * 1、如果工单对象关联的订单列表数据和销售订单列表数据存在，那么会通过订单列表数据去设置要保存的关联单据编号
     * 2、否则，关联的订单编号不为空，那么订单id也不能为空，否则不会保存关联数据* *
     */
    @Override
    public void saveOrderRelation(WorkOrderEntity workOrderEntity) {
        Integer workOrderId = workOrderEntity.getWorkOrderId();
        //生产订单
        List<ProductOrderEntity> productOrderList = workOrderEntity.getProductOrderList();
        if (CollectionUtils.isNotEmpty(productOrderList)) {
            // 更新订单关联关系,先删除，再插入
            orderWorkOrderService.lambdaUpdate().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                    .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                    .remove();
            List<OrderWorkOrderEntity> orderWorkOrderEntities = productOrderList.stream()
                    .filter(o -> Objects.nonNull(o.getProductOrderId()))
                    .map(o -> OrderWorkOrderEntity.builder()
                            .workOrderId(workOrderId).orderId(o.getProductOrderId())
                            .orderType(OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                            .relatedMaterialLineNumber(workOrderEntity.getRelatedProductOrderMaterialLineNumber()).build())
                    .collect(Collectors.toList());
            orderWorkOrderService.saveBatch(orderWorkOrderEntities);
            String productOrderNumber = productOrderList.stream().map(ProductOrderEntity::getProductOrderNumber).collect(Collectors.joining(","));
            workOrderEntity.setProductOrderNumber(productOrderNumber);
        } else {
            if (StringUtils.isNotBlank(workOrderEntity.getProductOrderNumber()) && workOrderEntity.getProductOrderId() != null) {
                // 更新订单关联关系,先删除，再插入
                orderWorkOrderService.lambdaUpdate().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                        .remove();
                OrderWorkOrderEntity orderWorkOrderEntity = OrderWorkOrderEntity.builder()
                        .workOrderId(workOrderId)
                        .orderId(workOrderEntity.getProductOrderId())
                        .orderType(OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                        .relatedMaterialLineNumber(workOrderEntity.getRelatedProductOrderMaterialLineNumber()).build();
                orderWorkOrderService.save(orderWorkOrderEntity);
            } else {
                orderWorkOrderService.lambdaUpdate().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                        .remove();
            }
        }

        if (StringUtils.isNotBlank(workOrderEntity.getProductOrderNumber())) {
            //推送AMS，变更订单排程状态
            messagePushToKafkaService.pushNewMessage(workOrderEntity.getProductOrderNumber(), Constants.KAFKA_VALUE_CHAIN_TOPIC,
                    AmsEventTypeEnum.PRODUCT_ORDER_MATERIAL_SCHEDULING_STATUS_UPDATE);
        }
        //销售订单
        List<SaleOrderEntity> saleOrderList = workOrderEntity.getSaleOrderList();
        if (CollectionUtils.isNotEmpty(saleOrderList)) {
            // 更新订单关联关系,先删除，再插入
            orderWorkOrderService.lambdaUpdate().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                    .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                    .remove();
            List<OrderWorkOrderEntity> saleOrderWorkOrderEntities = saleOrderList.stream()
                    .filter(o -> Objects.nonNull(o.getSaleOrderId()))
                    .map(o -> OrderWorkOrderEntity.builder()
                            .workOrderId(workOrderId).orderId(o.getSaleOrderId())
                            .orderType(OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                            .relatedMaterialLineNumber(workOrderEntity.getRelatedSaleOrderMaterialLineNumber()).build())
                    .collect(Collectors.toList());
            orderWorkOrderService.saveBatch(saleOrderWorkOrderEntities);
            String saleOrderNumber = saleOrderList.stream().map(SaleOrderEntity::getSaleOrderNumber).collect(Collectors.joining(","));
            workOrderEntity.setSaleOrderNumber(saleOrderNumber);
        } else {
            if (StringUtils.isNotBlank(workOrderEntity.getSaleOrderNumber()) && workOrderEntity.getSaleOrderId() != null) {
                // 更新订单关联关系,先删除，再插入
                orderWorkOrderService.lambdaUpdate().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                        .remove();
                OrderWorkOrderEntity orderWorkOrderEntity = OrderWorkOrderEntity.builder()
                        .workOrderId(workOrderId)
                        .orderId(workOrderEntity.getSaleOrderId())
                        .orderType(OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                        .relatedMaterialLineNumber(workOrderEntity.getRelatedSaleOrderMaterialLineNumber()).build();
                orderWorkOrderService.save(orderWorkOrderEntity);
            } else {
                orderWorkOrderService.lambdaUpdate().eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId)
                        .eq(OrderWorkOrderEntity::getOrderType, OrderNumTypeEnum.SALE_ORDER.getTypeCode())
                        .remove();
            }
        }
        //更新逗号隔开的多个订单号至orderNumber字段
        LambdaUpdateWrapper<WorkOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WorkOrderEntity::getWorkOrderId, workOrderId)
                .set(WorkOrderEntity::getProductOrderNumber, workOrderEntity.getProductOrderNumber())
                .set(WorkOrderEntity::getSaleOrderNumber, workOrderEntity.getSaleOrderNumber())
                .set(WorkOrderEntity::getPackageSchemeCode, workOrderEntity.getPackageSchemeCode());
        update(updateWrapper);
    }

    @Override
    public List<ProductEntity> getProduct() {
        return productMapper.selectList(null);
    }

    @Override
    public List<ProcedureEntity> getProcedure() {
        return procedureService.list();
    }

    @Override
    public List<ProductionLineEntity> getProductionLine() {
        return productionLineMapper.selectList(null);
    }

    @Override
    public List<WorkOrderEntity> getRunningWorkOrderByLineIds(String lineIds) {
        if (StringUtils.isBlank(lineIds)) {
            return new ArrayList<>();
        }
        List<String> lineIdList = Arrays.stream(lineIds.split(Constant.SEP)).collect(Collectors.toList());

        //正在生产的订单：投入状态的工单
        LambdaQueryWrapper<WorkOrderEntity> runningQw = new LambdaQueryWrapper<>();
        runningQw.in(WorkOrderEntity::getLineId, lineIdList)
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .orderByAsc(WorkOrderEntity::getActualStartDate);
        return list(runningQw);
    }

    @Override
    public List<WorkOrderEntity> selectByNumbers(String workOrderNumbers) {
        if (StringUtils.isBlank(workOrderNumbers)) {
            return new ArrayList<>();
        }
        List<String> workOrderNumberList = Stream.of(workOrderNumbers.split(Constants.SEP)).collect(Collectors.toList());
        return this.lambdaQuery()
                .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumberList)
                .list();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderEntity updateByWorkId(WorkOrderEntity entity, String username) {
        return workOrderService.updateByWorkId(WorkOrderSmartUpdateDTO.common(entity, username));
    }

    /**
     * isUpdateRelateOrder 是否更新关联订单数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderEntity updateByWorkId(WorkOrderSmartUpdateDTO dto) {
        WorkOrderEntity entity = dto.getWorkOrder();
        String username = dto.getUsername();
        if (StringUtils.isBlank(entity.getWorkOrderName())) {
            entity.setWorkOrderName(entity.getWorkOrderNumber());
        }
        Date now = Optional.ofNullable(entity.getFakerTime()).orElse(new Date());
        // 不能使用getById()，有缓存，会取到新对象entity，导致新旧对象一样
        WorkOrderEntity old = this.baseMapper.selectByIdNoCache(entity.getWorkOrderId());
        if (old == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(entity.getWorkOrderId()));
        }
        // 是否提交外部系统审批
        boolean submitOtherApprove = false;
        // 需要审批
        Integer approveConfig = approveNodeConfigService.getApproveConfig(ApproveModuleEnum.WORK_ORDER.getReleasedCode());
        if (IsApproveEnum.isApprove(approveConfig) && WorkOrderStateEnum.CREATED.getCode().equals(entity.getState())) {
            if (Boolean.TRUE.equals(entity.getIsSubmitApprove())) {
                // 待提交或者已驳回时，提交审批
                entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                // 外部系统审批
                if (!IsApproveEnum.SYSTEM.getCode().equals(approveConfig)) {
                    submitOtherApprove = true;
                }
            } else if (ApprovalStatusEnum.REJECTED.getCode() == old.getApprovalStatus()) {
                // 已驳回保存后，审批状态改为待提交
                entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_SUBMIT.getCode());
            }
        }
        //计算计划工时color
        double plannedWorkHours = dto.getIsCalPlannedWorkHours() ? calculatePlannedWorkHours(entity) : entity.getPlannedWorkingHours();
        entity.setPlannedWorkingHours(plannedWorkHours);
        // 实际开始时间只能刷新一次
        Date actualStartDate = entity.getState().equals(WorkOrderStateEnum.INVESTMENT.getCode()) && Objects.isNull(old.getActualStartDate()) ? new Date() : old.getActualStartDate();
        entity.setActualStartDate(actualStartDate);

        // 项目和合同，创建关联关系
        if (dto.getIsUpdateProjectContract()) {
            ReceiptProjectContractEntity receiptProjectContract = ReceiptProjectContractEntity.builder()
                    .contractId(ObjectUtil.isNotEmpty(entity.getContractId()) ? Integer.valueOf(entity.getContractId()) : null)
                    .contractName(entity.getContractName())
                    .projectDefineId(ObjectUtil.isNotEmpty(entity.getProjectDefineId()) ? Integer.valueOf(entity.getProjectDefineId()) : null)
                    .projectDefineName(entity.getProjectDefineName())
                    .relationId(entity.getWorkOrderId())
                    .type(ReceiptTypePMSEnum.PRUDUCT_WORK_ORDER.getCode())
                    .createBy(old.getCreateBy())
                    .createTime(old.getCreateDate())
                    .projectNodeIds(entity.getProjectNodeIds())
                    .relationNumber(entity.getWorkOrderNumber())
                    .build();
            receiptProjectContractService.saveRPC(receiptProjectContract);
        }
        // 更新工单
        entity.setFinishCount(Objects.isNull(entity.getFinishCount()) ? old.getFinishCount() : entity.getFinishCount());
        entity.setUnqualified(Objects.isNull(entity.getUnqualified()) ? old.getUnqualified() : entity.getUnqualified());
        updateWorkOrder(entity, now);

        // 状态修改为发放状态时，生成工单和工艺工序关系
        if (!WorkOrderStateEnum.RELEASED.getCode().equals(old.getState()) && entity.getState().equals(WorkOrderStateEnum.RELEASED.getCode()) && entity.getLineId() != null) {
            // 添加设备日历
            DeviceCalendarService deviceCalendarService = SpringUtil.getBean(DeviceCalendarService.class);
            deviceCalendarService.addDevicePlan(old);
        } else if (!WorkOrderStateEnum.FINISHED.getCode().equals(old.getState()) && entity.getState().equals(WorkOrderStateEnum.FINISHED.getCode())) {
            // 工单变更为完成状态下的各种逻辑处理
            doWhenWorkOrderChangeFinishState(entity, username);
        }
        if (dto.getIsUpdateRelateOrder()) {
            saveOrderRelation(entity);
        }
        // 工单状态变更逻辑
        WorkCenterEntity workCenterEntity = workCenterService.getById(entity.getWorkCenterId());
        workOrderStateChange(entity, now, workCenterEntity, old, username, dto.getIsUpdateProductBasicUnitRelation());

        //修改工单计划
//        if (dto.getIsUpdateWorkOrderPlan()) {
//            workOrderPlanService.updateWorkOrderPlan(entity);
//        }
        //保存计划数量对应得重量记录
        if (dto.getIsUpdateMaterialPlan()) {
            recordWorkOrderMaterialPlanService.updateMaterialPlan(entity);
        }
        //上传附件
        if (dto.getIsUpdateFile()) {
            saveNewFile(entity, false);
        }
        //保存工单投产班组成员信息
        if (dto.getIsUpdateWorkOrderTeam()) {
            workOrderTeamService.add(entity);
        }
        // 更新工序工单关联表
        if (dto.getIsUpdateProcedureRelation()) {
            workOrderProcedureRelationService.updateWorkOrderProcedureRelation(entity);
        }
        //保存工单关联资源
        if (dto.getIsUpdateRelevanceResource()) {
            saveRelevanceResource(entity);
        }
        //更新工单投产排序表
        if (dto.getIsUpdateOrderExecuteSeq() && !workCenterEntity.getIsOperation()) {
            orderExecuteSeqService.updateOrderExecuteSeq(entity);
        }

        // 更新到工单-生产基本单元关联表
        if (dto.getIsUpdateProductBasicUnitRelation()) {
            WorkOrderBasicUnitInsertDTO basicUnitInsertDTO = WorkOrderBasicUnitInsertDTO.builder()
                    .workOrderNumber(entity.getWorkOrderNumber())
                    .productBasicUnits(JacksonUtil.convertArray(entity.getProductBasicUnits(), WorkOrderBasicUnitRelationInsertDTO.class))
                    .build();
            basicUnitRelationService.batchSaveWorkOrderBasicUnits(Collections.singletonList(basicUnitInsertDTO));
        }
        // 外部系统审批
        if (submitOtherApprove) {
            WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(entity.getWorkOrderNumber());
            WorkOrderEntity orderObject = getWorkOrderById(detailDTO);
            ApproveFullPathCodeDTO approveDto = ApproveFullPathCodeDTO.builder()
                    .fullPathCode(ApproveModuleEnum.WORK_ORDER.getReleasedCode())
                    .orderNumber(orderObject.getWorkOrderNumber())
                    .orderObject(orderObject)
                    .userName(entity.getUpdateBy())
                    .build();
            // 提交企微审批
            String outerApproveNumber = approveTemplateService.wechatApprove(approveDto);
            this.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, entity.getWorkOrderNumber())
                    .set(WorkOrderEntity::getOuterApproveNumber, outerApproveNumber)
                    .update();
        }
        // 发送任务
        WorkOrderExtendService workOrderExtendService = SpringUtil.getBean(WorkOrderExtendService.class);
        WorkOrderTaskDTO taskDTO = WorkOrderTaskDTO.builder()
                .workOrderNumber(entity.getWorkOrderNumber())
                .build();
        workOrderExtendService.pushToTask(true, taskDTO);

        //删除缓存
        redisTemplate.delete(RedisKeyPrefix.SCANNER_WORK_ORDER + entity.getWorkOrderNumber());
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.SCANNER_WORK_ORDER + entity.getProductOrderNumber() + "*"));
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.WORK_ORDER_PROCEDURE_FID + entity.getWorkOrderNumber() + "*"));

        //临时方法：判断是否是否被激活，如果没有被激活不需要使用回调方法进行处理
        //事务回调方法，事务成功提交之后执行
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    WorkOrderServiceImpl bean = SpringUtil.getBean(WorkOrderServiceImpl.class);
                    bean.dealAfterUpdate(entity, old, username, dto);
                }
            });
        }
        return WorkOrderEntity.builder().workOrderNumber(entity.getWorkOrderNumber()).build();
    }

    @Async
    public void dealAfterUpdate(WorkOrderEntity entity, WorkOrderEntity old, String username, WorkOrderSmartUpdateDTO dto) {
        //根据生产工单查询对应的销售订单
        WorkOrderDetailDTO detailDTO = getConditionByBusinessConfig(ConfigConstant.WORK_ORDER_UPDATE_MSG_CONTENT, new WorkOrderDetailDTO(entity.getWorkOrderNumber()));
        entity = getWorkOrderById(detailDTO);
        // 自动创建流水码
        if (dto.getIsAutoGenerate()) {
            this.autoGenerate(Stream.of(entity).collect(Collectors.toList()));
        }
        if (!entity.getState().equals(old.getState())) {
            //生效 -> 投产，更改生产订单实际开始时间
            if (WorkOrderStateEnum.RELEASED.getCode().equals(old.getState()) &&
                    WorkOrderStateEnum.INVESTMENT.getCode().equals(entity.getState()) &&
                    StringUtils.isNotBlank(entity.getProductOrderNumber())) {
                messagePushToKafkaService.pushNewMessage(entity.getProductOrderNumber(), Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.PRODUCT_ORDER_START_TIME_UPDATE);
            }

            // 如果状态发生变更，需要发布状态变更消息
            messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_STATUS_CHANGE_MESSAGE);
            //有变化发送websocket消息到前端
            this.sendWebSocketMessage(entity, old);

            // 获取物料对象、工序名称，用于组装消息通知对象
            if (CollectionUtils.isEmpty(entity.getCraftProcedureEntities())) {
                setCraftProcedure(entity);
            }

            if (CollectionUtils.isNotEmpty(entity.getCraftProcedureEntities())) {
                List<ProcedureEntity> procedures = procedureService.listByIds(entity.getCraftProcedureEntities().stream().map(CraftProcedureEntity::getProcedureId).collect(Collectors.toSet()));
                String procedureNames = procedures.stream().map(ProcedureEntity::getName).collect(Collectors.joining(Constant.SEP));
                String procedureIds = procedures.stream().map(String::valueOf).collect(Collectors.joining(Constant.SEP));
                entity.setProcedureName(procedureNames);
                entity.setProcedureIds(procedureIds);
            }
            // 推送精制通知
            NoticeConfigBuilder noticeConfigBuilder = NoticeConfigBuilder.builder()
                    .sendUsername(username)
                    .workOrderNumber(entity.getWorkOrderNumber())
                    .lineId(entity.getLineId())
                    .lineName(entity.getLineName())
                    .stateName(WorkOrderStateEnum.getNameByCode(entity.getState()))
                    .preState(WorkOrderStateEnum.getNameByCode(old.getState()))
                    .createTime(DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT))
                    .noticeType(NoticeTypeEnum.WORK_ORDER_STATE_CHANGE_NOTICE.getCode())
                    .procedureId(entity.getProcedureIds())
                    .procedureName(entity.getProcedureName())
                    .productOrderNumber(entity.getProductOrderNumber())
                    .materialCode(entity.getMaterialCode())
                    .materialName(entity.getMaterialFields().getName())
                    .workCenterName(entity.getWorkCenterName())
                    .productionBasicUnitName(entity.getProductionBasicUnitName())
                    .redisKey(NoticeTypeEnum.WORK_ORDER_STATE_CHANGE_NOTICE.getCode() + entity.getWorkOrderNumber() + DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT))
                    .build();
            infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
        }

        if (!Objects.equals(entity.getFinishCount(), old.getFinishCount())) {
            List<ProductOrderEntity> productOrderList = entity.getProductOrderList();
            if (CollectionUtil.isEmpty(productOrderList)) {
                productOrderList = orderWorkOrderService.productOrderListByWorkId(entity);
            }
            //更新订单进度
            if (!CollectionUtil.isEmpty(productOrderList)) {
                messagePushToKafkaService.pushNewMessage(entity.getProductOrderList().get(0), Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.PRODUCT_ORDER_PROCESS_CAL);
            }
        }

        if (dto.getIsUpdateDayCount()) {
            //更新每日产量
            double finishCount = entity.getFinishCount() == null ? 0.0 : entity.getFinishCount();
            double unqualified = entity.getUnqualified() == null ? 0.0 : entity.getUnqualified();
            double oldFinishCount = old.getFinishCount() == null ? 0.0 : old.getFinishCount();
            double oldUnqualified = old.getUnqualified() == null ? 0.0 : old.getUnqualified();
            if (oldFinishCount != finishCount || oldUnqualified != unqualified) {
                WorkOrderDayCountServiceImpl bean = SpringUtil.getBean(WorkOrderDayCountServiceImpl.class);
                bean.updateWorkOrderDayCount(entity);
            }
        }

        //更新制造单元工单关联关系
        updateProductLineRelation(entity);

        // 生产订单自动完成消息推送（生效 -> 完成）
        writeBackProductOrder2WorkOrder.dealWriteBack(entity);
        //工单节点自动上报记录
        nodeAutoReportService.workOrderAutoReport(entity);
        // 推送给ams，计算ams的相关数量
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.BACK_FILL_QUANTITY);
        // 推送价值流消息
        sendValueChainMsg(entity, ConfigConstant.WORK_ORDER_UPDATE_MSG_CONTENT, KafkaMessageTypeEnum.WORK_ORDER_UPDATE_MESSAGE);
    }

    /**
     * @param workOrderAddMsgContent
     * @param workOrderDetailDTO
     * @return
     */
    private WorkOrderDetailDTO getConditionByBusinessConfig(String workOrderAddMsgContent, WorkOrderDetailDTO workOrderDetailDTO) {
        String value = businessConfigValueService.getValue(workOrderAddMsgContent);
        List<String> config = JacksonUtil.parseArray(value, String.class);
        List<String> all = Arrays.stream(WorkOrderMsgConfEnum.values()).map(WorkOrderMsgConfEnum::getCode).collect(Collectors.toList());
        //全部-配置=不需要发送的内容，置为null
        all.removeAll(config);
        if (CollectionUtils.isEmpty(all)) {
            return workOrderDetailDTO;
        }
        for (String code : all) {
            WorkOrderMsgConfEnum.getByCode(code).removeCondition(workOrderDetailDTO, all);
        }

        return workOrderDetailDTO;
    }

    private void sendValueChainMsg(WorkOrderEntity workOrderEntity, String workOrderAddMsgContent, KafkaMessageTypeEnum kafkaMessageTypeEnum) {
        String value = businessConfigValueService.getValue(workOrderAddMsgContent);
        List<String> config = JacksonUtil.parseArray(value, String.class);
        List<String> all = Arrays.stream(WorkOrderMsgConfEnum.values()).map(WorkOrderMsgConfEnum::getCode).collect(Collectors.toList());
        WorkOrderEntity send = workOrderEntity;
        //全部-配置=不需要发送的内容，置为null
        all.removeAll(config);
        if (CollectionUtils.isEmpty(all)) {
            messagePushToKafkaService.pushNewMessage(send, Constants.KAFKA_VALUE_CHAIN_TOPIC, kafkaMessageTypeEnum);
            return;
        }
        send = JacksonUtil.convertObject(workOrderEntity, WorkOrderEntity.class);
        for (String code : all) {
            WorkOrderMsgConfEnum.getByCode(code).removeMsg(send);
        }
        messagePushToKafkaService.pushNewMessage(send, Constants.KAFKA_VALUE_CHAIN_TOPIC, kafkaMessageTypeEnum);
    }


    /**
     * 工单变更为完成状态下的各种逻辑处理
     *
     * @param
     * @return
     */
    private void doWhenWorkOrderChangeFinishState(WorkOrderEntity entity, String username) {
        // 完工时，读取产品检验控制配置，工艺工序的检验方案控制配置（检验对应检验类型的送检单，合格才能通过）
        CheckInspectSchemeDTO schemeDTO = CheckInspectSchemeDTO.builder()
                .workOrderEntity(entity)
                .materialInspectMethodCode(MaterialInspectMethodEnum.PRODUCT_ORDER_BY_ONE_OF_WORK_ORDERS_FINISH.getCode())
                .craftProcedureInspectMethodCode(CraftProcedureInspectMethodEnum.WORK_ORDER_FINISH.getCode())
                .inspectTriggerCodes(Arrays.asList(InspectTriggerConditionEnum.CHECK_BY_FINISH.getCode()))
                .abnormalBehavior(Constant.WORK_ORDER_FINISH)
                .build();
        materialInspectMethodService.checkInspectSchemeController(schemeDTO);
        //计算实际工时、工单执行、时差
        //实际工时（算法：【完成】时间点到第一次【投产】时间点，并剔除挂起的时间。）
        double actualWorkHours = calculateActualWorkHours(entity);
        //生产超时时长 ：实际工时-计划工时
        double result = actualWorkHours - (entity.getPlannedWorkingHours() == null ? 0.0 : entity.getPlannedWorkingHours());
        //工单执行（算法：实际工时-计划工时，若小于等于0，则是按时，大于0则是超时）
        String workOrderExecutionState;
        if (result < 0) {
            //按时
            workOrderExecutionState = WorkOrderExecutionStateEnum.ON_TIME.getCode();
        } else {
            //超时
            workOrderExecutionState = WorkOrderExecutionStateEnum.OVERTIME.getCode();
        }
        entity.setActualWorkingHours(actualWorkHours);
        //去除绝对值，为负则提前完成，为正则超时完成
        entity.setTimeDifference(result);
        entity.setExecutionStatus(workOrderExecutionState);
        entity.setActualEndDate(entity.getActualEndDate() == null ? new Date() : entity.getActualEndDate());
        entity.setSchedulingState(WorkOrderScheduleStateEnum.FINISH.getCode());
        this.updateById(entity);
        // 完工后需要判断`完工自动报工配置`，满足条件可以自动新增一条与计划数相等的报工记录
        Boolean isAutoReportRecord = judgeIsAutoReportRecord(entity.getWorkOrderNumber());
        FullPathCodeDTO autoDto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.AUTO_REPORT_AFTER_FINISH_WORK).build();
        AutoReportAfterFinishWorkConfigDTO autoConfigDTO = businessConfigService.getValueDto(autoDto, AutoReportAfterFinishWorkConfigDTO.class);
        ReportCountService reportCountService = SpringUtil.getBean(ReportCountService.class);
        if (!autoConfigDTO.getPopUpDisplayEnable() && isAutoReportRecord) {
            ReportLineEntity reportLineEntity = ReportLineEntity.builder().workOrder(entity.getWorkOrderNumber()).finishCount(entity.getPlanQuantity()).operator(username).build();
            reportCountService.countOutput(reportLineEntity);
        } else if (isAutoReportRecord && Objects.nonNull(entity.getIsAutoReport()) && entity.getIsAutoReport()) {
            ReportLineEntity reportLineEntity = ReportLineEntity.builder().workOrder(entity.getWorkOrderNumber()).finishCount(entity.getPlanQuantity()).operator(username).build();
            reportCountService.countOutput(reportLineEntity);
        }
        // 工序完成进度判断（工单完成时，判断该工序下，同一销售订单的全部工单是否全部生产完成，
        // 如完成且该工序的全部工单完成数量之和 小于 关联的销售订单计划数之和，则推送消息至精致平台）
        if (workPropertise.getIsPushProcedureMsgNotice()) {
            produceCompleteProgressJudge(entity, username);
        }
    }

//

    /**
     * 工单状态变更逻辑
     */
    private void workOrderStateChange(WorkOrderEntity newEntity, Date now, WorkCenterEntity workCenterEntity, WorkOrderEntity oldEntity, String username, Boolean isUpdateProductBasicUnitRelation) {
        if (oldEntity.getState().equals(newEntity.getState())) {
            return;
        }
        String workOrderNumber = newEntity.getWorkOrderNumber();
        // 工单状态为投入时，
        // 1、限制一条制造单元只允许一个工单在投产状态
        // 2、读取产品检验控制配置，工艺工序的检验方案控制配置（检验对应检验类型的送检单，合格才能通过）
        judgeWhenWorkOrderIsInvestment(newEntity, now, workCenterEntity, username, isUpdateProductBasicUnitRelation);
        // 状态变更为取消 要求没有报工记录
        if (newEntity.getState().equals(WorkOrderStateEnum.CANCELED.getCode())) {
            LambdaQueryWrapper<ReportLineEntity> reportLineQW = new LambdaQueryWrapper<>();
            reportLineQW.eq(ReportLineEntity::getWorkOrder, workOrderNumber)
                    .ne(ReportLineEntity::getFinishCount, 0.0);
            Long count = reportLineMapper.selectCount(reportLineQW);
            if (count != 0) {
                throw new ResponseException(RespCodeEnum.CANCEL_WORK_ORDER_FAILED.fmtDes(workOrderNumber));
            }
            //排产状态改为已取消
            this.lambdaUpdate()
                    .eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                    .set(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.CANCELED.getCode())
                    .update();
            // 取消态需要更新下推记录
            OrderPushDownRecordEntity build = OrderPushDownRecordEntity.builder()
                    .targetOrderType(CategoryTypeEnum.WORK_ORDER.getTypeCode())
                    .targetOrderNumber(newEntity.getWorkOrderNumber())
                    .abnormalRemark("取消")
                    .build();
            orderPushDownRecordService.batchUpdateRecord(Collections.singletonList(build));
        }
        // 更新工单状态变更时间, 并插入到状态变更记录表
        Date stateTime = now;
        if (WorkOrderStateEnum.RELEASED.getCode().equals(oldEntity.getState()) &&
                WorkOrderStateEnum.INVESTMENT.getCode().equals(newEntity.getState())) {
            //生效到投产，小程序可能修改投产时间
            stateTime = newEntity.getActualStartDate() != null ? newEntity.getActualStartDate() : stateTime;
            //改为已排产
            this.lambdaUpdate()
                    .eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                    .set(WorkOrderEntity::getSchedulingState, WorkOrderScheduleStateEnum.INVESTMENT.getCode())
                    .update();
        }
        this.lambdaUpdate()
                .eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                .set(WorkOrderEntity::getStateChangeTime, stateTime)
                .update();
        workOrderStateService.insertState(workOrderNumber, newEntity.getState(), stateTime);

        // 保存工单上料防错物料信息
        if (WorkOrderStateEnum.RELEASED.getCode().equals(newEntity.getState())) {
            checkMaterialService.add(newEntity);
        }
        recordWorkOrderResumeService.deal2(newEntity, username);
        // 挂起/完成/关闭/取消工单时，刷新生产基本单元投产记录
        if (WorkOrderStateEnum.HANG_UP.getCode().equals(newEntity.getState()) ||
                WorkOrderStateEnum.FINISHED.getCode().equals(newEntity.getState()) ||
                WorkOrderStateEnum.CLOSED.getCode().equals(newEntity.getState()) ||
                WorkOrderStateEnum.CANCELED.getCode().equals(newEntity.getState())) {
            WorkOrderExtendService workOrderExtendService = SpringUtil.getBean(WorkOrderExtendService.class);
            workOrderExtendService.dealProductBasicUnitRecordWhenHangupWorkOrder(workOrderNumber);
        }
        //
        OperationLogEntity productOrderLogEntity = OperationLogEntity.builder()
                .module("工单管理")
                .type(OperationType.UPDATE)
                .des("工单:" + workOrderNumber + ", 状态发生变更：" + WorkOrderStateEnum.getNameByCode(oldEntity.getState())
                        + "-> " + WorkOrderStateEnum.getNameByCode(newEntity.getState()))
                .username(username)
                .nickname(userService.getNicknameByUsername(username))
                .createTime(new Date())
                .build();
        operationLogService.manualInsert(productOrderLogEntity);
    }

    /**
     * 设置派工状态 （创建 -- 待派工，生效 -- 待派工/已派工 ，投产、挂起、完成、关闭、取消 -- 已派工）
     */
    private void setAssignmentState(WorkOrderEntity entity) {
        if (entity.getState().equals(WorkOrderStateEnum.RELEASED.getCode())) {
            // 如果派工状态为空，获取派工的业务配置
            FullPathCodeDTO dto = FullPathCodeDTO.builder()
                    .fullPathCode(ConfigConstant.WORK_ORDER_ASSIGNMENT_CONFIG).build();
            WorkOrderAssignmentConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderAssignmentConfigDTO.class);
            // 直接读取配置，不以用户选择的派工状态为准
//            entity.setAssignmentState(StringUtils.isBlank(entity.getAssignmentState()) ? config.getAssignmentState() : entity.getAssignmentState());
            entity.setAssignmentState(config.getAssignmentState());
            if (config.getBasicProdUnitIsRequired() && entity.getProductionBasicUnitId() == null && entity.getAssignmentState().equals(AssignmentStateEnum.ASSIGNED.getType())) {
                throw new ResponseException("由于配置为派工时基本生产单元必填，故工单号:" + entity.getWorkOrderNumber() + "信息填写有误，请检查！");
            }
        } else if (!entity.getState().equals(WorkOrderStateEnum.CREATED.getCode()) && !entity.getState().equals(WorkOrderStateEnum.RELEASED.getCode())) {
            entity.setAssignmentState(AssignmentStateEnum.ASSIGNED.getType());
        }
    }

    /**
     * 工单状态为投入时，
     * 1、限制一条制造单元只允许一个工单在投产状态
     * 2、读取产品检验控制配置，工艺工序的检验方案控制配置（检验对应检验类型的送检单，合格才能通过）
     */
    private void judgeWhenWorkOrderIsInvestment(WorkOrderEntity entity, Date now, WorkCenterEntity workCenterEntity, String username, Boolean isUpdateProductBasicUnitRelation) {
        if (!entity.getState().equals(WorkOrderStateEnum.INVESTMENT.getCode())) {
            return;
        }
        // 投产时，读取产品检验控制配置，工艺工序的检验方案控制配置（检验对应检验类型的送检单，合格才能通过）
        CheckInspectSchemeDTO schemeDTO = CheckInspectSchemeDTO.builder()
                .workOrderEntity(entity)
                .materialInspectMethodCode(MaterialInspectMethodEnum.PRODUCT_ORDER_BY_ONE_OF_WORK_WORK_ORDER_INVESTMENT.getCode())
                .craftProcedureInspectMethodCode(CraftProcedureInspectMethodEnum.WORK_ORDER_INVESTMENT.getCode())
                .inspectTriggerCodes(Collections.singletonList(InspectTriggerConditionEnum.CHECK_BY_INVEST.getCode()))
                .abnormalBehavior(Constant.WORK_ORDER_INVESTMENT)
                .build();
        materialInspectMethodService.checkInspectSchemeController(schemeDTO);

        if (entity.getActualStartDate() == null) {
            entity.setActualStartDate(now);
        }
        // 投产产需校验是否绑定生产基本单元
        if (!workCenterEntity.getIsOperation() && isUpdateProductBasicUnitRelation) {
            if (workCenterEntity.getType().equals(WorkCenterTypeEnum.LINE.getCode())) {
                if (entity.getLineId() == null && CollectionUtils.isEmpty(entity.getProductBasicUnits())) {
                    throw new ResponseException("请选择基本生产单元(制造单元)");
                }
            }
            if (workCenterEntity.getType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
                if (entity.getLineId() == null && CollectionUtils.isEmpty(entity.getProductBasicUnits())) {
                    throw new ResponseException("请选择基本生产单元(班组)");
                }
            }
            if (workCenterEntity.getType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
                if (entity.getLineId() == null && CollectionUtils.isEmpty(entity.getProductBasicUnits())) {
                    throw new ResponseException("请选择基本生产单元(设备)");
                }
            }
        }
        // 该配置为：同一产线/班组是否允许多张工单投产
        boolean concurrent = Boolean.parseBoolean(businessConfigValueService.getValue(ConfigConstant.WORK_ORDER_CONCURRENT));
        if (!concurrent) {
            // 查询出同一条制造单元/班组 正在投产状态的工单
            List<WorkOrderEntity> list = this.lambdaQuery()
                    .select(WorkOrderEntity::getWorkOrderNumber)
                    .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                    .eq(entity.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode()), WorkOrderEntity::getLineId, entity.getLineId())
                    .eq(entity.getWorkCenterType().equals(WorkCenterTypeEnum.TEAM.getCode()), WorkOrderEntity::getTeamId, entity.getTeamId())
                    .list();
            if (list.size() > 1) {
                // 该配置为：是否挂起其他工单
                boolean hangUpOther = Boolean.parseBoolean(businessConfigValueService.getValue(ConfigConstant.HANG_UP_OTHER_WORK_ORDER));
                if (hangUpOther) {
                    //挂起其他工单
                    List<String> workOrderNumbers = list.stream()
                            .map(WorkOrderEntity::getWorkOrderNumber)
                            .filter(workOrderNumber -> !workOrderNumber.equals(entity.getWorkOrderNumber())).collect(Collectors.toList());
                    WorkOrderUpdateStateDTO build = WorkOrderUpdateStateDTO.builder()
                            .workOrderNumbers(workOrderNumbers)
                            .state(WorkOrderStateEnum.HANG_UP.getCode())
                            .username(username)
                            .build();
                    workOrderService.batchUpdateState(build);
                } else {
                    throw new ResponseException(RespCodeEnum.WORK_ORDER_EDIT_FALL);
                }
            }
        }
    }

    /**
     * 更新工单
     */
    private void updateWorkOrder(WorkOrderEntity entity, Date now) {
        // 获取制造单元名称
        if (entity.getLineId() != null) {
            ProductionLineEntity lineEntity = productionLineMapper.selectById(entity.getLineId());
            entity.setLineCode(lineEntity.getProductionLineCode());
            entity.setLineName(lineEntity.getName());
            entity.setWorkCenterId(lineEntity.getWorkCenterId());
            WorkCenterEntity workCenterEntity = workCenterMapper.selectById(lineEntity.getWorkCenterId());
            entity.setWorkCenterName(workCenterEntity.getName());
        }
        if (entity.getSkuId() == null) {
            entity.setSkuId(Constants.SKU_ID_DEFAULT_VAL);
        }
        // 防止前端漏传参数
        if (Objects.nonNull(entity.getCraftId()) && StringUtils.isBlank(entity.getCraftCode())) {
            CraftEntity craftEntity = craftService.getById(entity.getCraftId());
            entity.setCraftCode(craftEntity.getCraftCode());
        }
        // 由于工单允许绑定半成品物料，工艺允许绑定成品工艺，所以有可能前端渲染不出来半成品对应的工艺，导致工艺id为空，所以这里针对此现象做特殊处理
        if (StringUtils.isNotBlank(entity.getCraftCode()) && Objects.isNull(entity.getCraftId())) {
            CraftEntity craftEntity = craftService.lambdaQuery().eq(CraftEntity::getCraftCode, entity.getCraftCode()).one();
            entity.setCraftId(craftEntity.getCraftId());
        }
        // 如果单据类型未赋值，则需根据单据类型配置动态获取默认值
        if (StringUtils.isBlank(entity.getOrderType())) {
            OrderTypeInfoVO defaultOrderTypeVO = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER.getTypeCode());
            entity.setOrderType(defaultOrderTypeVO.getOrderType());
            entity.setBusinessType(defaultOrderTypeVO.getBusinessTypeCode());
        } else {
            entity.setOrderType(entity.getOrderType());
            OrderTypeInfoVO orderTypeInfoVO = orderTypeConfigService.getBusinessTypeByCategoryCodeAndOrderTypeCode(CategoryTypeEnum.WORK_ORDER.getTypeCode(), entity.getOrderType());
            entity.setBusinessType(orderTypeInfoVO.getBusinessTypeCode());
        }
        // 为了防止进度计算有问题，如果计划数量为0，默认赋值为1，此操作仅作为进度计算使用
        double planQuantity = entity.getPlanQuantity() == 0 ? 1 : entity.getPlanQuantity();
        double progress = new BigDecimal(entity.getFinishCount() / planQuantity).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        // 设置生产基本单元id和隔离ID
        setProductionBasicUnitIdAndIsolationId(entity);
        // 设置派工状态 （创建 -- 待派工，生效 -- 待派工/已派工 ，投产、挂起、完成、关闭、取消 -- 已派工）
        setAssignmentState(entity);
        // 更新
        LambdaUpdateWrapper<WorkOrderEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(WorkOrderEntity::getWorkOrderId, entity.getWorkOrderId())
                .set(WorkOrderEntity::getStartDate, entity.getStartDate())
                .set(WorkOrderEntity::getPlannedWorkingHours, entity.getPlannedWorkingHours())
                .set(WorkOrderEntity::getWorkOrderName, entity.getWorkOrderName())
                .set(WorkOrderEntity::getEndDate, entity.getEndDate())
                .set(WorkOrderEntity::getLineCode, entity.getLineCode())
                .set(WorkOrderEntity::getLineId, entity.getLineId())
                .set(WorkOrderEntity::getLineName, entity.getLineName())
                .set(WorkOrderEntity::getWorkCenterName, entity.getWorkCenterName())
                .set(WorkOrderEntity::getWorkCenterId, entity.getWorkCenterId())
                .set(WorkOrderEntity::getWorkCenterType, entity.getWorkCenterType())
                .set(WorkOrderEntity::getState, entity.getState())
                .set(WorkOrderEntity::getSupplierCode, entity.getSupplierCode())
                .set(WorkOrderEntity::getMaterialCode, entity.getMaterialCode())
                .set(WorkOrderEntity::getCraftId, entity.getCraftId())
                .set(WorkOrderEntity::getCraftCode, entity.getCraftCode())
                .set(WorkOrderEntity::getPnumber, entity.getPnumber())
                .set(WorkOrderEntity::getIdSequence, entity.getIdSequence())
                .set(WorkOrderEntity::getRemark, entity.getRemark())
                .set(WorkOrderEntity::getOrderType, entity.getOrderType())
                .set(WorkOrderEntity::getMagNickname, entity.getMagNickname())
                .set(WorkOrderEntity::getMagPhone, entity.getMagPhone())
                .set(WorkOrderEntity::getMagName, entity.getMagName())
                .set(WorkOrderEntity::getPriority, entity.getPriority())
                .set(WorkOrderEntity::getUpdateBy, entity.getUpdateBy())
                .set(WorkOrderEntity::getUpdateDate, now)
                // 实际开始和实际完成只能刷新一次，防止前端不传导致更新后数据为空
                .set(WorkOrderEntity::getActualStartDate, entity.getActualStartDate())
                .set(WorkOrderEntity::getActualEndDate, entity.getActualEndDate())
                .set(WorkOrderEntity::getPlanQuantity, entity.getPlanQuantity())
                .set(WorkOrderEntity::getApprover, entity.getApprover())
                .set(WorkOrderEntity::getActualApprover, entity.getActualApprover())
                .set(WorkOrderEntity::getApprovalStatus, entity.getApprovalStatus())
                .set(WorkOrderEntity::getApprovalSuggestion, entity.getApprovalSuggestion())
                .set(WorkOrderEntity::getApprovalTime, entity.getApprovalTime())
                .set(WorkOrderEntity::getErpDocumentCode, entity.getErpDocumentCode())
                .set(WorkOrderEntity::getPackageQuantity, entity.getPackageQuantity())
                .set(WorkOrderEntity::getCoefficient, entity.getCoefficient())
                .set(WorkOrderEntity::getTeamId, entity.getTeamId())
                .set(WorkOrderEntity::getDeviceId, entity.getDeviceId())
                .set(WorkOrderEntity::getProductionBasicUnitId, entity.getProductionBasicUnitId())
                .set(WorkOrderEntity::getIsolationId, entity.getIsolationId())
                .set(WorkOrderEntity::getSchedulingSequence, entity.getSchedulingSequence())
                .set(WorkOrderEntity::getRelateOrderMaterialId, entity.getRelateOrderMaterialId())
                .set(WorkOrderEntity::getSkuId, entity.getSkuId())
                .set(WorkOrderEntity::getPlannedBatches, entity.getPlannedBatches())
                .set(WorkOrderEntity::getPlansPerBatch, entity.getPlansPerBatch())
                .set(WorkOrderEntity::getActualBatches, entity.getActualBatches())
                .set(WorkOrderEntity::getCustomerCode, entity.getCustomerCode())
                .set(WorkOrderEntity::getCustomerName, entity.getCustomerName())
                .set(WorkOrderEntity::getCustomerMaterialCode, entity.getCustomerMaterialCode())
                .set(WorkOrderEntity::getCustomerMaterialName, entity.getCustomerMaterialName())
                .set(WorkOrderEntity::getCustomerSpecification, entity.getCustomerSpecification())
                .set(WorkOrderEntity::getProjectContract, entity.getProjectContract())
                .set(WorkOrderEntity::getAssignmentState, entity.getAssignmentState())
                .set(WorkOrderEntity::getPendentQuantity, entity.getPendentQuantity())
                .set(WorkOrderEntity::getProductCount, entity.getProductCount())
                .set(WorkOrderEntity::getInStockCount, entity.getInStockCount())
                .set(Objects.nonNull(entity.getOperateByApi()) && entity.getOperateByApi(), WorkOrderEntity::getFinishCount, entity.getFinishCount())
                .set(Objects.nonNull(entity.getOperateByApi()) && entity.getOperateByApi(), WorkOrderEntity::getUnqualified, entity.getUnqualified())
                .set(WorkOrderEntity::getProgress, progress)
                .set(WorkOrderEntity::getWorkOrderExtendFieldOne, entity.getWorkOrderExtendFieldOne())
                .set(WorkOrderEntity::getWorkOrderExtendFieldTwo, entity.getWorkOrderExtendFieldTwo())
                .set(WorkOrderEntity::getWorkOrderExtendFieldThree, entity.getWorkOrderExtendFieldThree())
                .set(WorkOrderEntity::getWorkOrderExtendFieldFour, entity.getWorkOrderExtendFieldFour())
                .set(WorkOrderEntity::getWorkOrderExtendFieldFive, entity.getWorkOrderExtendFieldFive())
                .set(WorkOrderEntity::getWorkOrderExtendFieldSix, entity.getWorkOrderExtendFieldSix())
                .set(WorkOrderEntity::getWorkOrderExtendFieldSeven, entity.getWorkOrderExtendFieldSeven())
                .set(WorkOrderEntity::getWorkOrderExtendFieldEight, entity.getWorkOrderExtendFieldEight())
                .set(WorkOrderEntity::getWorkOrderExtendFieldNine, entity.getWorkOrderExtendFieldNine())
                .set(WorkOrderEntity::getWorkOrderExtendFieldTen, entity.getWorkOrderExtendFieldTen())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldOne, entity.getWorkOrderMaterialExtendFieldOne())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldTwo, entity.getWorkOrderMaterialExtendFieldTwo())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldThree, entity.getWorkOrderMaterialExtendFieldThree())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldFour, entity.getWorkOrderMaterialExtendFieldFour())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldFive, entity.getWorkOrderMaterialExtendFieldFive())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldSix, entity.getWorkOrderMaterialExtendFieldSix())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldSeven, entity.getWorkOrderMaterialExtendFieldSeven())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldEight, entity.getWorkOrderMaterialExtendFieldEight())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldNine, entity.getWorkOrderMaterialExtendFieldNine())
                .set(WorkOrderEntity::getWorkOrderMaterialExtendFieldTen, entity.getWorkOrderMaterialExtendFieldTen())
                .set(entity.getSchedulingState() != null, WorkOrderEntity::getSchedulingState, entity.getSchedulingState())
        //由于排程顺序和排程数量不涉及工单修改页面，所以不进行修改//
        //.set(WorkOrderEntity::getSchedulingSequence, entity.getSchedulingSequence())
        //.set(WorkOrderEntity::getSchedulingCount, entity.getSchedulingCount())
        ;
        this.update(wrapper);
    }

    /**
     * 工序完成进度判断（工单完成时，判断该工序下，同一销售订单的全部工单是否全部生产完成，
     * 如完成且该工序的全部工单完成数量之和 小于 关联的销售订单计划数之和，则推送消息至精致平台）
     */
    private void produceCompleteProgressJudge(WorkOrderEntity entity, String username) {
        // 查询该工单绑定的工序
        List<WorkOrderProcedureRelationEntity> procedureRelationEntities = workOrderProcedureRelationService.getWorkOrderProcedureRelationEntities(entity.getWorkOrderNumber());
        if (CollectionUtils.isEmpty(procedureRelationEntities)) {
            return;
        }
        List<Integer> craftProcedureIds = procedureRelationEntities.stream()
                .map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toList());
        // 通过绑定的工序，获取每个工序下、关联相同的销售订单行ID的所有生产工单
        LambdaQueryWrapper<WorkOrderProcedureRelationEntity> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.in(WorkOrderProcedureRelationEntity::getCraftProcedureId, craftProcedureIds);
        List<WorkOrderProcedureRelationEntity> relationEntities = workOrderProcedureRelationService.list(relationWrapper);
        Map<Integer, List<WorkOrderProcedureRelationEntity>> map = relationEntities.stream()
                .collect(Collectors.groupingBy(WorkOrderProcedureRelationEntity::getCraftProcedureId));

        for (WorkOrderProcedureRelationEntity relationEntity : procedureRelationEntities) {
            // 获取每个工序下、关联相同的销售订单行ID的所有生产工单，过滤创建、取消的生产工单
            List<WorkOrderProcedureRelationEntity> entities = map.get(relationEntity.getCraftProcedureId());
            List<WorkOrderEntity> workOrderEntities = entities.stream()
                    .map(o -> this.getById(o.getWorkOrderId()))
                    .filter(Objects::nonNull)
                    .filter(o -> !o.getState().equals(WorkOrderStateEnum.CREATED.getCode()) &&
                            !o.getState().equals(WorkOrderStateEnum.CANCELED.getCode()))
                    .collect(Collectors.toList());


            // 工单绑定销售订单存在两种情况，第一种是直接绑定销售订单，此时工单会有销售订单行ID。
            // 第二种是绑定了生产订单和销售订单，存在强关联关系，此时工单没有销售订单行ID。
            double planQuantity = 0.0;
            List<Integer> relateSaleOrderMaterialIds = new ArrayList<>();
            String saleOrderCode = null;

            // 获取工单绑定的销售订单行物料，如果不为空，则是直接关联销售订单，不关联生产订单
            Integer relateSaleOrderMaterialId = entity.getRelateOrderMaterialId();
            if (relateSaleOrderMaterialId != null) {
                relateSaleOrderMaterialIds.add(relateSaleOrderMaterialId);
                PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderMaterialIds(Stream.of(relateSaleOrderMaterialId).collect(Collectors.toList())).build());
                SaleOrderMaterialEntity saleOrderMaterialEntity = null;

                if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
                    saleOrderMaterialEntity = pageResult.getRecords().get(0).getSaleOrderMaterial();
                    // 计划数量 = 销售订单的销售数量
                    planQuantity = saleOrderMaterialEntity.getSalesQuantity();
                    // 销售订单号
                    saleOrderCode = saleOrderMaterialEntity.getSaleOrderNumber();
                }

            } else {
                // 获取生产订单列表
                List<ProductOrderEntity> productOrderEntities = orderWorkOrderService.productOrderListByWorkId(entity);
                if (CollectionUtils.isEmpty(productOrderEntities)) {
                    return;
                }
                ProductOrderMaterialEntity productOrderMaterialEntity = productOrderEntities.get(0).getProductOrderMaterials().get(0);
                // 如果为合单，则需找出被合单关联的销售订单
                if (productOrderMaterialEntity.getMergedState().equals(OrderMergeStateEnum.MERGE.getCode())) {
                    PageResult<ProductOrderEntity> pageResult = extProductOrderInterface.getPage(ProductOrderSelectOpenDTO.builder().mergeOrderNumber(productOrderMaterialEntity.getProductOrderNumber()).build());
                    if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
                        relateSaleOrderMaterialIds = pageResult.getRecords().stream()
                                .map(ProductOrderEntity::getProductOrderMaterial)
                                .map(ProductOrderMaterialEntity::getRelateSaleOrderMaterialId)
                                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
                    }
                } else if (productOrderMaterialEntity.getRelateSaleOrderMaterialId() != null) {
                    relateSaleOrderMaterialIds.add(productOrderMaterialEntity.getRelateSaleOrderMaterialId());
                }

                // 查询生产订单关联的销售订单物料
                if (CollectionUtils.isEmpty(relateSaleOrderMaterialIds)) {
                    continue;
                }
                PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderMaterialIds(relateSaleOrderMaterialIds).build());

                if (CollectionUtils.isNotEmpty(pageResult.getRecords())) {
                    List<SaleOrderMaterialEntity> saleOrderMaterialEntities = pageResult.getRecords().stream().map(SaleOrderVO::getSaleOrderMaterial).collect(Collectors.toList());
                    planQuantity = saleOrderMaterialEntities.stream().mapToDouble(SaleOrderMaterialEntity::getSalesQuantity).sum();
                    saleOrderCode = saleOrderMaterialEntities.stream().map(SaleOrderMaterialEntity::getSaleOrderNumber)
                            .collect(Collectors.joining(Constant.SEP));
                }
            }
            // 查询工单绑定的生产订单
            List<ProductOrderMaterialEntity> relatedProductOrderMaterials = new ArrayList<>();
            String productOrderNumbers = "";
            for (WorkOrderEntity workOrderEntity : workOrderEntities) {
                List<ProductOrderEntity> productOrderEntities = orderWorkOrderService.productOrderListByWorkId(workOrderEntity);
                for (ProductOrderEntity productOrderEntity : productOrderEntities) {
                    relatedProductOrderMaterials.addAll(productOrderEntity.getProductOrderMaterials());
                }
                if (entity.getWorkOrderId().equals(workOrderEntity.getWorkOrderId())) {
                    productOrderNumbers = productOrderEntities.stream().map(ProductOrderEntity::getProductOrderNumber).collect(Collectors.joining(Constant.SEP));
                }
            }
            // 获取物料名称及 工单entity关联的生产订单号
            MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, entity.getMaterialCode()).one();

            // 获取同个工序下的工单完成数
            //  工单组成： 1、同个工序下
            //             2、由直接关联销售订单行ID的工单 + 关联生产订单（生产订单关联销售订单行ID）的工单 构成
            double finishCount = getFinishCountInSameProcedure(workOrderEntities, relatedProductOrderMaterials, relateSaleOrderMaterialIds);
            if (finishCount >= 0 && finishCount < planQuantity) {
                try {
                    // 发送通知
                    NoticeConfigBuilder noticeConfigBuilder = NoticeConfigBuilder.builder()
                            .sendUsername(username)
                            .redisKey(NoticeTypeEnum.PRODUCT_QUANTITY_LOWER_THAN_SALES_QUANTITY_NOTICE.getCode() + Constants.UNDER_LINE + entity.getWorkOrderNumber() + Constants.UNDER_LINE + relationEntity.getProcedureId())
                            .workOrderNumber(entity.getWorkOrderNumber())
                            .productionQuantity(finishCount)
                            .noticeType(NoticeTypeEnum.PRODUCT_QUANTITY_LOWER_THAN_SALES_QUANTITY_NOTICE.getCode())
                            .procedureId(String.valueOf(relationEntity.getProcedureId()))
                            .procedureName(relationEntity.getCraftProcedureName())
                            .saleOrderNumber(saleOrderCode)
                            .salesQuantity(planQuantity)
                            .lineName(entity.getLineName())
                            .productOrderNumber(productOrderNumbers)
                            .materialName(materialEntity.getName())
                            .build();
                    infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
                } catch (Exception e) {
                    log.error("推送至精致平台异常，请检查配置");
                }

            }
        }
    }

    /**
     * 获取同个工序下的工单完成数
     * 工单组成：
     * 1、同个工序下
     * 2、由直接关联销售订单行ID的工单 + 关联生产订单（生产订单关联销售订单行ID）的工单 构成
     */
    private double getFinishCountInSameProcedure(List<WorkOrderEntity> workOrderEntities, List<ProductOrderMaterialEntity> relatedProductOrderMaterials,
                                                 List<Integer> relateOrderMaterialIds) {
        List<WorkOrderEntity> list = new ArrayList<>();
        List<WorkOrderEntity> workOrderEntities1 = workOrderEntities.stream()
                .filter(o -> relateOrderMaterialIds.contains(o.getRelateOrderMaterialId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(workOrderEntities1)) {
            list.addAll(workOrderEntities1);
        }
        List<ProductOrderMaterialEntity> productOrderMaterialEntities = relatedProductOrderMaterials.stream()
                .filter(o -> relateOrderMaterialIds.contains(o.getRelateSaleOrderMaterialId()))
                .collect(Collectors.toList());
        List<Integer> orderIds = productOrderMaterialEntities.stream().map(ProductOrderMaterialEntity::getProductOrderId).collect(Collectors.toList());
        List<WorkOrderEntity> workOrderEntities2 = orderWorkOrderService.listWorkOrderByOrderIds(orderIds, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        if (CollectionUtils.isNotEmpty(workOrderEntities2)) {
            // 过滤创建、取消状态、不是该工序的生产工单
            List<WorkOrderEntity> workOrderEntities3 = workOrderEntities2.stream()
                    .filter(workOrderEntities::contains)
                    .collect(Collectors.toList());
            list.addAll(workOrderEntities3);
        }
        double finishCount = list.stream().distinct().mapToDouble(WorkOrderEntity::getFinishCount).sum();
        // 如果存在生效、投产、挂起的工单，则不发送消息通知
        long count = list.stream().filter(o -> !o.getState().equals(WorkOrderStateEnum.FINISHED.getCode())).distinct().count();
        if (count > 0) {
            return -1;
        }
        return finishCount;
    }

    /**
     * 实际工时（算法：【完成】时间点到第一次【投产】时间点，并剔除挂起的时间。）
     *
     * @param entity
     * @return
     */
    private double calculateActualWorkHours(WorkOrderEntity entity) {
        if (entity.getState().equals(WorkOrderStateEnum.FINISHED.getCode())) {
            //计算第一次投产到完成的时长(期间减去挂起的时长)
            return workOrderStateService.getInvestmentToFinishedDuration(entity.getWorkOrderNumber());
        }
        return 0;
    }

    @Override
    public void sendWebSocketMessage(WorkOrderEntity newEntity, WorkOrderEntity oldEntity) {
        String preWorkOrderState = WorkOrderStateEnum.getNameByCode(oldEntity.getState());
        String backWorkOrderState = WorkOrderStateEnum.getNameByCode(newEntity.getState());
        // 1 兼容旧通知，如esop
        String common = String.format("工单信息: 单号[ %s ] 的状态由%s -> %s",
                newEntity.getWorkOrderNumber(),
                preWorkOrderState,
                backWorkOrderState
        );
        kafkaWebSocketPublisher.sendMessage(TopicEnum.WORK_ORDER_STATE_TOPIC.getTopic(), MessageContent.builder()
                .time(new Date())
                .message(common)
                .build());

        List<WorkOrderProcedureRelationEntity> workOrderProcedureRelationEntities = workOrderProcedureRelationService.getWorkOrderProcedureRelationEntities(oldEntity.getWorkOrderNumber());
        String procedureIds = workOrderProcedureRelationEntities.stream().map(o -> String.valueOf(o.getProcedureId())).collect(Collectors.joining(Constant.SEP));
        String procedureNames = workOrderProcedureRelationEntities.stream().map(WorkOrderProcedureRelationEntity::getProcedureName).collect(Collectors.joining(Constant.SEP));

        // 2 根据消息流转配置发送消息
        infoNoticeConfigService.sendLocationMachineNotice(
                NoticeConfigBuilder.builder()
                        .noticeType(NoticeTypeEnum.LOCATION_MACHINE_NOTICE.getCode())
                        .workOrderNumber(newEntity.getWorkOrderNumber())
                        .lineId(newEntity.getLineId())
                        .lineName(newEntity.getLineName())
                        .procedureId(procedureIds)
                        .procedureName(procedureNames)
                        .workCenterId(newEntity.getWorkCenterId())
                        .workCenterName(newEntity.getWorkCenterName())
                        .stateName(backWorkOrderState)
                        .preState(preWorkOrderState)
                        .productOrderNumber(newEntity.getProductOrderNumber())
                        .materialCode(newEntity.getMaterialCode())
                        .materialName(newEntity.getMaterialFields().getName())
                        .workCenterName(newEntity.getWorkCenterName())
                        .productionBasicUnitName(newEntity.getProductionBasicUnitName())
                        .build()
        );
    }


    /**
     * 补全字段，防止更新时丢失信息
     *
     * @param entity
     * @return
     */
    private WorkOrderEntity completedNewWorkOrder(WorkOrderEntity entity) {
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(entity.getWorkOrderNumber());
        WorkOrderEntity old = getWorkOrderById(detailDTO);
        JSONObject oldJsonObject = JSON.parseObject(JSON.toJSONString(old));
        JSONObject newJsonObject = JSON.parseObject(JSON.toJSONString(entity));
        for (Map.Entry<String, Object> entry : newJsonObject.entrySet()) {
            //新值覆盖旧值
            if (entry.getValue() != null) {
                oldJsonObject.put(entry.getKey(), entry.getValue());
            }
        }
        return JacksonUtil.parseObject(oldJsonObject.toJSONString(), WorkOrderEntity.class);
    }

    private Double delMaterialCurrentInventory(String materialCode) {
        //堆放数量
        Double stock = extPurchaseReceiptInterface.getPurchaseReceiptMaterialStackAmount(PurchaseReceiptStackAmountDTO.builder().materialCode(materialCode).build());
        //库存数量
        Double sum = JacksonUtil.getResponseObject(inventoryDetailInterface.getStockInventoryByCode(materialCode), Double.class);
        return (stock == null ? 0 : stock) + (sum == null ? 0 : sum);
    }

    @Override
    public String viewDrawing(String materialCode) {
        LambdaQueryWrapper<MaterialEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(MaterialEntity::getCode, materialCode);
        MaterialEntity materialEntity = materialService.getOne(qw);
        return materialEntity.getFileUrl();
    }

    @Override
    public List<WorkOrderEntity> getNumbersByLineId(Integer productionLineId, String batchTime) {
        LocalDate date = LocalDate.parse(batchTime);
        LocalDateTime start = LocalDateTime.of(date, LocalTime.MIN);
        String startStr = start.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT));
        LocalDateTime end = LocalDateTime.of(date, LocalTime.MAX);
        String endStr = end.format(DateTimeFormatter.ofPattern(DateUtil.DATETIME_FORMAT));
        List<Integer> states = new ArrayList<>();
        states.add(WorkOrderStateEnum.RELEASED.getCode());
        states.add(WorkOrderStateEnum.INVESTMENT.getCode());
        states.add(WorkOrderStateEnum.HANG_UP.getCode());
        states.add(WorkOrderStateEnum.FINISHED.getCode());
        states.add(WorkOrderStateEnum.CLOSED.getCode());
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(WorkOrderEntity::getLineId, productionLineId)
                //早于当天结束时间，晚于当天开始时间
                .le(WorkOrderEntity::getStartDate, endStr)
                .ge(WorkOrderEntity::getEndDate, startStr)
                .in(WorkOrderEntity::getState, states);
        List<WorkOrderEntity> workOrderEntities = this.list(wrapper);
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return null;
        }
        return workOrderEntities;
    }

    @Override
    public List<WorkOrderEntity> getOrdersByLineId(Integer productionLineId) {
        List<Integer> states = new ArrayList<>();
        states.add(WorkOrderStateEnum.RELEASED.getCode());
        states.add(WorkOrderStateEnum.INVESTMENT.getCode());
        states.add(WorkOrderStateEnum.HANG_UP.getCode());
        states.add(WorkOrderStateEnum.FINISHED.getCode());
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(WorkOrderEntity::getLineId, productionLineId)
                .in(WorkOrderEntity::getState, states);
        return this.list(wrapper);
    }

    @Override
    public Page<WorkOrderEntity> listWorkOrderByLineId(Integer lineId, Integer teamId, Integer deviceId, Integer workCenterId, Integer state, String workOrderNumber,
                                                       String workOrderName, String materialCode, String materialName, Integer current, Integer size, String assignmentState,
                                                       Boolean isSubcontract, String projectDefineName) {
        WorkCenterEntity workCenterEntity = workCenterService.getById(workCenterId);
        String workCenterType = workCenterEntity == null ? null : workCenterEntity.getType();
        List<String> workOrderNumbers = new ArrayList<>();
        // 项目名称
        if (StringUtils.isNotBlank(projectDefineName)) {
            ProjectOrderSelect2DTO projectOrderSelect2DTO = ProjectOrderSelect2DTO.builder()
                    .relationType(ReceiptTypePMSEnum.PRUDUCT_WORK_ORDER.getCode())
                    .projectDefineName(projectDefineName).build();
            workOrderNumbers = receiptProjectContractService.getListByProject(projectOrderSelect2DTO);
            if (CollectionUtils.isEmpty(workOrderNumbers)) {
                return new Page<>();
            }
        }
        Page<WorkOrderEntity> page = this.getBaseMapper().selectListWorkOrderByPage(new Page<>(current, size), lineId, teamId, deviceId, workCenterId,
                state, workOrderNumber, workOrderName, materialCode, materialName, workCenterType, assignmentState, isSubcontract, workOrderNumbers);
        List<WorkOrderEntity> list = page.getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return new Page<>();
        }

        String occupyWorkOrderNumber = orderExecuteSeqService.getCalOrderByLineId(lineId);
        // 设置客户编号、名称
        List<Integer> collect = list.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        List<WorkOrderEntity> workOrderEntities = this.listByIds(collect);
        Map<Integer, SaleOrderVO> map = orderWorkOrderService.orderMaterialListByWorkOrders(workOrderEntities);
        Map<Integer, ProductOrderEntity> productOrderEntityMap = orderWorkOrderService.listProductOrderByWorkIdList(collect);
        for (WorkOrderEntity entity : list) {
            SaleOrderVO orderEntity = map.get(entity.getWorkOrderId());
            ProductOrderEntity productOrderEntity = productOrderEntityMap.get(entity.getWorkOrderId());
            if (orderEntity != null) {
                entity.setCustomerName(orderEntity.getCustomerName());
                entity.setCustomerCode(orderEntity.getCustomerCode());
                entity.setSaleOrderVO(orderEntity);
            }
            if (productOrderEntity != null) {
                entity.setProductOrderEntity(productOrderEntity);
            }
            entity.setMaterialFields(materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId()));
            // 判断工单是否正在占用计数器
            if (StringUtils.isNotBlank(occupyWorkOrderNumber) && occupyWorkOrderNumber.equals(entity.getWorkOrderNumber())) {
                entity.setIsCountNow(true);
            }
        }
        // 展示名称
        showStateAndName(list);
        workOrderService.setProjectContract(list);
        workOrderSubcontractService.showSubcontract(list);
        return page;
    }

    @Override
    public List<WorkOrderEntity> listWorkOrderByLineIdForDefect(Integer lineId) {
        List<Integer> states = new ArrayList<>();
        //只要生效、投产状态，不要挂起、完成状态的工单 2021年11月24日15点32分已与游尚锋确认
        states.add(WorkOrderStateEnum.RELEASED.getCode());
        states.add(WorkOrderStateEnum.INVESTMENT.getCode());
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(WorkOrderEntity::getLineId, lineId)
                .in(WorkOrderEntity::getState, states)
                .orderByDesc(WorkOrderEntity::getState);
        return this.list(wrapper);
    }


    /**
     * 统计制造单元下各个状态工单数量
     *
     * @return
     */
    @Override
    public Map<Integer, Long> listStateCount(Integer lineId, Integer deviceId, Integer teamId, Integer workCenterId, Boolean isBarCodePad, String username) throws ParseException {
        Map<Integer, Long> map = new HashMap<>(4);
        map.put(WorkOrderStateEnum.RELEASED.getCode(), 0L);
        map.put(WorkOrderStateEnum.INVESTMENT.getCode(), 0L);
        map.put(WorkOrderStateEnum.HANG_UP.getCode(), 0L);
        map.put(WorkOrderStateEnum.FINISHED.getCode(), 0L);
        WorkCenterEntity workCenterEntity = workCenterService.getById(workCenterId);
        if (workCenterEntity == null) {
            return map;
        }
        String workCenterType = workCenterEntity.getType();
        Page<WorkOrderEntity> releasePage = this.getBaseMapper().selectListWorkOrderByPage(new Page<>(1, 1), lineId, teamId, deviceId, workCenterId,
                WorkOrderStateEnum.RELEASED.getCode(), null, null, null, null, workCenterType, AssignmentStateEnum.ASSIGNED.getType(), null, null);
        Page<WorkOrderEntity> investmentPage = this.getBaseMapper().selectListWorkOrderByPage(new Page<>(1, 1), lineId, teamId, deviceId, workCenterId,
                WorkOrderStateEnum.INVESTMENT.getCode(), null, null, null, null, workCenterType, AssignmentStateEnum.ASSIGNED.getType(), null, null);
        Page<WorkOrderEntity> hangupPage = this.getBaseMapper().selectListWorkOrderByPage(new Page<>(1, 1), lineId, teamId, deviceId, workCenterId,
                WorkOrderStateEnum.HANG_UP.getCode(), null, null, null, null, workCenterType, AssignmentStateEnum.ASSIGNED.getType(), null, null);
        Page<WorkOrderEntity> finishPage = this.getBaseMapper().selectListWorkOrderByPage(new Page<>(1, 1), lineId, teamId, deviceId, workCenterId,
                WorkOrderStateEnum.FINISHED.getCode(), null, null, null, null, workCenterType, AssignmentStateEnum.ASSIGNED.getType(), null, null);

        map.put(WorkOrderStateEnum.RELEASED.getCode(), releasePage.getTotal());
        map.put(WorkOrderStateEnum.INVESTMENT.getCode(), investmentPage.getTotal());
        map.put(WorkOrderStateEnum.HANG_UP.getCode(), hangupPage.getTotal());
        map.put(WorkOrderStateEnum.FINISHED.getCode(), finishPage.getTotal());
        return map;
    }

    /**
     * 查询关联订单(销售订单和生产订单)
     */
    @Override
    public void getOrderDetailList(WorkOrderEntity workOrderEntity) {
        if (workOrderEntity == null) {
            return;
        }
        List<SaleOrderEntity> saleOrders = orderWorkOrderService.saleOrderListByWorkId(workOrderEntity);
        workOrderEntity.setSaleOrderList(saleOrders);
        List<ProductOrderEntity> productOrders = orderWorkOrderService.productOrderListByWorkId(workOrderEntity);
        workOrderEntity.setProductOrderList(productOrders);
    }

    /**
     * 删除订单关联关系
     *
     * @param workOrderId
     */
    private void removeOrderRelation(Integer workOrderId) {
        //根据工单id删除关系
        LambdaQueryWrapper<OrderWorkOrderEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(OrderWorkOrderEntity::getWorkOrderId, workOrderId);
        orderWorkOrderService.remove(lambda);
    }


    /**
     * 根据value赋值
     *
     * @return
     */
    public void setValue(String Index, String value, XSSFSheet sheet) {
        if (!(Index == null || value == null)) {
            String[] split = Index.split(",");
            setCellValue(sheet, Integer.parseInt(split[0].trim()), Integer.parseInt(split[1].trim()), value);
        }
    }

    /**
     * 根据下标设置某行某列的值
     *
     * @return
     */
    private void setCellValue(XSSFSheet sheet, int rowIndex, int colIndex, String value) {
        Row row = sheet.getRow(rowIndex);
        Cell cell = row.getCell(colIndex);
        cell.setCellValue(value);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderEntity deleteById(Integer workOrderId, String username) {
        WorkOrderEntity entity = this.getById(workOrderId);
        if (Objects.isNull(entity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        //为子工单时 有父工单 制造单元不能为空
        if (entity.getIsPid() != null && entity.getIsPid() && entity.getLineId() == null) {
            throw new ResponseException(RespCodeEnum.OPERATION_FAILED);
        }
        if (!this.removeById(workOrderId)) {
            return entity;
        }
        // 反射机制进行异步操作，防止异步失效
        WorkOrderExtendService workOrderExtendService = SpringUtil.getBean(WorkOrderExtendService.class);
        workOrderExtendService.removeAllRelatedDataByWorkOrder(entity, username);

        //为独立工单时 没有父工单 有制造单元
        //为父工单时 没有父工单 没有制造单元
        if (entity.getIsPid() != null && !entity.getIsPid() && entity.getLineId() == null) {
            //删除关联的子工单
            QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(WorkOrderEntity::getPnumber, entity.getWorkOrderNumber());
            remove(wrapper);
        }

        // 删除项目合同关联关系
        receiptProjectContractService.delete(ReceiptTypePMSEnum.PRUDUCT_WORK_ORDER.getCode(), entity.getWorkOrderNumber());
        //删除扫码缓存
        redisTemplate.delete(redisTemplate.keys(RedisKeyPrefix.SCANNER_WORK_ORDER + entity.getProductOrderNumber() + "*"));
        // 推送给ams，计算ams的相关数量
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.BACK_FILL_QUANTITY);
        // 发送任务
        WorkOrderTaskDTO taskDTO = WorkOrderTaskDTO.builder()
                .workOrderNumber(entity.getWorkOrderNumber())
                .build();
        workOrderExtendService.pushToTask(false, taskDTO);
        return entity;
    }

    /**
     * 删除制造单元与工单关联关系
     *
     * @param workOrderId
     */
    private void removeProductLineRation(Integer workOrderId) {
        QueryWrapper<WorkOrderProductLineRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WorkOrderProductLineRelationEntity::getWorkOrderId, workOrderId)
                .isNull(WorkOrderProductLineRelationEntity::getOperationOrderId);
        workOrderProductLineRelationService.remove(queryWrapper);
    }

    /**
     * 删除附件并删除绑定关系
     *
     * @param workOrderId
     */
    private void removeFile(Integer workOrderId) {
        List<WorkOrderFileEntity> workOrderFileEntities = workOrderFileService.getEntityByWorkOrderId(workOrderId);
        List<Integer> collect = workOrderFileEntities.stream().map(WorkOrderFileEntity::getId).collect(Collectors.toList());
        workOrderFileService.removeByIds(collect);
        for (WorkOrderFileEntity workOrderFileEntity : workOrderFileEntities) {
            uploadService.markUnusedFile(workOrderFileEntity.getFile());
        }
    }

    @Override
    public Double getFinishRate(String lineCode) {
        //及时交付率=该制造单元及时完成的工单数量/该制造单元工单完成数量
        List<WorkOrderEntity> finishWorkOrders = this.lambdaQuery()
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode())
                .eq(WorkOrderEntity::getLineCode, lineCode)
                .list();
        // 及时完成的工单：实际结束时间 <= 计划结束时间
        List<WorkOrderEntity> filterList = finishWorkOrders.stream()
                .filter(res -> Objects.nonNull(res.getActualEndDate()))
                .filter(res -> res.getEndDate().compareTo(res.getActualEndDate()) >= 0)
                .collect(Collectors.toList());
        int finishCount = filterList.size();

        long allFinishCount = finishWorkOrders.size();
        if (allFinishCount == 0) {
            return 0.0;
        }
        return MathUtil.divideDouble(finishCount, allFinishCount, 4);
    }

    /**
     * 判断编号是否重复
     */
    protected void notRepeat(String code) {
        //查询编号是否存在
        LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkOrderEntity::getWorkOrderNumber, code);
        if (this.count(queryWrapper) > 0) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_CODE_DUPLICATE.getMsgDes());
        }
    }


    @Override
    public List<WorkOrderStateDTO> getAllWorkOrderByLine(String lineCodes) {
        List<String> strings = Arrays.asList(lineCodes.split(Constants.SEP));
        if (CollectionUtils.isEmpty(strings)) {
            return new ArrayList<>();
        }
        List<WorkOrderEntity> workOrderEntities = this.lambdaQuery()
                .in(WorkOrderEntity::getLineCode, strings)
                .list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return new ArrayList<>();
        }
        List<WorkOrderStateDTO> workOrderList = new ArrayList<>();
        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            String workOrderNumber = workOrderEntity.getWorkOrderNumber();
            WorkOrderStateDTO build = WorkOrderStateDTO.builder()
                    .workOrderNumber(workOrderNumber)
                    .workOrderName(workOrderEntity.getWorkOrderName())
                    .state(WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()))
                    .progress(workOrderEntity.getProgress())
                    .build();
            workOrderList.add(build);
        }
        return workOrderList;
    }


    @Override
    public List<WorkOrderEntity> getSubListById(Integer id) {
        WorkOrderEntity entity = this.getById(id);
        if (entity == null || entity.getIsPid() == null || entity.getIsPid()) {
            return null;
        }
        LambdaQueryWrapper<WorkOrderEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(WorkOrderEntity::getPnumber, entity.getWorkOrderNumber());
        return this.list(qw);
    }

    @Override
    public List<WorkOrderEntity> parentWorkOrderList() {
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(WorkOrderEntity::getIsPid, false);
        return list(wrapper);
    }

    @Override
    public Page<WorkOrderEntity> preparationOrderList(String workOrderNumber, String workOrderName, Integer
            current, Integer size, String username) {
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        LambdaQueryWrapper<WorkOrderEntity> lambda = wrapper.lambda();
        lambda.in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.INVESTMENT.getCode());
        Page<WorkOrderEntity> page = new Page<>();
        if (current == null || size == null) {
            List<WorkOrderEntity> list = this.list(wrapper);
            //给前端展示状态名称和创建人真实名字
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = this.page(new Page<>(current, size), wrapper);
        }
        for (WorkOrderEntity workOrderEntity : page.getRecords()) {
            // 设置展示物料相关字段
            setMaterialFieldsForWorkOrderEntity(workOrderEntity);
        }
        showStateAndName(page.getRecords());
        return page;
    }

    @Override
    public List<FieldInfoDTO> getFieldList(String workOrderId, Integer newState) {
        WorkOrderEntity orderEntity = this.getById(workOrderId);
        if (Objects.isNull(orderEntity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        Integer preState = orderEntity.getState();
        List<FieldInfoDTO> list = new ArrayList<>();
        String materialCode = orderEntity.getMaterialCode();
        if (newState.equals(WorkOrderStateEnum.RELEASED.getCode())) {
            SimpleDateFormat format = new SimpleDateFormat(DateUtil.DATETIME_FORMAT);
            String startDateField = ColumnUtil.getField(WorkOrderEntity::getStartDate);
            FieldInfoDTO startDate = FieldInfoDTO.builder()
                    .fieldInfo(startDateField)
                    .fieldInfoName("计划开始时间")
                    .value(format.format(orderEntity.getStartDate()))
                    .required(0)
                    .modify(0)
                    .timeType(1)
                    .build();
            String endDateField = ColumnUtil.getField(WorkOrderEntity::getEndDate);
            FieldInfoDTO endDate = FieldInfoDTO.builder()
                    .fieldInfo(endDateField)
                    .fieldInfoName("计划结束时间")
                    .value(format.format(orderEntity.getEndDate()))
                    .required(0)
                    .modify(0)
                    .timeType(1)
                    .build();
            String planQuantityField = ColumnUtil.getField(WorkOrderEntity::getPlanQuantity);
            FieldInfoDTO planQuantity = FieldInfoDTO.builder()
                    .fieldInfo(planQuantityField)
                    .fieldInfoName("计划数量")
                    .value(String.valueOf(orderEntity.getPlanQuantity()))
                    .required(0)
                    .modify(0)
                    .timeType(0)
                    .build();
            list.add(startDate);
            list.add(endDate);
            list.add(planQuantity);
        }
        if (preState.equals(WorkOrderStateEnum.RELEASED.getCode())
                && newState.equals(WorkOrderStateEnum.INVESTMENT.getCode())) {
            String actualStartDateField = ColumnUtil.getField(WorkOrderEntity::getActualStartDate);
            FieldInfoDTO actualStartDate = FieldInfoDTO.builder()
                    .fieldInfo(actualStartDateField)
                    .fieldInfoName("实际开始时间")
                    .value("")
                    .required(1)
                    .modify(1)
                    .timeType(1)
                    .build();
            list.add(actualStartDate);
        }
        boolean stateBoolean = newState.equals(WorkOrderStateEnum.HANG_UP.getCode())
                || newState.equals(WorkOrderStateEnum.FINISHED.getCode());
        if (preState.equals(WorkOrderStateEnum.INVESTMENT.getCode())
                && stateBoolean) {
            String actualQuantityField = ColumnUtil.getField(WorkOrderEntity::getFinishCount);
            FieldInfoDTO actualQuantity = FieldInfoDTO.builder()
                    .fieldInfo(actualQuantityField)
                    .fieldInfoName("完成数量")
                    .value(orderEntity.getFinishCount() == null ? "" : String.valueOf(orderEntity.getFinishCount()))
                    .required(0)
                    .modify(0)
                    .timeType(0)
                    .build();
            list.add(actualQuantity);
        }
        if (preState.equals(WorkOrderStateEnum.FINISHED.getCode())
                && newState.equals(WorkOrderStateEnum.CLOSED.getCode())) {
            String actualEndDateField = ColumnUtil.getField(WorkOrderEntity::getActualEndDate);
            FieldInfoDTO actualEndDate = FieldInfoDTO.builder()
                    .fieldInfo(actualEndDateField)
                    .fieldInfoName("实际完成时间")
                    .value("")
                    .required(1)
                    .modify(1)
                    .timeType(1)
                    .build();
            String actualQuantityField = ColumnUtil.getField(WorkOrderEntity::getFinishCount);
            FieldInfoDTO actualQuantity = FieldInfoDTO.builder()
                    .fieldInfo(actualQuantityField)
                    .fieldInfoName("完成数量")
                    .value(orderEntity.getFinishCount() == null ? "" : String.valueOf(orderEntity.getFinishCount()))
                    .required(0)
                    .modify(1)
                    .timeType(0)
                    .build();
            list.add(actualEndDate);
            list.add(actualQuantity);
        }
        //除以上情况，全返回空集合
        return list;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdateTipsDTO updateStateOnly(WorkOrderEntity workOrderEntity, Integer isBatch, String username) {
        WorkOrderEntity entity = this.getById(workOrderEntity.getWorkOrderId());
        if (Objects.isNull(entity)) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderEntity.getWorkOrderId()));
        }
        Integer preState = entity.getState();
        Integer newState = workOrderEntity.getState();
        //判断是否有值
        boolean isSetStart = preState.equals(WorkOrderStateEnum.RELEASED.getCode())
                && newState.equals(WorkOrderStateEnum.INVESTMENT.getCode());

        boolean isSetEnd = preState.equals(WorkOrderStateEnum.FINISHED.getCode())
                && newState.equals(WorkOrderStateEnum.CLOSED.getCode());
        Date actualStartDate = workOrderEntity.getActualStartDate();
        if (isSetStart && actualStartDate == null) {
            throw new ResponseException("请填入实际开始时间");
        }
        Date actualEndDate = workOrderEntity.getActualEndDate();
        if (isSetEnd && actualEndDate == null) {
            throw new ResponseException("请填入实际结束时间");
        }
        // 获取工序工艺
        workOrderEntity.setCraftProcedureEntities(workOrderProcedureRelationService.getCraftProcedureListByWorkOrderNumber(workOrderEntity.getWorkOrderNumber()));
        // 获取绑定的销售订单、生产订单列表
        getOrderDetailList(workOrderEntity);
        workOrderService.updateByWorkId(completedNewWorkOrder(workOrderEntity), username);
        if (isBatch == NO_BATCH) {
            return UpdateTipsDTO.builder().success(workOrderEntity.getWorkOrderNumber()).build();
        }
        List<String> updateSuccess = new ArrayList<>();
        List<String> updateFail = new ArrayList<>();
        updateSuccess.add(workOrderEntity.getWorkOrderNumber());
        //判断是否为父工单
        if (workOrderEntity.getLineId() == null && entity.getIsPid() != null && !workOrderEntity.getIsPid()) {
            String workOrderNumber = workOrderEntity.getWorkOrderNumber();
            LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WorkOrderEntity::getPnumber, workOrderNumber);
            List<WorkOrderEntity> list = this.list(wrapper);
            if (CollectionUtils.isEmpty(list)) {
                String success = String.join(",", updateSuccess);
                return UpdateTipsDTO.builder().success(success).build();
            }
            boolean isFinished = newState.equals(WorkOrderStateEnum.FINISHED.getCode());
            for (WorkOrderEntity orderEntity : list) {
                Integer state = orderEntity.getState();
                boolean b = state.equals(WorkOrderStateEnum.INVESTMENT.getCode())
                        || state.equals(WorkOrderStateEnum.HANG_UP.getCode());
                if (preState.equals(state)) {
                    orderEntity.setState(newState);
                    updateSuccess.add(orderEntity.getWorkOrderNumber());
                } else if (isFinished && b) {
                    orderEntity.setState(newState);
                    updateSuccess.add(orderEntity.getWorkOrderNumber());
                }
                if (isSetStart && orderEntity.getActualStartDate() == null) {
                    orderEntity.setActualStartDate(actualStartDate);
                }
                if (isSetEnd && orderEntity.getActualEndDate() == null) {
                    orderEntity.setActualEndDate(actualEndDate);
                }
                // 获取工序工艺
                orderEntity.setCraftProcedureEntities(workOrderProcedureRelationService.getCraftProcedureListByWorkOrderNumber(orderEntity.getWorkOrderNumber()));
                workOrderService.updateByWorkId(completedNewWorkOrder(orderEntity), username);
            }
            updateFail = list.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        }
        updateFail.removeAll(updateSuccess);
        String success = String.join(",", updateSuccess);
        String fail = "";
        if (CollectionUtils.isNotEmpty(updateFail)) {
            fail = String.join(",", updateFail);
        }
        return UpdateTipsDTO.builder().success(success).fail(fail).build();
    }

    @Override
    public List<WorkOrderEntity> getByOrderId(Integer orderId) {
        LambdaQueryWrapper<OrderWorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderWorkOrderEntity::getOrderId, orderId);
        List<OrderWorkOrderEntity> orderWorkOrderEntities = orderWorkOrderService.list(wrapper);
        List<Integer> workOrderIds = orderWorkOrderEntities.stream().map(OrderWorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return new ArrayList<>();
        }
        workOrderWrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
        return this.list(workOrderWrapper);
    }

    @Override
    public String checkPlan(String workOrderNumber, Double planQuantity) {
        Double quantity = workOrderPlanMapper.getTotalQuantityByWorkOrder(workOrderNumber);
        if (quantity == null) {
            return null;
        }
        if (planQuantity > quantity) {
            return "计划数量大于每日计划数量总和";
        } else if (planQuantity < quantity) {
            return "计划数量小于每日计划数量总和";
        }
        return null;
    }

    @Override
    @Transactional
    public void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username) {
        this.examineOrApprove(approvalStatus, approvalSuggestion, id, username, false, null);
    }

    public void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username, Boolean isBatch, List<WorkOrderEntity> list) {
        //1.8.7版本有批量审批工单功能，与正夫讨论出的结果是逐条审批时只审批该条数据，父子工单用批量审批功能去勾选
        Date approvalTime = new Date();
        if (id == null) {
            return;
        }
        WorkOrderEntity workOrderEntity = this.getById(id);
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderEntity.getWorkOrderNumber());
        WorkOrderEntity entity = getWorkOrderById(detailDTO);
        if (entity.getState() == null) {
            return;
        }
        Integer preApprovalStatus = entity.getApprovalStatus();
        if (preApprovalStatus == null) {
            preApprovalStatus = entity.getState().equals(WorkOrderStateEnum.CREATED.getCode()) ?
                    ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.REJECTED.getCode();
        }
        //将要通过审批
        boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        //将要驳回
        boolean toReject = approvalStatus == ApprovalStatusEnum.REJECTED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        if (toApproved || toReject) {
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(approvalSuggestion);
            entity.setActualApprover(username);
            entity.setApprovalTime(approvalTime);
            // 单据审批更新
            workOrderService.orderApprove(entity, toApproved, isBatch, list);
        }
    }

    /**
     * 单据审批更新
     *
     * @param entity
     * @param toApproved
     * @param isBatch
     * @param list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orderApprove(WorkOrderEntity entity, Boolean toApproved, Boolean isBatch, List<WorkOrderEntity> list) {
        if (toApproved) {
            // 审批前进行必填字段校验
            List<WorkOrderBasicUnitRelationInsertDTO> productBasicUnitDTOS = basicUnitRelationService.lambdaQuery()
                    .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, entity.getWorkOrderNumber())
                    .list().stream()
                    .map(o -> WorkOrderBasicUnitRelationInsertDTO.builder().workCenterId(o.getWorkCenterId()).productionBasicUnitId(o.getProductionBasicUnitId()).build()).collect(Collectors.toList());
            judgeFieldLegality(WorkOrderStateEnum.RELEASED.getCode(), entity.getAssignmentState(), entity, productBasicUnitDTOS);
            entity.setState(WorkOrderStateEnum.RELEASED.getCode());
            // 由于新增是固定赋值为待派工，前端无法控制派工状态，更新时需要通过配置文件进行赋值，所以需要设置为null
            entity.setAssignmentState(null);
        }
        if (!isBatch) {
            workOrderService.updateByWorkId(WorkOrderSmartUpdateDTO.onlyState(entity, entity.getActualApprover()));
        } else {
            // 批量审批在外层更新，因为逐条审批，自动创建流水码会有问题
            list.add(entity);
        }
        List<CraftProcedureEntity> craftProcedureEntities = entity.getCraftProcedureEntities();
        String procedureIds = null;
        String procedureNames = null;
        if (CollectionUtils.isNotEmpty(craftProcedureEntities)) {
            procedureIds = craftProcedureEntities.stream().map(o -> String.valueOf(o.getProcedureId())).collect(Collectors.joining(Constant.SEP));
            procedureNames = craftProcedureEntities.stream().map(CraftProcedureEntity::getProcedureName).collect(Collectors.joining(Constant.SEP));
        }

        // 推送精制通知
        NoticeConfigBuilder noticeConfigBuilder = NoticeConfigBuilder.builder()
                .sendUsername(entity.getActualApprover())
                .workOrderNumber(entity.getWorkOrderNumber())
                .lineId(entity.getLineId())
                .lineName(entity.getLineName())
                .approvalStateName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()))
                .approvalTime(DateUtil.format(entity.getApprovalTime(), DateUtil.DATETIME_FORMAT))
                .createTime(DateUtil.format(entity.getApprovalTime(), DateUtil.DATETIME_FORMAT))
                .noticeType(NoticeTypeEnum.APPROVE_WORK_ORDER_NOTICE.getCode())
                .procedureId(procedureIds)
                .procedureName(procedureNames)
                .productOrderNumber(entity.getProductOrderNumber())
                .materialCode(entity.getMaterialCode())
                .materialName(entity.getMaterialFields().getName())
                .workCenterName(entity.getWorkCenterName())
                .productionBasicUnitName(entity.getProductionBasicUnitName())
                .redisKey(NoticeTypeEnum.APPROVE_WORK_ORDER_NOTICE.getCode() + entity.getWorkOrderNumber())
                .build();
        infoNoticeConfigService.sendInfoNotice(noticeConfigBuilder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveBatch(ApproveBatchDTO dto) {
        List<Integer> ids = dto.getIds();
        Integer approvalStatus = dto.getApprovalStatus();
        if (CollectionUtils.isEmpty(ids) || approvalStatus == null) {
            return;
        }
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(WorkOrderEntity::getWorkOrderId, WorkOrderEntity::getState, WorkOrderEntity::getApprovalStatus, WorkOrderEntity::getApprover)
                .in(WorkOrderEntity::getWorkOrderId, ids);
        List<WorkOrderEntity> entities = this.list(wrapper);
        List<String> approvers = entities.stream().map(WorkOrderEntity::getApprover).distinct().collect(Collectors.toList());
        approvers.remove(dto.getActualApprover());
        if (CollectionUtils.isNotEmpty(approvers)) {
            throw new ResponseException(RespCodeEnum.CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN);
        }
        List<WorkOrderEntity> list = entities.stream().filter(o -> WorkOrderStateEnum.CREATED.getCode().equals(o.getState()))
                .peek(o -> {
                    if (o.getApprovalStatus() == null) {
                        o.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    }
                })
                .filter(o -> o.getApprovalStatus().equals(ApprovalStatusEnum.TO_BE_APPROVAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<WorkOrderEntity> results = new ArrayList<>();
        for (WorkOrderEntity entity : list) {
            examineOrApprove(dto.getApprovalStatus(), dto.getApprovalSuggestion(), entity.getWorkOrderId(), dto.getActualApprover(), true, results);
        }
        if (CollectionUtils.isEmpty(results)) {
            return;
        }
        for (WorkOrderEntity entity : results) {
            WorkOrderSmartUpdateDTO onlyState = WorkOrderSmartUpdateDTO.onlyState(entity, dto.getActualApprover());
            onlyState.setIsAutoGenerate(false);
            workOrderService.updateByWorkId(onlyState);
        }
        // 自动创建流水码
        this.autoGenerate(results);
    }

    @Override
    public List<WorkOrderEntity> getWorkOrderByLineAndState(Integer lineId, List<Integer> stateList) {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.MULTIPLE_LINE_PRODUCTION).build();
        ShowConfigDTO config = businessConfigService.getValueDto(dto, ShowConfigDTO.class);
        // 跨制造单元生产，同一个制造单元类型
        List<Integer> lineIds = new ArrayList<>();
        if (Objects.nonNull(lineId)) {
            if (Boolean.TRUE.equals(config.getEnable())) {
                ProductionLineEntity productionLineEntity = productionLineMapper.selectById(lineId);
                LambdaQueryWrapper<ProductionLineEntity> qw = new LambdaQueryWrapper<>();
                qw.eq(ProductionLineEntity::getModelId, productionLineEntity.getModelId());
                lineIds = productionLineMapper.selectList(qw).stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            } else {
                lineIds.add(lineId);
            }
        }
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(CollectionUtils.isNotEmpty(lineIds), WorkOrderEntity::getLineId, lineIds)
                .in(WorkOrderEntity::getState, stateList)
                .orderByDesc(WorkOrderEntity::getCreateDate);
        List<WorkOrderEntity> result = this.list(wrapper);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        // 批量物料字段
        List<MaterialEntity> materialFields = materialService.listSimpleMaterialByCodesAndSkuIds(result.stream().map(e ->
                MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(e.getMaterialCode())
                        .skuId(e.getSkuId())
                        .build()
        ).collect(Collectors.toList()));
        Map<String, MaterialEntity> materialSkuMap = materialFields.stream()
                .collect(Collectors.toMap(e -> ColumnUtil.getMaterialSku(e.getCode(), e.getSkuEntity()), Function.identity(), (v1, v2) -> v2));
        result.forEach(entity -> entity.setMaterialFields(materialSkuMap.get(ColumnUtil.getMaterialSku(entity.getMaterialCode(), entity.getSkuId()))));
        return result;

    }

    @Override
    public Page<WorkOrderEntity> getWorkOrderByLineAndStatePage(Integer lineId, List<Integer> stateList, String workOrderNumber, Integer current, Integer size) {
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.MULTIPLE_LINE_PRODUCTION).build();
        ShowConfigDTO config = businessConfigService.getValueDto(dto, ShowConfigDTO.class);
        // 跨制造单元生产，同一个制造单元类型
        List<Integer> lineIds = new ArrayList<>();
        if (Objects.nonNull(lineId)) {
            if (Boolean.TRUE.equals(config.getEnable())) {
                ProductionLineEntity productionLineEntity = productionLineMapper.selectById(lineId);
                LambdaQueryWrapper<ProductionLineEntity> qw = new LambdaQueryWrapper<>();
                qw.eq(ProductionLineEntity::getModelId, productionLineEntity.getModelId());
                lineIds = productionLineMapper.selectList(qw).stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            } else {
                lineIds.add(lineId);
            }
        }
        Page<WorkOrderEntity> resultPage = this.lambdaQuery().in(CollectionUtils.isNotEmpty(lineIds), WorkOrderEntity::getLineId, lineIds)
                .in(WorkOrderEntity::getState, stateList)
                .like(StringUtils.isNotBlank(workOrderNumber), WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                .orderByDesc(WorkOrderEntity::getCreateDate)
                .orderByDesc(WorkOrderEntity::getWorkCenterId)
                .page(new Page<>(current == null ? 1 : current, size == null ? 10 : size));

        List<WorkOrderEntity> result = resultPage.getRecords();
        if (CollectionUtils.isEmpty(result)) {
            return resultPage;
        }
        // 批量物料字段
        List<MaterialEntity> materialFields = materialService.listSimpleMaterialByCodesAndSkuIds(result.stream().map(e ->
                MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(e.getMaterialCode())
                        .skuId(e.getSkuId())
                        .build()
        ).collect(Collectors.toList()));
        Map<String, MaterialEntity> materialSkuMap = materialFields.stream()
                .collect(Collectors.toMap(e -> ColumnUtil.getMaterialSku(e.getCode(), e.getSkuEntity()), Function.identity(), (v1, v2) -> v2));
        result.forEach(entity -> entity.setMaterialFields(materialSkuMap.get(ColumnUtil.getMaterialSku(entity.getMaterialCode(), entity.getSkuId()))));
        resultPage.setRecords(result);
        return resultPage;

    }

    @Override
    @Deprecated
    public List<WorkOrderExportDTO> convertToWorkOrderExportDTO(List<WorkOrderEntity> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return new ArrayList<>();
        }
        List<WorkOrderExportDTO> workOrderMaterialDTOS = new ArrayList<>();
        // 获取工单订单关联表数据，获取关联的行号
        List<Integer> workOrderIds = workOrders.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        Map<String, Integer> lineNumberMap = orderWorkOrderService.lambdaQuery().in(OrderWorkOrderEntity::getWorkOrderId, workOrderIds).list().stream()
                .filter(res -> Objects.nonNull(res.getRelatedMaterialLineNumber()))
                .collect(Collectors.toMap(o -> o.getWorkOrderId() + Constant.UNDERLINE + o.getOrderType(), OrderWorkOrderEntity::getRelatedMaterialLineNumber));
        // 查找制造单元对应的模型名称
        Map<Integer, String> lineIdModelNameMap = new HashMap<>();
        Set<Integer> lineIdSet = workOrders.stream().map(WorkOrderEntity::getLineId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(lineIdSet)) {
            Map<Integer, Integer> lineIdModelIdMap = productionLineMapper.selectBatchIds(lineIdSet).stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getModelId));
            if (CollectionUtils.isNotEmpty(lineIdModelIdMap.entrySet())) {
                Map<Integer, String> modelIdNameMap = modelService.listByIds(lineIdModelIdMap.values()).stream().collect(Collectors.toMap(ModelEntity::getId, ModelEntity::getName));
                for (Map.Entry<Integer, Integer> lineIdModelId : lineIdModelIdMap.entrySet()) {
                    lineIdModelNameMap.put(lineIdModelId.getKey(), modelIdNameMap.get(lineIdModelId.getValue()));
                }
            }
        }
        for (WorkOrderEntity workOrderEntity : workOrders) {
            MaterialEntity materialEntity = Objects.isNull(workOrderEntity.getMaterialFields()) ? new MaterialEntity() : workOrderEntity.getMaterialFields();
            WorkOrderExportDTO build = WorkOrderExportDTO.builder()
                    .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                    .workOrderName(workOrderEntity.getWorkOrderName())
                    .stateName(WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()))
                    .assignmentStateName(AssignmentStateEnum.getNameByType(workOrderEntity.getAssignmentState()))
                    .erpDocumentCode(workOrderEntity.getErpDocumentCode())
                    .name(materialEntity.getName())
                    .materialCode(workOrderEntity.getMaterialCode())
                    .standard(materialEntity.getStandard())
                    .drawingNumber(materialEntity.getDrawingNumber())
                    .rawMaterial(materialEntity.getRawMaterial())
                    .unit(materialEntity.getComp())
                    .planQuantity(workOrderEntity.getPlanQuantity())
                    .saleOrderNumber(workOrderEntity.getSaleOrderNumber())
                    .productOrderNumber(workOrderEntity.getProductOrderNumber())
                    .relatedSaleOrderMaterialLineNumber(lineNumberMap.get(workOrderEntity.getWorkOrderId() + Constant.UNDERLINE + OrderNumTypeEnum.SALE_ORDER.getTypeCode()))
                    .relatedProductOrderMaterialLineNumber(lineNumberMap.get(workOrderEntity.getWorkOrderId() + Constant.UNDERLINE + OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode()))
                    .magNickname(workOrderEntity.getMagNickname())
                    .magPhone(workOrderEntity.getMagPhone())
                    .orderTypeName(workOrderEntity.getOrderTypeName())
                    .businessTypeName(workOrderEntity.getBusinessTypeName())
                    .createDate(workOrderEntity.getCreateDate())
                    .startDate(workOrderEntity.getStartDate())
                    .endDate(workOrderEntity.getEndDate())
                    .lineModelName(lineIdModelNameMap.get(workOrderEntity.getLineId()))
                    .lineName(workOrderEntity.getLineName())
                    .procedureName(workOrderEntity.getProcedureName())
                    .procedureAlias(workOrderEntity.getProcedureAlias())
                    .priority(workOrderEntity.getPriority())
                    .unqualified(workOrderEntity.getUnqualified())
                    .finishCount(workOrderEntity.getFinishCount())
                    .inputTotal(workOrderEntity.getInputTotal())
                    .inventoryQuantity(workOrderEntity.getInventoryQuantity())
                    .currentInventory(workOrderEntity.getCurrentInventory())
                    .finStockCount(workOrderEntity.getInStockCount())
                    .productDuration(workOrderEntity.getTheoreticalWorkingHours())
                    .circulationDuration(workOrderEntity.getCirculationDuration())
                    .actualStartDate(workOrderEntity.getActualStartDate())
                    .actualEndDate(workOrderEntity.getActualEndDate())
                    .remark(workOrderEntity.getRemark())
                    .progress(workOrderEntity.getProgress())
                    .customerCode(workOrderEntity.getCustomerCode())
                    .customerName(workOrderEntity.getCustomerName())
                    .customerMaterialCode(workOrderEntity.getCustomerMaterialCode())
                    .customerMaterialName(workOrderEntity.getCustomerMaterialName())
                    .customerSpecification(workOrderEntity.getCustomerSpecification())
                    .deviceName(workOrderEntity.getDeviceName())
                    .teamName(workOrderEntity.getTeamName())
                    .plannedBatches(workOrderEntity.getPlannedBatches())
                    .plansPerBatch(workOrderEntity.getPlansPerBatch())
                    .actualBatches(workOrderEntity.getActualBatches())
                    .theoryHour(workOrderEntity.getTheoryHour())
                    .planTheoryHour(workOrderEntity.getPlanTheoryHour())
                    .produceTheoryHour(workOrderEntity.getProduceTheoryHour())
                    .businessUnitCode(workOrderEntity.getBusinessUnitCode())
                    .businessUnitName(workOrderEntity.getBusinessUnitName())
                    .workOrderExtendFieldOneName(workOrderEntity.getWorkOrderExtendFieldOneName())
                    .workOrderExtendFieldTwoName(workOrderEntity.getWorkOrderExtendFieldTwoName())
                    .workOrderExtendFieldThreeName(workOrderEntity.getWorkOrderExtendFieldThreeName())
                    .workOrderExtendFieldFourName(workOrderEntity.getWorkOrderExtendFieldFourName())
                    .workOrderExtendFieldFiveName(workOrderEntity.getWorkOrderExtendFieldFiveName())
                    .workOrderExtendFieldSixName(workOrderEntity.getWorkOrderExtendFieldSixName())
                    .workOrderExtendFieldSevenName(workOrderEntity.getWorkOrderExtendFieldSevenName())
                    .workOrderExtendFieldEightName(workOrderEntity.getWorkOrderExtendFieldEightName())
                    .workOrderExtendFieldNineName(workOrderEntity.getWorkOrderExtendFieldNineName())
                    .workOrderExtendFieldTenName(workOrderEntity.getWorkOrderExtendFieldTenName())
                    .workOrderMaterialExtendFieldOneName(workOrderEntity.getWorkOrderMaterialExtendFieldOneName())
                    .workOrderMaterialExtendFieldTwoName(workOrderEntity.getWorkOrderMaterialExtendFieldTwoName())
                    .workOrderMaterialExtendFieldThreeName(workOrderEntity.getWorkOrderMaterialExtendFieldThreeName())
                    .workOrderMaterialExtendFieldFourName(workOrderEntity.getWorkOrderMaterialExtendFieldFourName())
                    .workOrderMaterialExtendFieldFiveName(workOrderEntity.getWorkOrderMaterialExtendFieldFiveName())
                    .workOrderMaterialExtendFieldSixName(workOrderEntity.getWorkOrderMaterialExtendFieldSixName())
                    .workOrderMaterialExtendFieldSevenName(workOrderEntity.getWorkOrderMaterialExtendFieldSevenName())
                    .workOrderMaterialExtendFieldEightName(workOrderEntity.getWorkOrderMaterialExtendFieldEightName())
                    .workOrderMaterialExtendFieldNineName(workOrderEntity.getWorkOrderMaterialExtendFieldNineName())
                    .workOrderMaterialExtendFieldTenName(workOrderEntity.getWorkOrderMaterialExtendFieldTenName())
                    .build();
            MaterialTypeConfigFieldExportDTO.setMaterialFieldVale(build, JacksonUtil.convertObject(materialEntity, com.yelink.dfscommon.entity.dfs.MaterialEntity.class));
            workOrderMaterialDTOS.add(build);
        }
        return workOrderMaterialDTOS;
    }

    @Override
    public WorkOrderEntity getInvestmentLatelyWorkOrder(Integer lineId) {
        LambdaQueryWrapper<WorkOrderEntity> runningQw = new LambdaQueryWrapper<>();
        runningQw.eq(WorkOrderEntity::getLineId, lineId)
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .orderByDesc(WorkOrderEntity::getStartDate)
                .orderByDesc(WorkOrderEntity::getWorkCenterId)
                .last(Constant.LIMIT_ONE);
        return this.getOne(runningQw);
    }

    @Override
    public List<WorkOrderEntity> getIncompleteList(String lineId) {
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(WorkOrderEntity::getState,
                        WorkOrderStateEnum.RELEASED.getCode(),
                        WorkOrderStateEnum.HANG_UP.getCode(),
                        WorkOrderStateEnum.INVESTMENT.getCode())
                .eq(WorkOrderEntity::getLineId, lineId);
        return this.list(queryWrapper);
    }


    @Override
    public List<WorkOrderEntity> getQualityList(String workOrderNumber) {
        QueryWrapper<WorkOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(WorkOrderEntity::getState,
                WorkOrderStateEnum.RELEASED.getCode(),
                WorkOrderStateEnum.FINISHED.getCode(),
                WorkOrderStateEnum.HANG_UP.getCode(),
                WorkOrderStateEnum.CLOSED.getCode(),
                WorkOrderStateEnum.INVESTMENT.getCode());
        queryWrapper.lambda()
                .like(StringUtils.isNotEmpty(workOrderNumber), WorkOrderEntity::getWorkOrderNumber, workOrderNumber);
        List<WorkOrderEntity> workOrderEntities = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(workOrderEntities)) {
            for (WorkOrderEntity entity : workOrderEntities) {
                entity.setMaterialFields(materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId()));
            }
        }
        return workOrderEntities;
    }

    @Override
    public Boolean updatePrenatalStatus(String workOrderNumber, Boolean prenatalStatus, String username) {
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity != null && workOrderEntity.getState().equals(WorkOrderStateEnum.RELEASED.getCode())) {
            UpdateWrapper<WorkOrderEntity> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                    .set(WorkOrderEntity::getPrenatalStatus, prenatalStatus)
                    .set(WorkOrderEntity::getUpdateDate, new Date())
                    .set(WorkOrderEntity::getUpdateBy, username);
            return this.update(wrapper);
        } else {
            //非生效状态下的工单不允许修改
            throw new ResponseException(RespCodeEnum.NOT_ALLOW_TO_UPDATE);
        }
    }

    @Override
    public List<CraftFileDTO> getWorkOrderFileByWorkNumber(String workOrderNumber, String fileName) {
        List<CraftFileDTO> workOrderFileList;
        if (StringUtils.isBlank(fileName)) {
            workOrderFileList = selectWorkOrderFile(workOrderNumber);
        } else {
            //模糊查询
            workOrderFileList = search(fileName, selectWorkOrderFile(workOrderNumber));
        }
        return workOrderFileList;
    }

    @Override
    public List<WorkOrderEntity> selectByNumbersAndOther(List<String> workOrderNumbers, Integer lineId, String materialCode) {
        LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
        WrapperUtil.eq(queryWrapper, WorkOrderEntity::getLineId, lineId);
        WrapperUtil.eq(queryWrapper, WorkOrderEntity::getMaterialCode, materialCode);
        List<WorkOrderEntity> list = this.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            for (WorkOrderEntity workOrderEntity : list) {
                // 查询工艺工序
                setCraftProcedure(workOrderEntity);
            }

        }
        return list;
    }

    @Override
    public List<CommonType> getAllWorkOrderExecutionState() {
        WorkOrderExecutionStateEnum[] values = WorkOrderExecutionStateEnum.values();
        List<CommonType> states = new ArrayList<>();
        for (WorkOrderExecutionStateEnum stateEnum : values) {
            states.add(CommonType.builder()
                    .type(stateEnum.getCode())
                    .name(stateEnum.getName())
                    .build());
        }
        return states;
    }


    @Override
    public Page<WorkOrderEntity> getWorkOrderListByProcedure(Integer procedureId, Integer current, Integer size, String username) {
        List<String> isolationIds = new ArrayList<>();
        List<String> workCenterIds = new ArrayList<>();
        // 数据隔离，根据用户名获取隔离信息
        if (StringUtils.isNotBlank(username)) {
            IsolationDTO isolationDTO = isolationService.getIsolationDTO(username);
            isolationIds = isolationDTO.getAllWorkCenter() ? new ArrayList<>() : isolationDTO.getIsolationIds();
            workCenterIds = isolationDTO.getAllWorkCenter() ? new ArrayList<>() : isolationDTO.getWorkCenterIds();
        }
        // 过滤条件 工序 状态-投产、挂起、生效，且需要按状态顺序展示
        Page<WorkOrderEntity> page = this.getBaseMapper().selectPageByProcedureId(new Page<>(current, size), procedureId, isolationIds, workCenterIds);

        List<WorkOrderEntity> list = page.getRecords();
        if (CollectionUtils.isEmpty(list)) {
            return new Page<>();
        }
        List<MaterialCodeAndSkuIdSelectDTO> codes = list.stream()
                .map(materielLine -> MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(materielLine.getMaterialCode())
                        .skuId(materielLine.getSkuId()).build())
                .collect(Collectors.toList());
        Map<String, com.yelink.dfs.entity.product.MaterialEntity> codeMaterialMap = materialService.getMaterialEntity(codes);

        for (WorkOrderEntity workOrderEntity : list) {
            workOrderEntity.setMaterialFields(codeMaterialMap.get(ColumnUtil.getMaterialSku(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId())));
            // 设置工单绑定的工艺工序id  for订单报工app
            WorkOrderProcedureRelationEntity relationEntity = workOrderProcedureRelationService.getOneByWorkOrderAndProcedureId(workOrderEntity.getWorkOrderId(), procedureId);
            if (relationEntity != null) {
                workOrderEntity.setCraftProcedureId(relationEntity.getCraftProcedureId());
            }
            setProductionBasicUnit(workOrderEntity);
        }
        return page;
    }

    @Override
    public Page<WorkOrderEntity> getWorkOrderListByWorkCenterId(Integer workCenterId, Integer teamId, String lineCode, String workOrderNumber, String customerName, String materialName,
                                                                String materialCode, String startDate, String endDate, String state, Integer current, Integer size, String username) {
        LambdaQueryWrapper<WorkOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(lineCode)) {
            LambdaQueryWrapper<ProductionLineEntity> lineWrapper = new LambdaQueryWrapper<>();
            lineWrapper.eq(ProductionLineEntity::getProductionLineCode, lineCode);
            ProductionLineEntity lineEntity = productionLineMapper.selectOne(lineWrapper);
            if (lineEntity == null) {
                throw new ResponseException("请先添加制造单元");
            }
            queryWrapper.eq(WorkOrderEntity::getLineCode, lineCode);
            workCenterId = lineEntity.getWorkCenterId();
        }
        queryWrapper.eq(workCenterId != null, WorkOrderEntity::getWorkCenterId, workCenterId);
        queryWrapper.eq(teamId != null, WorkOrderEntity::getTeamId, teamId);
        //  过滤掉工单完成状态的 实际完成时间 在当前日期2天之外的数据
        queryWrapper.apply("work_order_id not in (\n" +
                "SELECT work_order_id FROM `dfs_work_order` where state = 5 and \n" +
                "DATE_FORMAT(actual_end_date ,'%Y-%m-%d') <= DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 2 DAY),'%Y-%m-%d'))");
        //queryWrapper.and(wrapper -> wrapper.in(WorkOrderEntity::getLineId, lineIds).or().isNull(WorkOrderEntity::getLineId));
        // 数据隔离
        Boolean hasPermission = isolationService.dataIsolation(username, queryWrapper);
        if (!hasPermission) {
            return new Page<>();
        }

        List<WorkOrderEntity> list = this.list(queryWrapper);
        List<String> workOrderNumbers = list.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return new Page<>();
        }
        return getWorkOrderListPage(workOrderNumber, customerName, materialName, materialCode, startDate, endDate, state, current, size, workOrderNumbers);
    }

    /**
     * 分页获取工单列表
     *
     * @param workOrderNumber
     * @param customerName
     * @param materialName
     * @param materialCode
     * @param startDate
     * @param endDate
     * @param current
     * @param size
     * @param workOrderNumbers
     * @return
     */
    private Page<WorkOrderEntity> getWorkOrderListPage(String workOrderNumber, String customerName, String materialName, String materialCode,
                                                       String startDate, String endDate, String state, Integer current, Integer size, List<String> workOrderNumbers) {
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
        // 过滤状态 生效、投产、挂起、完成
        wrapper.in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(),
                WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode(), WorkOrderStateEnum.FINISHED.getCode());
        WrapperUtil.like(wrapper, WorkOrderEntity::getWorkOrderNumber, workOrderNumber);
        WrapperUtil.like(wrapper, WorkOrderEntity::getMaterialCode, materialCode);
        WrapperUtil.between(wrapper, WorkOrderEntity::getStartDate, startDate, endDate);
        WrapperUtil.between(wrapper, WorkOrderEntity::getEndDate, startDate, endDate);
        if (StringUtils.isNotBlank(state)) {
            wrapper.in(WorkOrderEntity::getState, Arrays.stream(state.split(Constant.SEP)).collect(Collectors.toList()));
        }
        wrapper.orderByDesc(WorkOrderEntity::getCreateDate)
                .orderByDesc(WorkOrderEntity::getWorkOrderId);

        //通过物料名称查询
        if (StringUtils.isNotBlank(materialName)) {
            List<String> codeCollect = materialService.lambdaQuery()
                    .select(MaterialEntity::getCode)
                    .like(MaterialEntity::getName, materialName)
                    .list()
                    .stream().map(MaterialEntity::getCode).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(codeCollect)) {
                return new Page<>(current, size);
            }
            wrapper.in(WorkOrderEntity::getMaterialCode, codeCollect);
        }
        //根据客户名称过滤
        if (StringUtils.isNotBlank(customerName)) {
            SaleOrderSelectOpenDTO build = SaleOrderSelectOpenDTO.builder().customerName(customerName).build();
            PageResult<SaleOrderVO> responsePage = extSaleOrderInterface.getPage(build);
            if (CollectionUtils.isEmpty(responsePage.getRecords())) {
                return new Page<>(current, size);
            }
            List<Integer> orderIds = responsePage.getRecords().stream().map(SaleOrderVO::getSaleOrderId).collect(Collectors.toList());
            List<Integer> workOrderIds = orderWorkOrderService.lambdaQuery()
                    .select(OrderWorkOrderEntity::getWorkOrderId)
                    .in(OrderWorkOrderEntity::getOrderId, orderIds)
                    .list().stream().map(OrderWorkOrderEntity::getWorkOrderId)
                    .collect(Collectors.toList());
            wrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds);
        }

        Page<WorkOrderEntity> page = new Page<>();
        if (current == null || size == null) {
            List<WorkOrderEntity> list = this.list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = this.page(new Page<>(current, size), wrapper);
        }
        List<WorkOrderEntity> workOrderList = page.getRecords();
        for (WorkOrderEntity workOrderEntity : workOrderList) {
            // 设置状态名称
            workOrderEntity.setStateName(WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()));
            // 设置物料规格
            workOrderEntity.setMaterialFields(materialService.getEntityByCodeAndSkuId(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId()));
        }
        return page;
    }


    /**
     * 根据文件名称进行模糊查询
     *
     * @param fileName
     * @param workOrderFileList
     * @return
     */
    private List<CraftFileDTO> search(String fileName, List<CraftFileDTO> workOrderFileList) {
        List<CraftFileDTO> results = new ArrayList<>();
        Pattern pattern = Pattern.compile(fileName);
        for (CraftFileDTO craftFileDTO : workOrderFileList) {
            Matcher matcher = pattern.matcher(craftFileDTO.getName());
            //可以进行模糊查询
            if (matcher.find()) {
                results.add(craftFileDTO);
            }
        }
        return results;
    }

    /**
     * 根据工单号查询工单附件
     *
     * @param workOrderNumber
     * @return
     */
    private List<CraftFileDTO> selectWorkOrderFile(String workOrderNumber) {
        List<CraftFileDTO> workOrderFileList = new ArrayList<>();
        //1.根据工单号查询对应的工单
        WorkOrderDetailDTO detailDTO = WorkOrderDetailDTO.builder()
                .workOrderNumber(workOrderNumber)
                // 附件信息
                .isShowFileInfo(true)
                .build();
        WorkOrderEntity workOrderEntity = getWorkOrderById(detailDTO);
        if (workOrderEntity != null) {
            //2.查询对应的工单附件列表
            List<WorkOrderFileEntity> fileList = workOrderEntity.getFile();
            if (CollectionUtils.isNotEmpty(fileList)) {
                List<CraftFileDTO> collect = fileList.stream()
                        .map(file -> CraftFileDTO.builder().name(file.getFileName()).url(file.getFile()).build()).collect(Collectors.toList());
                workOrderFileList.addAll(collect);
            }
        }
        return workOrderFileList;
    }


    /**
     * 获取工单物料的可选工序列表
     */
    @Override
    public List<CraftProcedureEntity> getCraftProcedureList(Integer craftId, String materialCode) {
        List<CraftProcedureEntity> resultList = new ArrayList<>();
        CraftEntity craftEntity = Objects.isNull(craftId) ? craftService.getCraftByMaterialCode(materialCode) : craftService.getById(craftId);
        if (craftEntity == null) {
            return resultList;
        }
        resultList = craftProcedureService.listByCraftId(craftEntity.getCraftId());
        resultList = resultList.stream().filter(o -> OutsourcingProcedureEnum.YES.getCode() != o.getIsSubContractingOperation()).filter(o -> o.getWorkCenterIds() != null).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<CraftProcedureInspectControllerEntity> getWorkOrderCraftScheme(String workOrderNumber, Integer inspectSheetType) {
        LambdaQueryWrapper<WorkOrderEntity> workorderWrapper = new LambdaQueryWrapper<>();
        workorderWrapper.eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber);
        List<WorkOrderEntity> workOrderEntityList = this.baseMapper.selectList(workorderWrapper);
        if (CollectionUtils.isEmpty(workOrderEntityList) || ObjectUtils.isEmpty(workOrderEntityList.get(0).getCraftId())) {
            return new ArrayList<>();
        }

        // 工单下工艺绑定的工序
        LambdaQueryWrapper<WorkOrderProcedureRelationEntity> procedureRelationWrapper = new LambdaQueryWrapper<>();
        procedureRelationWrapper.eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumber);
        List<WorkOrderProcedureRelationEntity> procedureRelationEntityList = workOrderProcedureRelationMapper.selectList(procedureRelationWrapper);
        Set<Integer> selectedRelationIdSet = new HashSet<>();
        if (!procedureRelationEntityList.isEmpty()) {
            selectedRelationIdSet = procedureRelationEntityList.stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toSet());
        }


        LambdaQueryWrapper<CraftProcedureInspectControllerEntity> inspectWrapper = new LambdaQueryWrapper<>();
        inspectWrapper.eq(CraftProcedureInspectControllerEntity::getCraftId, workOrderEntityList.get(0).getCraftId());
        inspectWrapper.eq(CraftProcedureInspectControllerEntity::getInspectTypeCode, inspectSheetType);
        inspectWrapper.in(!selectedRelationIdSet.isEmpty(), CraftProcedureInspectControllerEntity::getCraftProcedureId, selectedRelationIdSet);
        List<CraftProcedureInspectControllerEntity> inspectControllerEntityList = craftProcedureInspectControllerMapper.selectList(inspectWrapper);
        if (CollectionUtils.isEmpty(inspectControllerEntityList)) {
            return new ArrayList<>();
        }

        return inspectControllerEntityList.stream().filter(item -> !StringUtils.isEmpty(item.getInspectSchemeName())).collect(Collectors.toList());
    }

    /**
     * 刷新作业工单相关数量
     *
     * @param workOrderNumber
     */
    @Override
    public void refreshProcedureCount(String workOrderNumber) {
        ResponseData response = assignmentInterface.getOperationOrder(workOrderNumber);
        WorkOrderEntity workOrderByNumber = this.getSimpleWorkOrderByNumber(workOrderNumber);
        WorkOrderEntity workOrderEntity = WorkOrderEntity.builder().workOrderId(workOrderByNumber.getWorkOrderId())
                .planQuantity(workOrderByNumber.getPlanQuantity()).build();
        List<OperationOrderDTO> operationOrder = JacksonUtil.getResponseArray(response, OperationOrderDTO.class);
        if (CollectionUtils.isNotEmpty(operationOrder)) {
            // 设置完成数量 过滤掉状态不为完成的作业工单
            double finishCount = operationOrder.stream()
                    .filter(o -> o.getFinishCount() != null).mapToDouble(OperationOrderDTO::getFinishCount).sum();
            workOrderEntity.setFinishCount(finishCount);
            // 设置排产数量 状态为完成 计算完成数 否则计算排产数
            double finishCountSum = operationOrder.stream().filter(o -> WorkOrderStateEnum.FINISHED.getCode().equals(o.getState()))
                    .filter(o -> o.getFinishCount() != null).mapToDouble(OperationOrderDTO::getFinishCount).sum();
            double planCountSum = operationOrder.stream().filter(o -> !WorkOrderStateEnum.FINISHED.getCode().equals(o.getState()))
                    .filter(o -> o.getProductCount() != null).mapToDouble(OperationOrderDTO::getProductCount).sum();
            double productCount = finishCountSum + planCountSum;
            workOrderEntity.setProductCount(productCount);
            workOrderEntity.setPendentQuantity((workOrderEntity.getPlanQuantity() - productCount) < 0 ? 0.0 : (workOrderEntity.getPlanQuantity() - productCount));
            // 设置不良数量
            double unqualifiedSum = operationOrder.stream().filter(o -> o.getUnqualified() != null).mapToDouble(OperationOrderDTO::getUnqualified).sum();
            workOrderEntity.setUnqualified(unqualifiedSum);

            //实际工时
            double actualWorkingHours = operationOrder.stream().filter(o -> o.getActualWorkingHours() != null).mapToDouble(OperationOrderDTO::getActualWorkingHours).sum();
            workOrderEntity.setActualWorkingHours(actualWorkingHours);
            workOrderEntity.setProgress(finishCount / workOrderByNumber.getPlanQuantity());
            this.updateById(workOrderEntity);
            //刷新工单每日完成表
            RecordWorkOrderDayCountService recordWorkOrderDayCountService = SpringUtil.getBean(RecordWorkOrderDayCountService.class);
            if (recordWorkOrderDayCountService != null) {
                recordWorkOrderDayCountService.updateWorkOrderDayCount(getWorkOrderByNumber(workOrderNumber));
            }
        }
    }

    /**
     * 刷新作业工单投入量
     *
     * @param workOrderNumber
     */
    @Override
    public void refreshInputCount(String workOrderNumber) {
        ResponseData response = assignmentInterface.getOperationOrder(workOrderNumber);
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNumber);
        List<OperationOrderDTO> operationOrder = JacksonUtil.getResponseArray(response, OperationOrderDTO.class);
        if (CollectionUtils.isNotEmpty(operationOrder)) {
            // 设置排产数量 状态为完成 计算完成数 否则计算排产数
            double inputCount = operationOrder.stream()
                    .filter(o -> o.getInputTotal() != null).mapToDouble(OperationOrderDTO::getInputTotal).sum();
            WorkOrderEntity build = WorkOrderEntity.builder()
                    .workOrderId(workOrderEntity.getWorkOrderId())
                    .inputTotal(inputCount).build();
            this.updateById(build);
        }
    }


    @Override
    public List<WorkOrderEntity> getTheStateList(WorkOrderSelectDTO workOrderSelectDTO, String username) {
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        //生效、投产、挂起
        wrapper.in(WorkOrderEntity::getState, WorkOrderStateEnum.RELEASED.getCode(),
                WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
        WrapperUtil.like(wrapper, WorkOrderEntity::getWorkOrderNumber, workOrderSelectDTO.getWorkOrderNumber());
        if (username != null) {
            // 数据隔离，只能查询角色绑定工作中心下的工单
            Boolean hasPermission = isolationService.dataIsolation(username, wrapper);
            if (!hasPermission) {
                throw new ResponseException(RespCodeEnum.NO_WORK_CENTER_PERMISSION);
            }
        }
        List<WorkOrderEntity> listTemp = this.list(wrapper);
        List<WorkOrderEntity> list = new ArrayList<>();
        for (WorkOrderEntity workOrderEntityTemp : listTemp) {
            //是否筛除没开启作业工单的工单
            if (workOrderSelectDTO.getIsAssignment() != null) {
                Boolean isAssignment = workCenterService.isOperationWorkOrder(workOrderEntityTemp.getWorkOrderId());
                if (!workOrderSelectDTO.getIsAssignment() && isAssignment) {
                    continue;
                }
                if (workOrderSelectDTO.getIsAssignment() && !isAssignment) {
                    continue;
                }
                list.add(workOrderEntityTemp);
            }
            //过滤工作中心生产基本单元类型
            if (workOrderSelectDTO.getWorkCenterType() != null) {
                WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntityTemp);
                if (workCenterEntity == null) {
                    throw new ResponseException("系统找不到" + workOrderEntityTemp.getWorkCenterName() + "工作中心");
                }
                if (!workCenterEntity.getType().equals(workOrderSelectDTO.getWorkCenterType())) {
                    continue;
                }
            }
        }
        for (WorkOrderEntity workOrderEntity : list) {
            WorkCenterEntity workCenterEntity = workCenterMapper.selectById(workOrderEntity.getWorkCenterId());
            //拿到工作中心type
            workOrderEntity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
            workOrderEntity.setWorkCenterRelevanceType(workCenterEntity == null ? null : workCenterEntity.getRelevanceType());
            //workOrderEntity.setCraftId(getCraftIdByWorkOrderNumber(workOrderEntity.getWorkOrderNumber()));
            workOrderEntity.setMaterialFields(materialService.getEntityByCodeAndSkuId(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId()));
        }
        return list;
    }

    /**
     * 查询生效、投产、挂起的工单，附带模糊查询物料编号、物料名称等条件
     *
     * @param workOrderSelectDTO
     * @return
     */
    @Override
    public List<WorkOrderEntity> getStateList(WorkOrderSelectDTO workOrderSelectDTO) {
        List<Integer> states = Arrays.asList(WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.RELEASED.getCode(), WorkOrderStateEnum.HANG_UP.getCode());
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        String workOrderNumber = workOrderSelectDTO.getWorkOrderNumber();
        wrapper.in(StringUtils.isBlank(workOrderSelectDTO.getState()), WorkOrderEntity::getState, states)
                .eq(StringUtils.isNotBlank(workOrderSelectDTO.getState()), WorkOrderEntity::getState, workOrderSelectDTO.getState())
                .eq(StringUtils.isNotBlank(workOrderSelectDTO.getLineId()), WorkOrderEntity::getLineId, workOrderSelectDTO.getLineId())
                .like(StringUtils.isNotBlank(workOrderNumber), WorkOrderEntity::getWorkOrderNumber, workOrderNumber);
        List<WorkOrderEntity> list = this.list(wrapper);
        //投产状态的工单排序优先，其次按照计划时间排序
        list.sort(Comparator.comparing(WorkOrderEntity::getStartDate));
        list.sort(Comparator.comparing(o -> states.indexOf(o.getState())));
        return list;
    }

    /**
     * 新增导入数据
     *
     * @param dto
     * @param userName
     */
    private void addImportDto(WorkOrderExcelDTO dto, String userName) {
        // 是否需要审批
        Boolean approveConfig = approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER.getCode());
        //用户信息
        SysUserEntity userEntity = userService.selectByUsername(userName);
        //获取物料当前库存
        Double currentInventory = delMaterialCurrentInventory(dto.getMaterialCode());
        String workOrderNumber = dto.getWorkOrderNumber();
        WorkOrderEntity workOrderEntity = WorkOrderEntity.builder()
                .workOrderName(workOrderNumber)
                .workOrderNumber(workOrderNumber)
                .materialCode(dto.getMaterialCode())
                .craftId(dto.getCraftId())
                .craftCode(dto.getCraftCode())
                .state(dto.getState())
                .assignmentState(dto.getAssignmentState())
                .startDate(dto.getStartDate())
                .endDate(dto.getEndDate())
                .priority(NORMAL)
                .type(ModelEnum.WORK_ORDER.getType())
                .orderType(dto.getOrderType())
                .customerCode(dto.getCustomerCode())
                .customerName(dto.getCustomerName())
                .customerMaterialCode(dto.getCustomerMaterialCode())
                .customerMaterialName(dto.getCustomerMaterialName())
                .customerSpecification(dto.getCustomerSpecification())
                .packageSchemeCode(dto.getPackageSchemeCode())
                .plannedBatches(dto.getPlannedBatches())
                .plansPerBatch(dto.getPlansPerBatch())
                .planQuantity((double) dto.getPlanQuantity())
                .pendentQuantity((double) dto.getPlanQuantity())
                .productCount(0.0)
                .finishCount(0.0)
                .progress(0.0)
                .currentInventory(currentInventory)
                .approvalStatus(approveConfig ? (dto.getState() > WorkOrderStateEnum.CREATED.getCode() ? ApprovalStatusEnum.APPROVED.getCode() : ApprovalStatusEnum.TO_BE_SUBMIT.getCode()) : null)
                .approver(userEntity.getUsername())
                .magName(userEntity.getUsername())
                .magNickname(userEntity.getNickname())
                .magPhone(userEntity.getMobile())
                .workCenterId(dto.getWorkCenterId())
                .workCenterName(dto.getWorkCenterName())
//                .teamId(dto.getTeamId())
//                .deviceId(dto.getDeviceId())
                .productBasicUnits(JacksonUtil.convertArray(dto.getProductBasicUnits(), WorkOrderBasicUnitRelationEntity.class))
                .remark(dto.getRemark())
                .createBy(userName)
                .updateBy(userName)
                .updateDate(dto.getCreateDate())
                .createDate(Objects.nonNull(dto.getCreateDate()) ? dto.getCreateDate() : new Date())
                .importTime(dto.getCreateDate())
                .relevanceDeviceIds(dto.getRelevanceDeviceIds())
                .relevanceTeamIds(dto.getRelevanceTeamIds())
                .relatedSaleOrderMaterialLineNumber(dto.getRelatedSaleOrderMaterialLineNumber())
                .relatedProductOrderMaterialLineNumber(dto.getRelatedProductOrderMaterialLineNumber())
                .workOrderExtendFieldOne(dto.getWorkOrderExtendFieldOne())
                .workOrderExtendFieldTwo(dto.getWorkOrderExtendFieldTwo())
                .workOrderExtendFieldThree(dto.getWorkOrderExtendFieldThree())
                .workOrderExtendFieldFour(dto.getWorkOrderExtendFieldFour())
                .workOrderExtendFieldFive(dto.getWorkOrderExtendFieldFive())
                .workOrderExtendFieldSix(dto.getWorkOrderExtendFieldSix())
                .workOrderExtendFieldSeven(dto.getWorkOrderExtendFieldSeven())
                .workOrderExtendFieldEight(dto.getWorkOrderExtendFieldEight())
                .workOrderExtendFieldNine(dto.getWorkOrderExtendFieldNine())
                .workOrderExtendFieldTen(dto.getWorkOrderExtendFieldTen())
                .workOrderMaterialExtendFieldOne(dto.getWorkOrderMaterialExtendFieldOne())
                .workOrderMaterialExtendFieldTwo(dto.getWorkOrderMaterialExtendFieldTwo())
                .workOrderMaterialExtendFieldThree(dto.getWorkOrderMaterialExtendFieldThree())
                .workOrderMaterialExtendFieldFour(dto.getWorkOrderMaterialExtendFieldFour())
                .workOrderMaterialExtendFieldFive(dto.getWorkOrderMaterialExtendFieldFive())
                .workOrderMaterialExtendFieldSix(dto.getWorkOrderMaterialExtendFieldSix())
                .workOrderMaterialExtendFieldSeven(dto.getWorkOrderMaterialExtendFieldSeven())
                .workOrderMaterialExtendFieldEight(dto.getWorkOrderMaterialExtendFieldEight())
                .workOrderMaterialExtendFieldNine(dto.getWorkOrderMaterialExtendFieldNine())
                .workOrderMaterialExtendFieldTen(dto.getWorkOrderMaterialExtendFieldTen())
                .businessUnitCode(dto.getBusinessUnitCode())
                .businessUnitName(dto.getBusinessUnitName())
                .build();
        // 获取工艺工序对象，绑定制造单元类型及工艺工序
        List<CraftProcedureEntity> craftProcedureEntities = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getProcedureName())) {
            craftProcedureEntities = dto.getRelateCraftProcedures();
        }
        workOrderEntity.setCraftProcedureEntities(craftProcedureEntities);

//        String lineName = dto.getLineName();
//        if (StringUtils.isNotBlank(lineName)) {
//            // 产线只能导入1个
//            String[] lineNames = lineName.split((Constants.SEP));
//            ProductionLineEntity lineEntity = getProductionLineByName(lineNames[0]);
//            workOrderEntity.setLineId(lineEntity.getProductionLineId());
//            workOrderEntity.setLineCode(lineEntity.getProductionLineCode());
//            workOrderEntity.setLineName(lineEntity.getName());
//            workOrderEntity.setMaterialCheckType(lineEntity.getMaterialCheckType());
//        }
        // 设置生产基本单元id和隔离ID
        setProductionBasicUnitIdAndIsolationId(workOrderEntity);
        //关联订单
        String relatedOrder = dto.getRelatedOrder();
        String orderType = OrderNumTypeEnum.getCodeByName(dto.getRelatedOrderType());
        if (StringUtils.isNoneBlank(dto.getRelatedOrderType(), dto.getRelatedOrder())) {
            if (OrderNumTypeEnum.SALE_ORDER.getTypeCode().equals(orderType)) {
                SaleOrderEntity saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(relatedOrder).build());
                workOrderEntity.setSaleOrderList(Stream.of(saleOrderEntity).collect(Collectors.toList()));
                workOrderEntity.setSaleOrderNumber(relatedOrder);
                // 根据关联销售订单的行号及销售订单，获取关联物料行id，方便后续数据回填
                SaleOrderMaterialEntity saleOrderMaterialEntity = saleOrderEntity.getSaleOrderMaterials().stream().filter(o -> o.getLineNumber().equals(dto.getRelatedSaleOrderMaterialLineNumber())).findFirst().get();
                workOrderEntity.setRelateOrderMaterialId(saleOrderMaterialEntity.getId());
                workOrderEntity.setRelatedSaleOrderMaterialLineNumber(saleOrderMaterialEntity.getLineNumber());
            }
            // 如果为生产订单，且生产订单关联销售订单物料行id，则找到对应关联的销售订单物料行号，插入到订单工单关联表里
            if (OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode().equals(orderType)) {
                ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(relatedOrder).build());
                workOrderEntity.setProductOrderList(Stream.of(productOrderEntity).collect(Collectors.toList()));
                workOrderEntity.setProductOrderNumber(relatedOrder);
                workOrderEntity.setRelatedProductOrderMaterialLineNumber(1);
                // 关联销售订单物料行id、物料行号
                Integer relateSaleOrderMaterialId = productOrderEntity.getProductOrderMaterial().getRelateSaleOrderMaterialId();
                if (Objects.nonNull(relateSaleOrderMaterialId)) {
                    PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderMaterialIds(Stream.of(relateSaleOrderMaterialId).collect(Collectors.toList())).build());
                    List<SaleOrderEntity> saleOrderEntities = JacksonUtil.convertArray(pageResult.getRecords(), SaleOrderEntity.class);
                    workOrderEntity.setRelatedSaleOrderMaterialLineNumber(ObjectUtils.isEmpty(dto.getRelatedSaleOrderMaterialLineNumber()) ? saleOrderEntities.get(0).getSaleOrderMaterial().getLineNumber() : dto.getRelatedSaleOrderMaterialLineNumber());
                    workOrderEntity.setRelateOrderMaterialId(relateSaleOrderMaterialId);
                }
                //生产工单导入，字段若存在为空，则从生产订单获取：包装方案、客户名称、客户编码、计划批数每批计划数
                workOrderEntity.setCustomerCode(StringUtils.isNotBlank(dto.getCustomerCode()) ? dto.getCustomerCode() : productOrderEntity.getCustomerCode());
                workOrderEntity.setCustomerName(StringUtils.isNotBlank(dto.getCustomerName()) ? dto.getCustomerName() : productOrderEntity.getCustomerName());
                workOrderEntity.setPackageSchemeCode(StringUtils.isNotBlank(dto.getPackageSchemeCode()) ? dto.getPackageSchemeCode() : productOrderEntity.getProductOrderMaterial().getPackageSchemeCode());
                workOrderEntity.setPlannedBatches(Objects.nonNull(dto.getPlannedBatches()) ? dto.getPlannedBatches() : productOrderEntity.getProductOrderMaterial().getPlannedBatches());
                workOrderEntity.setPlansPerBatch(Objects.nonNull(dto.getPlansPerBatch()) ? dto.getPlansPerBatch() : productOrderEntity.getProductOrderMaterial().getPlansPerBatch());
            }
        }
        // 计算计划工时
        workOrderEntity.setPlannedWorkingHours(calculatePlannedWorkHours(workOrderEntity));
        UnitUtil.formatObj(workOrderEntity);
        this.save(workOrderEntity);
        //关联订单
        saveOrderRelation(workOrderEntity);
        //创建工单计划
//        workOrderPlanService.createWorkOrderPlan(workOrderEntity);
        // 插入到工单工序关联表
        workOrderProcedureRelationService.insertWorkOrderProcedureRelation(workOrderEntity);
        // 更新到工单-生产基本单元关联表
        WorkOrderBasicUnitInsertDTO basicUnitInsertDTO = WorkOrderBasicUnitInsertDTO.builder()
                .workOrderNumber(workOrderEntity.getWorkOrderNumber())
                .productBasicUnits(JacksonUtil.convertArray(workOrderEntity.getProductBasicUnits(), WorkOrderBasicUnitRelationInsertDTO.class))
                .build();
        basicUnitRelationService.batchSaveWorkOrderBasicUnits(Collections.singletonList(basicUnitInsertDTO));
        //保存工单关联资源
        saveRelevanceResource(workOrderEntity);
        // 自动创建流水码
        this.autoGenerate(Stream.of(workOrderEntity).collect(Collectors.toList()));
        // 消息发送
        workOrderService.dealAfterAdd(workOrderEntity);
    }

    /**
     * 更新已存在的导入数据
     *
     * @param dto
     * @param entity
     * @param userName
     */
    private void updateImportDto(WorkOrderExcelDTO dto, WorkOrderEntity entity, String userName) {
        // 是否需要审批
        Boolean approveConfig = approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER.getCode());
        //用户信息
        SysUserEntity userEntity = userService.selectByUsername(userName);
        entity.setMaterialCode(dto.getMaterialCode());
        entity.setCraftId(dto.getCraftId());
        entity.setCraftCode(dto.getCraftCode());
        entity.setState(dto.getState());
        entity.setAssignmentState(dto.getAssignmentState());
        entity.setStartDate(dto.getStartDate());
        entity.setEndDate(dto.getEndDate());
        entity.setPriority(NORMAL);
        entity.setType(ModelEnum.WORK_ORDER.getType());
        entity.setCustomerCode(dto.getCustomerCode());
        entity.setCustomerName(dto.getCustomerName());
        entity.setPackageSchemeCode(dto.getPackageSchemeCode());
        entity.setPlannedBatches(dto.getPlannedBatches());
        entity.setPlansPerBatch(dto.getPlansPerBatch());
        entity.setOrderType(dto.getOrderType());
        entity.setPlanQuantity((double) dto.getPlanQuantity());
        entity.setApprovalStatus(approveConfig ? (dto.getState() > WorkOrderStateEnum.CREATED.getCode() ? ApprovalStatusEnum.APPROVED.getCode() : ApprovalStatusEnum.TO_BE_SUBMIT.getCode()) : null);
        entity.setApprover(userEntity.getUsername());
        entity.setMagName(userEntity.getUsername());
        entity.setMagNickname(userEntity.getNickname());
        entity.setMagPhone(userEntity.getMobile());
        entity.setWorkCenterId(dto.getWorkCenterId());
        entity.setWorkCenterName(dto.getWorkCenterName());
        entity.setRemark(dto.getRemark());
        entity.setUpdateBy(userName);
        entity.setUpdateDate(new Date());
        //导入时间需要是当前的操作时间
        entity.setImportTime(new Date());
//        entity.setTeamId(dto.getTeamId());
        entity.setRelevanceDeviceIds(dto.getRelevanceDeviceIds());
        entity.setRelevanceTeamIds(dto.getRelevanceTeamIds());
        entity.setRelatedSaleOrderMaterialLineNumber(dto.getRelatedSaleOrderMaterialLineNumber());
        entity.setRelatedProductOrderMaterialLineNumber(dto.getRelatedProductOrderMaterialLineNumber());
        entity.setWorkOrderExtendFieldOne(entity.getWorkOrderExtendFieldOne());
        entity.setWorkOrderExtendFieldTwo(entity.getWorkOrderExtendFieldTwo());
        entity.setWorkOrderExtendFieldThree(entity.getWorkOrderExtendFieldThree());
        entity.setWorkOrderExtendFieldFour(entity.getWorkOrderExtendFieldFour());
        entity.setWorkOrderExtendFieldFive(entity.getWorkOrderExtendFieldFive());
        entity.setWorkOrderExtendFieldSix(entity.getWorkOrderExtendFieldSix());
        entity.setWorkOrderExtendFieldSeven(entity.getWorkOrderExtendFieldSeven());
        entity.setWorkOrderExtendFieldEight(entity.getWorkOrderExtendFieldEight());
        entity.setWorkOrderExtendFieldNine(entity.getWorkOrderExtendFieldNine());
        entity.setWorkOrderExtendFieldTen(entity.getWorkOrderExtendFieldTen());
        entity.setWorkOrderMaterialExtendFieldOne(entity.getWorkOrderMaterialExtendFieldOne());
        entity.setWorkOrderMaterialExtendFieldTwo(entity.getWorkOrderMaterialExtendFieldTwo());
        entity.setWorkOrderMaterialExtendFieldThree(entity.getWorkOrderMaterialExtendFieldThree());
        entity.setWorkOrderMaterialExtendFieldFour(entity.getWorkOrderMaterialExtendFieldFour());
        entity.setWorkOrderMaterialExtendFieldFive(entity.getWorkOrderMaterialExtendFieldFive());
        entity.setWorkOrderMaterialExtendFieldSix(entity.getWorkOrderMaterialExtendFieldSix());
        entity.setWorkOrderMaterialExtendFieldSeven(entity.getWorkOrderMaterialExtendFieldSeven());
        entity.setWorkOrderMaterialExtendFieldEight(entity.getWorkOrderMaterialExtendFieldEight());
        entity.setWorkOrderMaterialExtendFieldNine(entity.getWorkOrderMaterialExtendFieldNine());
        entity.setWorkOrderMaterialExtendFieldTen(entity.getWorkOrderMaterialExtendFieldTen());
        entity.setBusinessUnitCode(dto.getBusinessUnitCode());
        entity.setBusinessUnitName(dto.getBusinessUnitName());

        //制造单元id只有在校验制造单元类型的工作中心时才会set,不为空即工作中心类型为制造单元,需要处理制造单元字段
//        if (dto.getLineId() != null) {
//            entity.setLineId(dto.getLineId());
//            entity.setLineCode(dto.getLineCode());
//            entity.setLineName(dto.getLineName());
//        }
        //关联订单
        String relatedOrder = dto.getRelatedOrder();
        String orderType = OrderNumTypeEnum.getCodeByName(dto.getRelatedOrderType());
        if (StringUtils.isNoneBlank(dto.getRelatedOrderType(), dto.getRelatedOrder())) {
            if (OrderNumTypeEnum.SALE_ORDER.getTypeCode().equals(orderType)) {
                SaleOrderEntity saleOrderEntity = extSaleOrderInterface.selectSaleOrderByNumber(SaleOrderDetailDTO.builder().saleOrderNumber(relatedOrder).build());
                entity.setSaleOrderList(Stream.of(saleOrderEntity).collect(Collectors.toList()));
                entity.setSaleOrderNumber(relatedOrder);
                // 根据关联销售订单的行号及销售订单，获取关联物料行id，方便后续数据回填
                SaleOrderMaterialEntity saleOrderMaterialEntity = saleOrderEntity.getSaleOrderMaterials().stream().filter(o -> o.getLineNumber().equals(dto.getRelatedSaleOrderMaterialLineNumber())).findFirst().get();
                entity.setRelateOrderMaterialId(saleOrderMaterialEntity.getId());
            }
            // 如果为生产订单，且生产订单关联销售订单物料行id，则找到对应关联的销售订单物料行号，插入到订单工单关联表里
            if (OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode().equals(orderType)) {
                ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(relatedOrder).build());
                entity.setProductOrderList(Stream.of(productOrderEntity).collect(Collectors.toList()));
                entity.setProductOrderNumber(relatedOrder);
                // 关联销售订单物料行id、物料行号
                Integer relateSaleOrderMaterialId = productOrderEntity.getProductOrderMaterial().getRelateSaleOrderMaterialId();
                PageResult<SaleOrderVO> pageResult = extSaleOrderInterface.getPage(SaleOrderSelectOpenDTO.builder().showType(ShowTypeEnum.MATERIAL.getType()).saleOrderMaterialIds(Stream.of(relateSaleOrderMaterialId).collect(Collectors.toList())).build());
                List<SaleOrderEntity> saleOrderEntities = JacksonUtil.convertArray(pageResult.getRecords(), SaleOrderEntity.class);

                entity.setRelatedSaleOrderMaterialLineNumber(ObjectUtils.isEmpty(dto.getRelatedSaleOrderMaterialLineNumber()) ? saleOrderEntities.get(0).getSaleOrderMaterial().getLineNumber() : dto.getRelatedSaleOrderMaterialLineNumber());
                entity.setRelateOrderMaterialId(relateSaleOrderMaterialId);
                //生产工单导入，字段若存在为空，则从生产订单获取：包装方案、客户名称、客户编码、计划批数每批计划数
                entity.setCustomerCode(StringUtils.isNotBlank(dto.getCustomerCode()) ? dto.getCustomerCode() : productOrderEntity.getCustomerCode());
                entity.setCustomerName(StringUtils.isNotBlank(dto.getCustomerName()) ? dto.getCustomerName() : productOrderEntity.getCustomerName());
                entity.setPackageSchemeCode(StringUtils.isNotBlank(dto.getPackageSchemeCode()) ? dto.getPackageSchemeCode() : productOrderEntity.getProductOrderMaterial().getPackageSchemeCode());
                entity.setPlannedBatches(Objects.nonNull(dto.getPlannedBatches()) ? dto.getPlannedBatches() : productOrderEntity.getProductOrderMaterial().getPlannedBatches());
                entity.setPlansPerBatch(Objects.nonNull(dto.getPlansPerBatch()) ? dto.getPlansPerBatch() : productOrderEntity.getProductOrderMaterial().getPlansPerBatch());
            }
        }
        // 工单工序关联表
        if (StringUtils.isNotEmpty(dto.getProcedureName())) {
            entity.setCraftProcedureEntities(dto.getRelateCraftProcedures());
        }
        entity.setProductBasicUnits(JacksonUtil.convertArray(dto.getProductBasicUnits(), WorkOrderBasicUnitRelationEntity.class));
        UnitUtil.formatObj(entity);
        workOrderService.updateByWorkId(entity, userName);
        //根据生产工单查询对应的销售订单
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(entity.getWorkOrderNumber());
        entity = this.getWorkOrderById(detailDTO);
        // 推送给ams，计算ams的相关数量
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, AmsEventTypeEnum.BACK_FILL_QUANTITY);
    }

    /**
     * 通过名称查询制造单元
     *
     * @param lineName
     * @return
     */
    private ProductionLineEntity getProductionLineByName(String lineName) {
        LambdaQueryWrapper<ProductionLineEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProductionLineEntity::getProductionLineId, ProductionLineEntity::getName,
                        ProductionLineEntity::getProductionLineCode)
                .eq(ProductionLineEntity::getName, lineName).last(Constant.LIMIT_ONE);
        return productionLineMapper.selectOne(wrapper);
    }

    @Async
    @Override
    public void sendWebSocket(WorkOrderEntity workOrderEntity) {
        // 发送websocket消息到前端
        String topic = TopicEnum.WORK_ORDER_COUNT_TOPIC.getTopic().replace("+", workOrderEntity.getWorkOrderNumber());
        FacCountVo facCountVo = FacCountVo.builder()
                .totalQuantity(workOrderEntity.getPlanQuantity())
                .finishQuantity(workOrderEntity.getFinishCount())
                .unqualified(workOrderEntity.getUnqualified())
                .inputQuantity(workOrderEntity.getInputTotal())
                .build();
        kafkaWebSocketPublisher.sendMessage(topic, MessageContent.builder()
                .time(new Date())
                .message(facCountVo)
                .build());
        //推送消息
        messagePushToKafkaService.pushNewMessage(workOrderEntity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_COUNT_CHANGE_MESSAGE
        );

    }

    @Override
    public List<WorkOrderEntity> listWorkOrderByLineIdForFeed(Integer productionLineId, Integer state) {
        List<Integer> states;
        if (state == null) {
            // 默认生效、投产、挂起状态
            states = Arrays.asList(
                    WorkOrderStateEnum.RELEASED.getCode(),
                    WorkOrderStateEnum.INVESTMENT.getCode(),
                    WorkOrderStateEnum.HANG_UP.getCode()
            );
        } else {
            states = Collections.singletonList(state);
        }

        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(WorkOrderEntity::getLineId, productionLineId)
                .in(WorkOrderEntity::getState, states)
                .orderByDesc(WorkOrderEntity::getState)
                .orderByDesc(WorkOrderEntity::getCreateDate);
        List<WorkOrderEntity> workOrderEntities = this.list(wrapper);
        workOrderEntities.forEach(workOrderEntity -> workOrderEntity
                .setMaterialFields(materialService.getEntityByCodeAndSkuId(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId())));
        return workOrderEntities;
    }


    @Override
    public List<CraftFileNewDTO> getAllFiles(String workOrderNumber, String name, String type, Integer procedureId) {
        List<CraftFileNewDTO> craftFileDTOList = new ArrayList<>();
        if (StringUtils.isBlank(name)) {
            craftFileDTOList = selectAllCraftFile(workOrderNumber, procedureId);
        }
        if (StringUtils.isNotBlank(name)) {
            //模糊查询名称
            craftFileDTOList = searchName(name, selectAllCraftFile(workOrderNumber, procedureId));
        }
        if (StringUtils.isNotBlank(type)) {
            //查询类型
            craftFileDTOList = searchType(type, selectAllCraftFile(workOrderNumber, procedureId));
        }
        return craftFileDTOList;
    }

    @Override
    public WorkOrderFacDTO selectWorkOrderFac(String workOrderNumber, Integer facId, Integer craftProcedureId) {
        if (StringUtils.isBlank(workOrderNumber) || facId == null) {
            return null;
        }
        WorkOrderEntity workOrderEntity = getSimpleWorkOrderByNumber(workOrderNumber);
        WorkOrderFacDTO workOrderFacDTO = JacksonUtil.parseObject(JSON.toJSONString(workOrderEntity), WorkOrderFacDTO.class);

        //展示工单附件
        if (workOrderFacDTO != null) {
            //根据工位id获取工位信息
            FacilitiesEntity facilitiesEntity = facilitiesService.getById(facId);
            if (facilitiesEntity != null) {
                workOrderFacDTO.setFacId(facilitiesEntity.getFid());
                workOrderFacDTO.setFacName(facilitiesEntity.getFname());
                workOrderFacDTO.setOpertorName(facilitiesEntity.getMagNickname());
                //查询该工位id绑定的设备
                LambdaQueryWrapper<DeviceEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(DeviceEntity::getFid, facilitiesEntity.getFid()).last(Constant.LIMIT_ONE);
                DeviceEntity deviceEntity = deviceService.getOne(wrapper);
                if (deviceEntity != null) {
                    workOrderFacDTO.setDeviceId(deviceEntity.getDeviceId());
                    workOrderFacDTO.setDeviceName(deviceEntity.getDeviceName());
                }
            }
            List<WorkOrderFileEntity> fileEntityList = workOrderFileService.getEntityByWorkOrderId(workOrderFacDTO.getWorkOrderId());
            workOrderFacDTO.setFile(fileEntityList);
            //拿到工单该工序过站数
            workOrderFacDTO.setPassCount(getPassCount(workOrderNumber, craftProcedureId));
            //维修数量
            LambdaQueryWrapper<ProductFlowCodeRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber).eq(ProductFlowCodeRecordEntity::getFacId, facId)
                    .eq(ProductFlowCodeRecordEntity::getIsMaintain, true);
            Long maintainCount = productFlowCodeRecordMapper.selectCount(lambdaQueryWrapper);
            workOrderFacDTO.setMaintainCount(maintainCount.intValue());
            workOrderFacDTO.setMaterialFields(materialService.getEntityByCodeAndSkuId(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId()));
        }
        return workOrderFacDTO;
    }

    @Override
    public Integer getPassCount(String workOrderNumber, Integer craftProcedureId) {
        //拿到工单该工序过站数
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> productFlowCodeRecordWrapper = new LambdaQueryWrapper<>();
        productFlowCodeRecordWrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber);
        productFlowCodeRecordWrapper.eq(ProductFlowCodeRecordEntity::getState, ProductFlowCodeRecordStateEnum.COMPLETE.getCode());
        //不传工序找工位过站数
        WrapperUtil.eq(productFlowCodeRecordWrapper, ProductFlowCodeRecordEntity::getProdureId, craftProcedureId);
        Long count = productFlowCodeRecordMapper.selectCount(productFlowCodeRecordWrapper);
        return count.intValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void batchUpdateWorkOrder(BatchUpdateWorkOrderDTO dto, String username, String code) {
        List<String> orderNumbers = dto.getList().stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        String progressKey = RedisKeyPrefix.WORK_ORDER_BATCH_EDIT_TASK + code;
        // 初始化进度，获取锁（给单加锁,同一个单，在操作没有完成之前，不能多次操作）
        // 对用户也要加锁（如果不加，后续取缓存的时候有问题）
        commonService.initLockProgress(progressKey, orderNumbers, BATCH_UPDATE_WORK_ORDER_LOCK, 30, TimeUnit.MINUTES, username);
        // 加锁
        // 自动生成流水码列表
        List<WorkOrderEntity> autoGenerateList = new ArrayList<>();
        try {
            // 开始执行批量操作
            Integer state = dto.getState();
            String assignmentState = dto.getAssignmentState();
            List<WorkOrderEntity> list = dto.getList();
            boolean emptyBasicUnit = CollectionUtils.isEmpty(dto.getProductBasicUnits());
            int rowTotal = list.size();
            for (int i = 0; i < list.size(); i++) {
                WorkOrderEntity workOrderEntity = list.get(i);
                if (WorkOrderStateEnum.CANCELED.getCode().equals(workOrderEntity.getState())) {
                    throw new ResponseException("取消的工单无法编辑");
                }
                // 如果审批状态开启，不能直接从创建 --> 非 (创建 | 取消)
                if (WorkOrderStateEnum.CREATED.getCode().equals(workOrderEntity.getState()) &&
                        !(WorkOrderStateEnum.CREATED.getCode().equals(state) || WorkOrderStateEnum.CANCELED.getCode().equals(state)) &&
                        approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER.getCode())) {
                    throw new ResponseException("已开启审批功能，需审批才能变更为该状态");
                }
                // 如果批量编辑未填写基本生产单元，则取自己原绑定的生产基本单元
                if (emptyBasicUnit) {
                    List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = basicUnitRelationService.lambdaQuery().eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber()).list();
                    dto.setProductBasicUnits(JacksonUtil.convertArray(basicUnitRelationEntities, WorkOrderBasicUnitRelationInsertDTO.class));
                }
                // 判断字段合法性
                judgeFieldLegality(state, assignmentState, workOrderEntity, dto.getProductBasicUnits());

                WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderEntity.getWorkOrderNumber());
                WorkOrderEntity originWorkOrder = getWorkOrderById(detailDTO);
                BeanUtil.copyProperties(originWorkOrder, workOrderEntity);
                // 更新单据状态
                workOrderEntity.setState(state);
                if (StringUtils.isNotEmpty(dto.getAssignmentState())) {
                    workOrderEntity.setAssignmentState(dto.getAssignmentState());
                }
                if (StringUtils.isNotEmpty(dto.getPickingStateName())) {
                    workOrderEntity.setPickingStateName(dto.getPickingStateName());
                }
                Date now = new Date();
                // 设置实际完成时间
                if (state.equals(WorkOrderStateEnum.FINISHED.getCode())) {
                    workOrderEntity.setActualEndDate(now);
                }
                workOrderEntity.setIsAutoReport(dto.getIsAutoReport());
                workOrderEntity.setUpdateBy(username);
                workOrderEntity.setUpdateDate(now);
                workOrderEntity.setApprover(StringUtils.isNotBlank(workOrderEntity.getApprover()) ? workOrderEntity.getApprover() : username);
                // 计划数量
                if (dto.getPlanQuantity() != null) {
                    if (dto.getPlanQuantity() < 0) {
                        throw new ResponseException("计划数量需不可以小于0");
                    }
                    workOrderEntity.setPlanQuantity(dto.getPlanQuantity());
                }
                // 计划时间
                if (dto.getStartDate() != null) {
                    Date endDate = dto.getEndDate() != null ? dto.getEndDate() : workOrderEntity.getEndDate();
                    if (endDate != null && endDate.before(dto.getStartDate())) {
                        throw new ResponseException("工单:" + workOrderEntity.getWorkOrderNumber() + " 结束时间需大于开始时间");
                    }
                    workOrderEntity.setStartDate(dto.getStartDate());
                }
                if (dto.getEndDate() != null) {
                    Date startDate = dto.getStartDate() != null ? dto.getStartDate() : workOrderEntity.getStartDate();
                    if (startDate != null && startDate.after(dto.getEndDate())) {
                        throw new ResponseException("工单:" + workOrderEntity.getWorkOrderNumber() + " 开始时间需小于结束时间");
                    }
                    workOrderEntity.setEndDate(dto.getEndDate());
                }
                workOrderEntity.setInvestCheckResult(dto.getInvestCheckResult());
                workOrderEntity.setProductBasicUnits(JacksonUtil.convertArray(dto.getProductBasicUnits(), WorkOrderBasicUnitRelationEntity.class));
                // 更新工单
                WorkOrderSmartUpdateDTO updateDTO = WorkOrderSmartUpdateDTO.common(workOrderEntity, username);
                updateDTO.setIsAutoGenerate(false);
                workOrderService.updateByWorkId(updateDTO);
                autoGenerateList.add(workOrderEntity);
                // 处理进度
                Double processPercent = MathUtil.divideDouble(i + 1, rowTotal, 2);
                commonService.updateProgress(progressKey, processPercent, null);
            }
        } catch (Exception e) {
            commonService.importProgressException(progressKey, e);
            log.warn("工单批量编辑过程中出错", e);
        } finally {
            // 自动创建流水码
            this.autoGenerate(autoGenerateList);
            // 删除锁
            commonService.releaseLock(orderNumbers, BATCH_UPDATE_WORK_ORDER_LOCK, username);
        }
    }
//
//    private String checkAndGetProductBaseName(BatchUpdateWorkOrderDTO dto, List<WorkOrderEntity> list) {
//        String productBaseName = null;
//        Integer productionBasicUnitId = dto.getProductionBasicUnitId();
//        if (productionBasicUnitId != null) {
//            WorkCenterTypeEnum workCenterTypeEnum = WorkCenterTypeEnum.getByCode(dto.getWorkCenterType());
//            String noWorkCenterOrder = list.stream().filter(e -> e.getWorkCenterId() == null).map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.joining(Constant.SEP));
//            if (StringUtils.isNotEmpty(noWorkCenterOrder)) {
//                throw new ResponseException("存在工单的工作中心为空:" + noWorkCenterOrder);
//            }
//            Map<Integer, List<WorkOrderEntity>> workCenterIdGroup = list.stream().collect(Collectors.groupingBy(WorkOrderEntity::getWorkCenterId));
//            if (workCenterIdGroup.size() != 1) {
//                throw new ResponseException("修改基本生产单元时, 所有工单的工作中心需保持一致");
//            }
//            Integer workCenterId = workCenterIdGroup.keySet().stream().findAny().orElse(null);
//            WorkCenterEntity workCenter = workCenterService.getById(workCenterId);
//            if (workCenter == null) {
//                throw new ResponseException("未找到工作中心,id:" + workCenterId);
//            }
//            switch (workCenterTypeEnum) {
//                case TEAM:
//                    SysTeamEntity team = sysTeamService.getById(productionBasicUnitId);
//                    if (team == null) {
//                        throw new ResponseException("未找到班组,id:" + productionBasicUnitId);
//                    }
//                    break;
//                case DEVICE:
//                    DeviceEntity device = deviceService.getById(productionBasicUnitId);
//                    if (device == null) {
//                        throw new ResponseException("未找到设备,id:" + productionBasicUnitId);
//                    }
//                    break;
//                case LINE:
//                    ProductionLineEntity line = productionLineMapper.selectById(productionBasicUnitId);
//                    if (line == null) {
//                        throw new ResponseException("未找到制造单元,id:" + productionBasicUnitId);
//                    }
//                    productBaseName = line.getName();
//                    break;
//            }
//        }
//        return productBaseName;
//    }

    /**
     * 判断字段合法性(批量修改工单状态)
     */
    private void judgeFieldLegality(Integer state, String assignmentState, WorkOrderEntity workOrderEntity, List<WorkOrderBasicUnitRelationInsertDTO> productBasicUnitList) {
        if (state == null) {
            return;
        }
        // 创建 --> 生效
        if (state.equals(WorkOrderStateEnum.RELEASED.getCode()) && workOrderEntity.getState().equals(WorkOrderStateEnum.CREATED.getCode())) {
            // 校验字段是否为空
            if (workOrderEntity.getStartDate() == null) {
                throw new ResponseException("工单号:" + workOrderEntity.getWorkOrderNumber() + "计划开始时间为空");
            }
            if (workOrderEntity.getEndDate() == null) {
                throw new ResponseException("工单号:" + workOrderEntity.getWorkOrderNumber() + "计划结束时间为空");
            }
            if (workOrderEntity.getWorkCenterId() == null) {
                throw new ResponseException("工单号:" + workOrderEntity.getWorkOrderNumber() + "关联工作中心为空");
            }
            // 获取派工的业务配置
            FullPathCodeDTO dto = FullPathCodeDTO.builder()
                    .fullPathCode(ConfigConstant.WORK_ORDER_ASSIGNMENT_CONFIG).build();
            WorkOrderAssignmentConfigDTO config = businessConfigService.getValueDto(dto, WorkOrderAssignmentConfigDTO.class);
            if (config.getBasicProdUnitIsRequired() && CollectionUtils.isEmpty(productBasicUnitList) && assignmentState.equals(AssignmentStateEnum.ASSIGNED.getType())) {
                throw new ResponseException("由于配置为派工时基本生产单元必填，故工单号:" + workOrderEntity.getWorkOrderNumber() + "信息填写有误，请检查！");
            }
        }
        // 生效 --> 投产
        if (state.equals(WorkOrderStateEnum.INVESTMENT.getCode()) && workOrderEntity.getState().equals(WorkOrderStateEnum.RELEASED.getCode())) {
            // 实际开始时间为空则赋值当前时间
            if (workOrderEntity.getActualStartDate() == null) {
                workOrderEntity.setActualStartDate(new Date());
            }
            if (CollectionUtils.isEmpty(productBasicUnitList)) {
                // 如果批量编辑未填写基本生产单元，则取自己原绑定的生产基本单元
                throw new ResponseException("工单号:" + workOrderEntity.getWorkOrderNumber() + "未选基本生产单元，请检查！");
            }
        }
        // 完成
        if (state.equals(WorkOrderStateEnum.FINISHED.getCode())) {
            // 校验字段是否为空
            if (workOrderEntity.getFinishCount() == null) {
                throw new ResponseException("工单号:" + workOrderEntity.getWorkOrderNumber() + "完成数量为空");
            }
            // 实际完成时间为空则赋值当前时间
            if (workOrderEntity.getActualEndDate() == null) {
                workOrderEntity.setActualEndDate(new Date());
            }
        }
    }

    @Override
    public WorkOrderEntity updateLineById(WorkOrderEntity entity, String username) {
        WorkOrderEntity orderEntity = this.getById(entity.getWorkOrderId());
        entity.setUpdateBy(username);
        entity.setUpdateDate(new Date());
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(orderEntity.getWorkOrderNumber());
        WorkOrderEntity workOrderEntity = this.getWorkOrderById(detailDTO);
        if (workOrderEntity != null) {
            double plannedWorkHours = calculatePlannedWorkHours(workOrderEntity);
            workOrderEntity.setPlannedWorkingHours(plannedWorkHours);
            this.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                    .set(WorkOrderEntity::getPlannedWorkingHours, plannedWorkHours)
                    .update();
            //更新制造单元工单关联关系
            workOrderEntity.setLineId(entity.getLineId());
            workOrderEntity.setDeviceId(entity.getDeviceId());
            workOrderEntity.setTeamId(entity.getTeamId());
            updateProductLineRelation(workOrderEntity);
        }
        return workOrderEntity;
    }

    /**
     * 增加工单与制造单元的关系到关系表
     *
     * @param entity
     */
    @Override
    public void updateProductLineRelation(WorkOrderEntity entity) {
        if (Objects.isNull(entity)) {
            return;
        }
        List<WorkOrderBasicUnitRelationEntity> productBasicUnits = entity.getProductBasicUnits();
        removeProductLineRation(entity.getWorkOrderId());
        if (CollectionUtils.isNotEmpty(productBasicUnits) && entity.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode())) {
            List<WorkOrderProductLineRelationEntity> lineRelationEntities = productBasicUnits.stream().map(relationEntity ->
                            WorkOrderProductLineRelationEntity.builder()
                                    .workOrderId(entity.getWorkOrderId())
                                    .productLineId(relationEntity.getProductionBasicUnitId())
                                    .build())
                    .collect(Collectors.toList());
            workOrderProductLineRelationService.saveBatch(lineRelationEntities);
        }
    }

    @Override
    public void updateProductLineRelation(Integer operationId, Integer lineId, String workOrderNum) {
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNum);
        QueryWrapper<WorkOrderProductLineRelationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WorkOrderProductLineRelationEntity::getWorkOrderId, workOrderEntity.getWorkOrderId())
                .eq(WorkOrderProductLineRelationEntity::getOperationOrderId, operationId);
        workOrderProductLineRelationService.remove(queryWrapper);
        List<WorkOrderBasicUnitRelationEntity> relationEntities = basicUnitRelationService.getByWorkOrderNumber(workOrderEntity.getWorkOrderNumber());
        if (CollectionUtils.isNotEmpty(relationEntities) && workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode())) {
            List<WorkOrderProductLineRelationEntity> lineRelationEntities = relationEntities.stream().map(relationEntity ->
                            WorkOrderProductLineRelationEntity.builder()
                                    .workOrderId(workOrderEntity.getWorkOrderId())
                                    .productLineId(relationEntity.getProductionBasicUnitId())
                                    .operationOrderId(operationId)
                                    .build())
                    .collect(Collectors.toList());
            workOrderProductLineRelationService.saveBatch(lineRelationEntities);
        }
    }

    @Override
    public List<String> getWorkOrderNumberByState(Integer fid, String workOrderNumber) {
//        获取指定工位下指定状态的工单列表(非创建，关闭，取消状态的列表)
        //1.查询工位对应的制造单元
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
        if (facilitiesEntity == null) {
            throw new ResponseException(RespCodeEnum.FAC_NOT_FOUND);
        }
        Integer productionLineId = facilitiesEntity.getProductionLineId();
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkOrderEntity::getLineId, productionLineId);
        wrapper.in(WorkOrderEntity::getState,
                WorkOrderStateEnum.RELEASED.getCode(),
                WorkOrderStateEnum.HANG_UP.getCode(),
                WorkOrderStateEnum.INVESTMENT.getCode(),
                WorkOrderStateEnum.FINISHED.getCode());
        if (StringUtils.isNotBlank(workOrderNumber)) {
            wrapper.like(WorkOrderEntity::getWorkOrderNumber, workOrderNumber);
        }
        List<WorkOrderEntity> grantList = this.list(wrapper);
        if (CollectionUtils.isEmpty(grantList)) {
            //该工位下没有这个工单
            throw new ResponseException(RespCodeEnum.WORK_ORDER_NOT_FAC);
        }
        List<String> workOrderNumberList = grantList.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderNumberList)) {
            //该工位下没有这个工单
            throw new ResponseException(RespCodeEnum.WORK_ORDER_NOT_FAC);
        }
        return workOrderNumberList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkOrderEntity updateBaseInfo(WorkOrderEntity entity, String username) {
        // 补全字段
        WorkOrderEntity workOrderEntity = completedNewWorkOrder(entity);

        // 允许修改为空值
        if (entity.getCoefficient() == null) {
            workOrderEntity.setCoefficient(null);
        }
        if (entity.getLineId() == null) {
            workOrderEntity.setLineId(null);
            workOrderEntity.setLineCode(null);
            workOrderEntity.setLineName(null);
        } else {
            ProductionLineEntity productionLineEntity = productionLineMapper.selectById(entity.getLineId());
            workOrderEntity.setLineName(productionLineEntity.getName());
            workOrderEntity.setLineCode(productionLineEntity.getProductionLineCode());
            workOrderEntity.setLineId(productionLineEntity.getProductionLineId());
        }
        return workOrderService.updateByWorkId(workOrderEntity, username);
    }

    @Override
    public Boolean judgeBarCodeType(String barCode, String scanType) {
        if (scanType.equals(Constant.DEVICE)) {
            LambdaQueryWrapper<DeviceEntity> deviceWrapper = new LambdaQueryWrapper<>();
            deviceWrapper.eq(DeviceEntity::getDeviceCode, barCode);
            return deviceService.count(deviceWrapper) > 0;
        } else if (scanType.equals(Constant.WORK_ORDER)) {
            LambdaQueryWrapper<WorkOrderEntity> workOrderWrapper = new LambdaQueryWrapper<>();
            workOrderWrapper.eq(WorkOrderEntity::getWorkOrderNumber, barCode);
            return this.count(workOrderWrapper) > 0;
        }
        return false;
    }

    @Override
    public List<WorkOrderProcedureRelationEntity> selectWorkOrderProcedureList(String workOrderNumber) {
        return workOrderProcedureRelationService.getWorkOrderProcedureRelationEntities(workOrderNumber);
    }

    @Override
    public List<CraftProcedureEntity> listCraftProcedureByProductOrderId(Integer productOrderId) {
        // 生产订单关联的工单
        List<WorkOrderEntity> workOrders = orderWorkOrderService.listWorkOrderByOrderId(productOrderId, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        if (CollectionUtils.isEmpty(workOrders)) {
            return new ArrayList<>();
        }
        // 取第一个工单关联的工艺
        Integer craftId = workOrders.get(0).getCraftId();
        if (Objects.isNull(craftId)) {
            return new ArrayList<>();
        }
        List<CraftProcedureEntity> tempCraftProcedures = craftProcedureService.getProcedureList(craftId);

        return craftProcedureService.sortCraftProceduresLine(tempCraftProcedures);
    }

    @Override
    public List<CraftProcedureEntity> listCraftProcedureByWorkOrderNumber(String workOrderNumber) {
        WorkOrderEntity workOrderEntity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).one();
        if (ObjectUtil.isEmpty(workOrderEntity)) {
            return new ArrayList<>();
        }
        Integer craftId = workOrderEntity.getCraftId();
        if (ObjectUtil.isEmpty(craftId)) {
            return new ArrayList<>();
        }
        return craftProcedureService.getProcedureList(craftId);
    }

    @Override
    public Integer getCraftIdByWorkOrderNumber(String workOrderNumber) {
        WorkOrderEntity workOrderEntity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).one();
        if (workOrderEntity == null) {
            return null;
        }
        return workOrderEntity.getCraftId();
    }

    @Override
    public WorkOrderEntity checkWorkOrder(String workOrderNumber) {
        //检验工单在系统中是否存在
        if (StringUtils.isNotBlank(workOrderNumber)) {
            WorkOrderEntity entity = this.getSimpleWorkOrderByNumber(workOrderNumber);
            if (entity.getState().equals(WorkOrderStateEnum.CREATED.getCode())) {
                throw new ResponseException(RespCodeEnum.WORK_ORDER_STATE_ERROR);
            }
            return entity;
        }
        return null;
    }


    @Override
    public List<BillVo> judgeBeforeInsert(WorkOrderEntity entity) {
        // 无关联销售订单和生产订单则直接返回
        if (CollectionUtils.isEmpty(entity.getSaleOrderList()) && CollectionUtils.isEmpty(entity.getProductOrderList())) {
            return null;
        }
        List<BillVo> billVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entity.getSaleOrderList())) {
            // 获取销售订单绑定的物料
            //List<Integer> orderIds = entity.getSaleOrderPage().stream().map(SaleOrderEntity::getSaleOrderId).collect(Collectors.toList());
            String relatedBillNumbers = entity.getSaleOrderList().stream().map(SaleOrderEntity::getSaleOrderNumber).collect(Collectors.joining(Constant.SEP));
            // 获取超出相同物料数量的对象
            billVos.add(BillVo.builder()
                    .billTypeCode(Constant.WORK_ORDER)
                    .relatedBillNumber(relatedBillNumbers)
                    .relatedBillTypeCode(Constant.SALE_ORDER).build());
        }
        if (CollectionUtils.isNotEmpty(entity.getProductOrderList())) {
            // 获取生产订单绑定的物料
            String relatedBillNumbers = entity.getProductOrderList().stream().map(ProductOrderEntity::getProductOrderNumber).collect(Collectors.joining(Constant.SEP));
            // 获取超出相同物料数量的对象
            billVos.add(BillVo.builder()
                    .billTypeCode(Constant.WORK_ORDER)
                    .relatedBillNumber(relatedBillNumbers)
                    .relatedBillTypeCode(Constant.PRODUCT_ORDER)
                    .build());
        }
        return billVos;
    }

    @Override
    public void updateByWorkOrderNumber(WorkOrderEntity workOrderEntity) {
        this.updateByIdAndDefectConfig(workOrderEntity, WorkOrderUnqualifiedSourceEnum.LINE_REPORT);
        //工单节点自动上报记录
        nodeAutoReportService.workOrderAutoReport(workOrderEntity);
        messagePushToKafkaService.pushNewMessage(workOrderEntity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_UPDATE_MESSAGE);
    }

    @Override
    public List<WorkOrderEntity> getListForAging(String workOrderNumber) {
        QueryWrapper<WorkOrderEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().like(StringUtils.isNotBlank(workOrderNumber), WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                .in(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode(),
                        WorkOrderStateEnum.HANG_UP.getCode(),
                        WorkOrderStateEnum.FINISHED.getCode());
        List<WorkOrderEntity> workOrderEntities = this.list(wrapper);
        // 查询物料信息
        List<String> materialCodes = workOrderEntities.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(materialCodes)) {
            Map<String, MaterialEntity> codeMaterialMap = materialService.lambdaQuery()
                    .in(MaterialEntity::getCode, materialCodes)
                    .list().stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
            // 查询sku信息
            Set<Integer> skuIds = workOrderEntities.stream().map(WorkOrderEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, SkuEntity> skuMap = skuService.getSkusByIds(skuIds).stream().collect(Collectors.toMap(SkuEntity::getSkuId, v -> v));
            workOrderEntities.forEach(record -> {
                MaterialEntity materialEntity = codeMaterialMap.get(record.getMaterialCode());
                if (materialEntity != null) {
                    materialEntity.setSkuEntity(skuMap.get(record.getSkuId()));
                }
                record.setMaterialFields(materialEntity);
            });
        }
        return workOrderEntities;
    }


    /**
     * 按照类型进行查找
     *
     * @param type
     * @param list
     * @return
     */
    private List<CraftFileNewDTO> searchType(String type, List<CraftFileNewDTO> list) {
        List<CraftFileNewDTO> results = new ArrayList<>();
        for (CraftFileNewDTO craftFileNewDTO : list) {
            if (type.equals(craftFileNewDTO.getType())) {
                results.add(craftFileNewDTO);
            }
        }
        return results;

    }

    /**
     * 根据文件名称进行模糊查询
     *
     * @param name
     * @param list
     * @return
     */
    private List<CraftFileNewDTO> searchName(String name, List<CraftFileNewDTO> list) {
        List<CraftFileNewDTO> results = new ArrayList<>();
        Pattern pattern = Pattern.compile(name);
        for (int i = 0; i < list.size(); i++) {
            Matcher matcher = pattern.matcher((list.get(i)).getName());
            //可以进行模糊查询
            if (matcher.find()) {
                results.add(list.get(i));
            }
            Matcher mat = pattern.matcher((list.get(i)).getFileName());
            if (mat.find()) {
                results.add(list.get(i));
            }
        }
        return results;
    }

    /**
     * 获取作业工单文件
     *
     * @param workOrderNumber
     * @return
     */
    private List<CraftFileNewDTO> selectAllCraftFile(String workOrderNumber, Integer procedureId) {
        List<CraftFileNewDTO> craftFileDTOList = new ArrayList<>();
        //条件2：根据工单号查询对应的工单，根据工单查询对应的成品物料，查询成品物料下的附件
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            return Collections.emptyList();
        }
        // 工单下的附件
        craftFileDTOList.addAll(getWorkOrderFiles(workOrderEntity.getWorkOrderId()));
        // 查询工单关联的物料附件
        craftFileDTOList.addAll(getMaterialFiles(workOrderEntity.getMaterialCode()));

        Integer craftId = workOrderEntity.getCraftId();
        if (Objects.isNull(craftId)) {
            return craftFileDTOList;
        }
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.lambdaQuery()
                .eq(CraftProcedureEntity::getCraftId, craftId)
                .list();

        if (CollectionUtils.isEmpty(craftProcedureEntities)) {
            return craftFileDTOList;
        }

        List<CraftProcedureEntity> filterProcedures;
        if (procedureId != null) {
            filterProcedures = craftProcedureEntities.stream().filter(o -> o.getId().equals(procedureId)).collect(Collectors.toList());
        } else {
            filterProcedures = craftProcedureEntities;
        }

        for (CraftProcedureEntity craftProcedureEntity : filterProcedures) {
            QueryWrapper<ProcedureFileEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ProcedureFileEntity::getProcedureId, craftProcedureEntity.getId())
                    .eq(ProcedureFileEntity::getCraftId, craftProcedureEntity.getCraftId());
            List<ProcedureFileEntity> procedureFileEntityList = procedureFileService.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(procedureFileEntityList)) {
                for (ProcedureFileEntity fileEntity : procedureFileEntityList) {
                    CraftFileNewDTO craftFileNewDTO = CraftFileNewDTO.builder()
                            .name(craftProcedureEntity.getProcedureName())
                            .fileName(fileEntity.getName())
                            .typeName(AppendixTypeEnum.PROCEDURE_APPENDIX.getName())
                            .type(AppendixTypeEnum.PROCEDURE_APPENDIX.getCode())
                            .filePath(fileEntity.getFileUrl())
                            .build();
                    craftFileDTOList.add(craftFileNewDTO);
                }
            }
        }
        return craftFileDTOList;
    }

    @Override
    public List<CraftFileNewDTO> getWorkOrderFiles(Integer workOrderId) {
        List<AppendixEntity> appendixWorkOrderEntities = appendixService.getFilesById(AppendixTypeEnum.WORKORDER_APPENDIX.getCode(), String.valueOf(workOrderId), null);
        // 构造前端返回参数
        return appendixWorkOrderEntities.stream()
                // 加上排序
                .sorted(Comparator.comparing(AppendixEntity::getId).reversed())
                .map(fileEntity -> CraftFileNewDTO.builder()
                        .name(fileEntity.getRelateName())
                        .filePath(fileEntity.getFilePath())
                        .typeName(AppendixTypeEnum.WORKORDER_APPENDIX.getName())
                        .type(AppendixTypeEnum.WORKORDER_APPENDIX.getCode())
                        .remark(fileEntity.getRemark())
                        .fileName(fileEntity.getFileName()).build()
                ).collect(Collectors.toList());
    }

    @Override
    public List<CraftFileNewDTO> getMaterialFiles(String materialCode) {
        MaterialEntity materialEntityByCode = materialService.getSimpleMaterialByCode(materialCode);
        // 查询物料关联的附件
        List<AppendixEntity> appendixMaterialEntities = appendixService.getFilesById(AppendixTypeEnum.MATERIAL_APPENDIX.getCode(), String.valueOf(materialEntityByCode.getId()), null);
        // 构造前端返回参数
        return appendixMaterialEntities.stream()
                .sorted(Comparator.comparing(AppendixEntity::getId).reversed())
                .map(fileEntity -> CraftFileNewDTO
                        .builder()
                        .name(fileEntity.getRelateName())
                        .filePath(fileEntity.getFilePath())
                        .typeName(AppendixTypeEnum.MATERIAL_APPENDIX.getName())
                        .type(AppendixTypeEnum.MATERIAL_APPENDIX.getCode())
                        .remark(fileEntity.getRemark())
                        .fileName(fileEntity.getFileName())
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public List<CraftFileNewDTO> getBomFiles(String materialCode) {
        List<BomEntity> bomList = bomService.lambdaQuery().eq(BomEntity::getCode, materialCode).list();
        // 查询bom关联的附件
        if (CollectionUtils.isEmpty(bomList)) {
            return Collections.emptyList();
        }
        List<AppendixEntity> appendixMaterialEntities = appendixService.getFiles(
                AppendixTypeEnum.BOM_APPENDIX.getCode(),
                bomList.stream().map(e -> e.getId().toString()).collect(Collectors.toList()));
        // 构造前端返回参数
        return appendixMaterialEntities.stream()
                .sorted(Comparator.comparing(AppendixEntity::getId).reversed())
                .map(fileEntity -> CraftFileNewDTO
                        .builder()
                        .name(fileEntity.getRelateName())
                        .filePath(fileEntity.getFilePath())
                        .typeName(AppendixTypeEnum.BOM_APPENDIX.getName())
                        .type(AppendixTypeEnum.BOM_APPENDIX.getCode())
                        .remark(fileEntity.getRemark())
                        .fileName(fileEntity.getFileName())
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public List<CraftFileNewDTO> getProcedureFiles(String workOrderNumber, Integer fid, Integer procedureId) {
        // 工单关联的工序
        List<WorkOrderProcedureRelationEntity> procedureRelationEntities = workOrderProcedureRelationService.lambdaQuery()
                .eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumber)
                // 根据工序过滤
                .eq(procedureId != null, WorkOrderProcedureRelationEntity::getProcedureId, procedureId)
                .list();
        if (CollectionUtils.isEmpty(procedureRelationEntities)) {
            return Collections.emptyList();
        }

        // 工位名称
        String FacType = null;
        if (fid != null) {
            FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
            if (facilitiesEntity == null) {
                throw new ResponseException("未找工位，id:" + fid);
            }
            // 工位类型名称
            FacType = modelService.getModelNameById(facilitiesEntity.getModelId());
        }
        String finalFacType = FacType;

        // 工艺工序
        List<Integer> craftProcedureIds = procedureRelationEntities.stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureId).distinct().collect(Collectors.toList());
        List<CraftProcedureEntity> craftProcedureEntities = craftProcedureService.listByIds(craftProcedureIds);
        Map<Integer, Integer> craftProcedureIdMap = craftProcedureEntities.stream().collect(Collectors.toMap(CraftProcedureEntity::getId, CraftProcedureEntity::getProcedureId));

        // 工序文件
        List<ProcedureFileEntity> procedureFileEntities = craftProcedureEntities.stream()
                .filter(e -> fid == null || Objects.equals(e.getFacType(), finalFacType))
                // 各个工序工位的附件组
                .map(e -> procedureFileService.lambdaQuery()
                        .eq(ProcedureFileEntity::getProcedureId, e.getId())
                        .eq(ProcedureFileEntity::getCraftId, e.getCraftId())
                        .list()
                )
                // 元素合并
                .flatMap(Collection::stream)
                // 按照最新的排序
                .sorted(Comparator.comparing(ProcedureFileEntity::getId).reversed())
                .collect(Collectors.toList());

        // 工序名称
        List<ProcedureEntity> procedureEntities = CollectionUtils.isEmpty(craftProcedureIdMap) ?
                Collections.emptyList() :
                procedureService.listByIds(craftProcedureIdMap.values());
        Map<Integer, String> procedureIdNameMap = procedureEntities.stream().collect(Collectors.toMap(ProcedureEntity::getProcedureId, ProcedureEntity::getName));

        // 返回
        return procedureFileEntities.stream()
                .map(fileEntity -> CraftFileNewDTO.builder()
                        .name(procedureIdNameMap.get(craftProcedureIdMap.get(fileEntity.getProcedureId())))
                        .fileName(fileEntity.getName())
                        .typeName(AppendixTypeEnum.PROCEDURE_APPENDIX.getName())
                        .type(AppendixTypeEnum.PROCEDURE_APPENDIX.getCode())
                        .filePath(fileEntity.getFileUrl())
                        .build()
                ).collect(Collectors.toList());
    }

    /**
     * 获取生效、投产、挂起工单状态
     *
     * @return
     */
    @Override
    public List<StateEnumDTO> getPartOfStates() {
        ArrayList<StateEnumDTO> dtos = new ArrayList<>();
        dtos.add(StateEnumDTO.builder().code(WorkOrderStateEnum.RELEASED.getCode()).name(WorkOrderStateEnum.RELEASED.getName()).build());
        dtos.add(StateEnumDTO.builder().code(WorkOrderStateEnum.INVESTMENT.getCode()).name(WorkOrderStateEnum.INVESTMENT.getName()).build());
        dtos.add(StateEnumDTO.builder().code(WorkOrderStateEnum.HANG_UP.getCode()).name(WorkOrderStateEnum.HANG_UP.getName()).build());
        return dtos;
    }

    @Override
    public BomEntity getMultiLevelBom(String materialCode, String workOrderNum) {
        log.info("getMultiLevelBom入参，物料编码：{}，工单编码：{}", materialCode, workOrderNum);
        Map<BomKeyDTO, BomEntity> bomMap = new HashMap<>();
        List<BomRawMaterialEntity> list = new ArrayList<>();
        Map<String, Double> totalQuantityMap = new HashMap<>(8);

        // 获取生产工单的计划数量
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNum);
        double planQuantity = 0.0;
        if (workOrderEntity != null) {
            planQuantity = workOrderEntity.getPlanQuantity();
        }
        BomKeyDTO bomKeyDTO = new BomKeyDTO(materialCode, workOrderEntity == null ? 0 : workOrderEntity.getSkuId());
        BomEntity bom = bomService.getLatestSimpleBom(bomKeyDTO.getMaterialCode(), bomKeyDTO.getSkuId());
        if (Objects.isNull(bom)) {
            return null;
        }
        // 设置状态
        bom.setStateName(BomStateEnum.getNameByCode(bom.getState()));
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.getListByBomId(bom.getId());
        // 查询bom关联的修订次数
        Integer latestVersionRevision = versionChangeRecordService.getLatestVersionRevision(VersionChangeRecordSelectDTO.builder()
                .relateType(VersionModelIdEnum.BOM)
                .relateId(bom.getId())
                .build());
        bom.setVersionRevision(latestVersionRevision);

        // 查询物料当前库存数量
        List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
        bomRawMaterialEntities.forEach(o -> materialDetails.add(StockMaterialDetailEntity.builder().productCode(o.getCode()).skuId(o.getSkuId()).build()));

        List<String> codes = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
        Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(inventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new com.alibaba.fastjson.TypeReference<Map<String, BigDecimal>>() {
        }, null);
        materialCodeStockMap = materialCodeStockMap == null ? new HashMap<>(16) : materialCodeStockMap;
        if (CollectionUtils.isNotEmpty(bomRawMaterialEntities)) {
            //获取物料总库存
            List<StockInventoryDetailEntity> allHouse = JacksonUtil.getResponseArray(inventoryDetailInterface.getList(StockInventorySelectDTO.builder().materialCodes(codes).build()), StockInventoryDetailEntity.class);

            for (StockInventoryDetailEntity record : allHouse) {
                totalQuantityMap.merge(record.getProductCode(), record.getStockLocationQuantity(), Double::sum);
            }
        }

        // 领料申请数量：生成工单 关联的生产领料单（除创建和取消状态）
        List<TakeOutApplicationMaterialEntity> takeOutApplicationMaterials = takeOutApplicationService.listTakeOutApplicationMaterials(workOrderNum);
        Map<String, Double> applyMaterialCodeQuantityMap = new HashMap<>(8);
        takeOutApplicationMaterials.stream().filter(record -> !Objects.isNull(record.getPlanNum()))
                .forEach(record -> applyMaterialCodeQuantityMap.merge(ColumnUtil.getMaterialSku(record.getCode(), record.getSkuId()), record.getPlanNum(), Double::sum));

        // 领料数量：生产工单关联的 关联的出库单(类型为工单领取物料)
        List<StockMaterialDetailEntity> takeOutStockMaterialDetails = JacksonUtil.getResponseArray(inAndOutInterface.listStockMaterialDetailsByType(workOrderNum, InputOrOutputRelateTypeEnum.WORK_ORDER_TAKE_OUT_WORK_ORDER), StockMaterialDetailEntity.class);

        Map<String, Double> takeOutMaterialCodeQuantityMap = new HashMap<>(8);
        takeOutStockMaterialDetails.stream().filter(record -> !Objects.isNull(record.getActualAmount()))
                .forEach(record -> takeOutMaterialCodeQuantityMap.merge(ColumnUtil.getMaterialSku(record.getProductCode(), record.getSkuId()), record.getActualAmount(), Double::sum));

        // 退料数量：生产工单关联的 关联的入库单(类型为工单退物料)
        List<StockMaterialDetailEntity> applicationReturnStockMaterialDetails = JacksonUtil.getResponseArray(inAndOutInterface.listStockMaterialDetailsByType(workOrderNum, InputOrOutputRelateTypeEnum.APPLICATION_RETURN_WORK_ORDER), StockMaterialDetailEntity.class);
        Map<String, Double> returnMaterialCodeQuantityMap = new HashMap<>(8);
        applicationReturnStockMaterialDetails.stream().filter(record -> !Objects.isNull(record.getActualAmount()))
                .forEach(record -> returnMaterialCodeQuantityMap.merge(ColumnUtil.getMaterialSku(record.getProductCode(), record.getSkuId()), record.getActualAmount(), Double::sum));
        // 获取物料相关字段
        List<MaterialCodeAndSkuIdSelectDTO> codeAndSkuIds = bomRawMaterialEntities.stream()
                .map(res -> MaterialCodeAndSkuIdSelectDTO.builder().materialCode(res.getCode()).skuId(res.getSkuId()).build())
                .collect(Collectors.toList());
        List<MaterialEntity> materials = materialService.listSimpleMaterialByCodesAndSkuIds(codeAndSkuIds);
        Map<String, MaterialEntity> keyMaterialMap = materials.stream().collect(Collectors.toMap(material -> ColumnUtil.getMaterialSku(material.getCode(), material.getSkuEntity()), v -> v));
        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
            // 设置BOM清单中物料的领料申请数量、领料数量、退料数量、当前领用数量、当前库存数量
            String code = bomRawMaterialEntity.getCode();
            Integer skuId = bomRawMaterialEntity.getSkuId();
            String materialSku = ColumnUtil.getMaterialSku(code, skuId);

            // 领料申请数量
            bomRawMaterialEntity.setApplyQuantity(applyMaterialCodeQuantityMap.getOrDefault(materialSku, 0.0));
            // 领料数量
            bomRawMaterialEntity.setTakeOutQuantity(takeOutMaterialCodeQuantityMap.getOrDefault(materialSku, 0.0));
            // 退料数量
            bomRawMaterialEntity.setReturnQuantity(returnMaterialCodeQuantityMap.getOrDefault(materialSku, 0.0));
            // 当前领用数量
            bomRawMaterialEntity.setCurUseQuantity(bomRawMaterialEntity.getTakeOutQuantity() - bomRawMaterialEntity.getReturnQuantity());
            // 当前库存数量
            bomRawMaterialEntity.setStockQuantity(materialCodeStockMap.getOrDefault(materialSku, BigDecimal.valueOf(0.0)).doubleValue());
            // 物料总库存
            bomRawMaterialEntity.setAllQuantity(totalQuantityMap.getOrDefault(code, 0.0));
            // 物料数量 = 生产工单的计划数量 x 该层的物料系数
            Rational rational = new Rational(bomRawMaterialEntity.getNum().doubleValue(), bomRawMaterialEntity.getNumber().doubleValue());
            Rational mulitiply = new Rational(planQuantity).mulitiply(rational);
            BigDecimal materialQuantity = BigDecimal.valueOf(mulitiply.doubleValue());
            bomRawMaterialEntity.setWorkOrderMaterialQuantity(materialQuantity);
            // 齐套数量( 物料当前库存 - 物料数量 )
            double completeQuantity = bomRawMaterialEntity.getStockQuantity() - materialQuantity.doubleValue();
            bomRawMaterialEntity.setIsComplete(completeQuantity >= 0);

            MaterialEntity materialEntity = keyMaterialMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId()));
            bomRawMaterialEntity.setMaterialFields(materialEntity);
            list.add(bomRawMaterialEntity);
            // 子物料需要计算的物料数量
            double needQuantity = completeQuantity >= 0 ? 0 : materialQuantity.doubleValue() - bomRawMaterialEntity.getStockQuantity();
            // 获取下一级bom
            setSubLevelBom(bomRawMaterialEntity, materialCode, bomMap, applyMaterialCodeQuantityMap, takeOutMaterialCodeQuantityMap, returnMaterialCodeQuantityMap, needQuantity);
        }
        bom.setBomRawMaterialEntities(list);
        return bom;
    }

    @Override
    public List<WorkCenterEntity> getAllWorkCenterWithMaterialCode(String materialCode, Integer craftId, String businessType) {
        // 获取工厂所有的制造单元类型
        List<WorkCenterEntity> workCenterEntityList = workCenterMapper.selectList(new LambdaQueryWrapper<>());
        // 单据类型为标准工单 --> 查询正常工艺
        // 单据类型为返工工单 --> 查询返工工艺
        String typeCode = null;
        if (Objects.nonNull(businessType)) {
            //正常工单获取正常的工艺，返工工单既可以获取正常又可以获取返工工艺
            typeCode = Objects.equals(WorkOrderTypeEnum.STANDARD_WORK_ORDER.getCode(), businessType) ? CraftTypeEnum.NORMAL.getCode() : CraftTypeEnum.REWORK.getCode();
        }
        CraftEntity craftEntity = Objects.isNull(craftId) ? craftService.lambdaQuery().eq(CraftEntity::getMaterialCode, materialCode)
                .eq(CraftEntity::getIsTemplate, Constants.FALSE)
                .eq(CraftEntity::getState, CraftStateEnum.RELEASED.getCode())
                .eq(StringUtils.isNotBlank(typeCode), CraftEntity::getType, typeCode)
                .orderByDesc(CraftEntity::getCreateTime)
                .orderByDesc(CraftEntity::getCraftId)
                .last(Constant.LIMIT_ONE)
                .one() : craftService.getById(craftId);
        if (Objects.isNull(craftEntity)) {
            return workCenterEntityList;
        }
        List<CraftProcedureEntity> craftProcedureList = craftProcedureService.listByCraftId(craftEntity.getCraftId());
        // 获取工单物料的可选工序列表中的制造单元类型
        craftProcedureList = craftProcedureList.stream().filter(o -> OutsourcingProcedureEnum.YES.getCode() != o.getIsSubContractingOperation()).filter(o -> o.getWorkCenterIds() != null).collect(Collectors.toList());
        List<String> workCenterIdsTemp = craftProcedureList.stream().map(CraftProcedureEntity::getWorkCenterIds).distinct().collect(Collectors.toList());
        List<String> workCenterIds = new ArrayList<>();
        for (String workCenterId : workCenterIdsTemp) {
            if (StringUtils.isNotBlank(workCenterId)) {
                workCenterIds.addAll(Arrays.asList(workCenterId.split(Constant.SEP)));
            }
        }
        for (WorkCenterEntity workCenterEntity : workCenterEntityList) {
            if (workCenterIds.contains(String.valueOf(workCenterEntity.getId()))) {
                workCenterEntity.setIsExistProcedure(true);
            }
        }
        return workCenterEntityList;
    }


    /**
     * 获取下一级bom
     */
    private void setSubLevelBom(BomRawMaterialEntity bomRawMaterialEntity, String materialCode, Map<BomKeyDTO, BomEntity> bomMap,
                                Map<String, Double> applyMaterialCodeQuantityMap,
                                Map<String, Double> takeOutMaterialCodeQuantityMap,
                                Map<String, Double> returnMaterialCodeQuantityMap, Double materialQuantity) {
        List<BomRawMaterialEntity> list = new ArrayList<>();
        Map<String, Double> totalQuantityMap = new HashMap<>(8);
        BomKeyDTO dto = new BomKeyDTO(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId());
        List<BomRawMaterialEntity> bomRawMaterialEntities = bomService.getLatestBomInCache(bomMap, dto);

        // 查询物料当前库存数量
        List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
        bomRawMaterialEntities.forEach(o -> materialDetails.add(StockMaterialDetailEntity.builder().productCode(o.getCode()).skuId(o.getSkuId()).build()));

        List<String> codes = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
        Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(inventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new com.alibaba.fastjson.TypeReference<Map<String, BigDecimal>>() {
        }, null);
        materialCodeStockMap = materialCodeStockMap == null ? new HashMap<>(16) : materialCodeStockMap;
        // 获取物料总库存
        if (CollectionUtils.isNotEmpty(bomRawMaterialEntities)) {
            List<StockInventoryDetailEntity> allHouse = JacksonUtil.getResponseArray(inventoryDetailInterface.getList(StockInventorySelectDTO.builder().materialCodes(codes).build()), StockInventoryDetailEntity.class);
            for (StockInventoryDetailEntity record : allHouse) {
                totalQuantityMap.merge(record.getProductCode(), record.getStockLocationQuantity(), Double::sum);
            }
        }
        for (BomRawMaterialEntity entity : bomRawMaterialEntities) {
            // 设置BOM清单中物料的领料申请数量、领料数量、退料数量、当前领用数量、当前库存数量
            String code = entity.getCode();
            Integer skuId = entity.getSkuId();
            String materialSku = ColumnUtil.getMaterialSku(code, skuId);
            if (materialCode.equals(code)) {
                throw new ResponseException("bom循环");
            }
            // 领料申请数量
            entity.setApplyQuantity(applyMaterialCodeQuantityMap.getOrDefault(materialSku, 0.0));
            // 领料数量
            entity.setTakeOutQuantity(takeOutMaterialCodeQuantityMap.getOrDefault(materialSku, 0.0));
            // 退料数量
            entity.setReturnQuantity(returnMaterialCodeQuantityMap.getOrDefault(materialSku, 0.0));
            // 当前领用数量
            entity.setCurUseQuantity(entity.getTakeOutQuantity() - entity.getReturnQuantity());
            // 当前库存数量
            entity.setStockQuantity(materialCodeStockMap.getOrDefault(materialSku, BigDecimal.valueOf(0.0)).doubleValue());
            // 物料总库存
            entity.setAllQuantity(totalQuantityMap.getOrDefault(code, 0.0));
            // 物料数量 = 上级物料的数量 x 该层的物料系数
            Rational rational = new Rational(entity.getNum().doubleValue(), entity.getNumber().doubleValue());
            Rational subMaterialQuantityRational = new Rational(materialQuantity).mulitiply(rational);
            BigDecimal subMaterialQuantity = BigDecimal.valueOf(subMaterialQuantityRational.doubleValue());
            entity.setWorkOrderMaterialQuantity(subMaterialQuantity);
            // 齐套数量( 物料当前库存 - 物料数量 )
            double completeQuantity = entity.getStockQuantity() - subMaterialQuantity.doubleValue();
            entity.setIsComplete(completeQuantity >= 0);

            list.add(entity);
            // 子物料需要计算的物料数量
            double needQuantity = completeQuantity >= 0 ? 0 : subMaterialQuantity.doubleValue() - entity.getStockQuantity();
            // 获取下一级bom
            setSubLevelBom(entity, materialCode, bomMap, applyMaterialCodeQuantityMap, takeOutMaterialCodeQuantityMap, returnMaterialCodeQuantityMap, needQuantity);
        }
        bomRawMaterialEntity.setChildren(list);
    }

    @Override
    public Double getPlanWorkTimeOutByNumber(String workOrderNumber) {
        //计划工时超时时间(小时)
        double timeOut = 0.0;
        // 查询工单下的工艺工序
        List<CraftProcedureEntity> craftProcedureEntities = workOrderProcedureRelationService.getCraftProcedureListByWorkOrderNumber(workOrderNumber);
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNumber);
        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
            ProcedureControllerConfigEntity configEntity = procedureControllerConfigService.getEntityByCraftProcedureId(craftProcedureEntity.getId());
            if (configEntity != null) {
                double add;
                //判断生产超时阈值设置的单位类型
                if (configEntity.getProductionTimeoutThresholdConfigurationUnit().equals(TimeoutThresholdTypeEnum.FIXED_VALUE.getCode())) {
                    //固定值类型(min)
//                    计划工时+生产超时阈值配置_固定值
                    //分钟转换为小时
                    Double productionTimeoutThresholdConfiguration = MathUtil.divideDouble(configEntity.getProductionTimeoutThresholdConfiguration(), 60, 2);
                    add = MathUtil.add(workOrderEntity.getPlannedWorkingHours(), productionTimeoutThresholdConfiguration);
                } else {
                    //百分比类型
//                    计划工时*(1+生产超时阈值配置_百分比)
                    add = MathUtil.mul(workOrderEntity.getPlannedWorkingHours(), MathUtil.add(1.0, configEntity.getProductionTimeoutThresholdConfiguration()));
                }
                //判断是否超时，超时多久
                if (workOrderEntity.getActualWorkingHours() >= add) {
                    //计划工时超时多久//多个工序进行累加
                    timeOut += MathUtil.sub(workOrderEntity.getActualWorkingHours(), add);
                }
            }
        }
        return timeOut;
    }

    @Override
    public Double getCirculationTimeOutByNumber(String workOrderNumber) {
        //流转超时时间(小时)
        double timeOut = 0.0;
        // 查询工单下的工艺工序
        List<CraftProcedureEntity> craftProcedureEntities = workOrderProcedureRelationService.getCraftProcedureListByWorkOrderNumber(workOrderNumber);
        WorkOrderEntity workOrderEntity = this.getWorkOrderByNumber(workOrderNumber);
        for (CraftProcedureEntity craftProcedureEntity : craftProcedureEntities) {
            ProcedureControllerConfigEntity configEntity = procedureControllerConfigService.getEntityByCraftProcedureId(craftProcedureEntity.getId());
            if (configEntity != null) {
                double add;
                //判断流转超时阈值设置的单位类型
                if (configEntity.getFlowTimeoutThresholdConfigurationUnitType().equals(TimeoutThresholdTypeEnum.FIXED_VALUE.getCode())) {
                    //固定值类型(min)
//                    标准流转时长+流转超时阈值配置_固定值
                    //分钟转换为小时
                    Double flowTimeoutThresholdConfiguration = MathUtil.divideDouble(configEntity.getFlowTimeoutThresholdConfiguration(), 60, 2);
                    add = MathUtil.add(configEntity.getStandardCirculationDuration(), flowTimeoutThresholdConfiguration);
                } else {
                    //百分比类型
//                    标准流转时长+流转超时阈值配置_固定值
                    add = MathUtil.mul(configEntity.getStandardCirculationDuration(), MathUtil.add(1.0, configEntity.getFlowTimeoutThresholdConfiguration()));
                }
                //判断是否超时，超时多久
                if (workOrderEntity.getCirculationDuration() > add) {
                    //流转超时多久 多个工序进行累加
                    timeOut += MathUtil.sub(workOrderEntity.getCirculationDuration(), add);
                }
            }
        }
        return timeOut;
    }

    /**
     * 统计制造单元的生产工单预计剩余加工时间
     *
     * @param lineId
     * @return
     */
    @Override
    public Double getTheRestOfProductionTime(Integer lineId, Integer state, List<String> excludeOrders) {
        return this.baseMapper.getTheRestOfProductionTime(lineId, state, excludeOrders);
    }

    /**
     * 获取工单的工艺工序的加工时长
     *
     * @param workOrderNumbers
     * @return
     */
    @Override
    public Double getProcessingHoursOfWorkOrder(List<String> workOrderNumbers) {
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return 0.0;
        }
        return this.baseMapper.getProcessingHoursOfWorkOrder(workOrderNumbers);
    }


    @Override
    public ProductOrderEntity getProductOrderByWorkOrder(Integer workOrderId, Integer craftProcedureId) {
        ArrayList<WorkOrderEntity> workOrderEntities = new ArrayList<>();
        List<OrderWorkOrderEntity> orderWorkOrderEntities = orderWorkOrderService.getByWorkOrderId(workOrderId, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        // 判断生产工单是否关联多个生产订单
        if (orderWorkOrderEntities.size() == 1) {
            OrderWorkOrderEntity orderWorkOrderEntity = orderWorkOrderEntities.get(0);
            ProductOrderEntity orderEntity = extProductOrderInterface.selectProductOrderById(orderWorkOrderEntity.getOrderId());
            productService.dealProcedureAlias(Collections.singletonList(orderEntity));
            if (orderEntity != null) {
                String materialCode = orderEntity.getProductOrderMaterials().get(0).getMaterialCode();
                Integer skuId = orderEntity.getProductOrderMaterials().get(0).getSkuId();
                // 判断工艺路线是否匹配
//                CraftEntity craftEntity = craftService.getCraftByMaterialCode(materialCode);
//                if (craftEntity == null) {
//                    throw new ResponseException(RespCodeEnum.ORDER_MATERIAL_HAS_NO_PROCESS_INFORMATION);
//                }
                // 设置工单详情
                WorkOrderEntity workOrderEntity = this.getById(workOrderId);
                if (Objects.isNull(workOrderEntity)) {
                    throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
                }
                // 判断工单订单产品是否匹配
                //if (!materialCode.equals(workOrderEntity.getMaterialCode())) {
                //    throw new ResponseException(RespCodeEnum.ORDER_MATERIAL_FOR_WORK_ORDER_DOES_NOT_MATCH);
                //}
                // 设置工序名称
                setCraftProcedure(workOrderEntity);
                workOrderEntity.setStateName(WorkOrderStateEnum.getNameByCode(workOrderEntity.getState()));
                //班组和设备名称
//                DeviceEntity deviceEntity = deviceService.getById(workOrderEntity.getDeviceId());
                String productBasicUnitNames = basicUnitRelationService.lambdaQuery().eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                        .list().stream()
                        .map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).collect(Collectors.joining(Constants.SEP));
                workOrderEntity.setDeviceName(workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.DEVICE.getCode()) ? productBasicUnitNames : null);
//                SysTeamEntity sysTeamEntity = sysTeamService.getById(workOrderEntity.getTeamId());
                workOrderEntity.setTeamName(workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.TEAM.getCode()) ? productBasicUnitNames : null);
                workOrderEntity.setLineName(workOrderEntity.getWorkCenterType().equals(WorkCenterTypeEnum.LINE.getCode()) ? productBasicUnitNames : null);
                WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getWorkCenterId());
                setProductionBasicUnit(workOrderEntity);
                //拿到工作中心type
                workOrderEntity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
                // 派工状态
                workOrderEntity.setAssignmentStateName(AssignmentStateEnum.getNameByType(workOrderEntity.getAssignmentState()));
                if (craftProcedureId != null) {
                    orderEntity.setCraftProcedureId(craftProcedureId);
                } else {
                    LambdaQueryWrapper<WorkOrderProcedureRelationEntity> relationQueryWrapper = new LambdaQueryWrapper<>();
                    relationQueryWrapper.eq(WorkOrderProcedureRelationEntity::getWorkOrderId, orderWorkOrderEntity.getWorkOrderId())
                            .orderByAsc(WorkOrderProcedureRelationEntity::getCraftProcedureId)
                            .orderByAsc(WorkOrderProcedureRelationEntity::getId)
                            .last(Constant.LIMIT_ONE);
                    WorkOrderProcedureRelationEntity workOrderProcedureRelationEntity = workOrderProcedureRelationService.getOne(relationQueryWrapper);
                    if (workOrderProcedureRelationEntity == null) {
                        throw new ResponseException(RespCodeEnum.WORK_ORDER_BIND_PROCESS_NOT_FOUND);
                    }
                    orderEntity.setCraftProcedureId(workOrderProcedureRelationEntity.getCraftProcedureId());
                }
                workOrderEntity.setCraftProcedureId(orderEntity.getCraftProcedureId());
                workOrderEntities.add(workOrderEntity);
                List<com.yelink.dfscommon.entity.dfs.WorkOrderEntity> workOrderEntities1 = JSONObject.parseArray(JSON.toJSONString(workOrderEntities), com.yelink.dfscommon.entity.dfs.WorkOrderEntity.class);
                orderEntity.setWorkOrderEntities(workOrderEntities1);
                orderEntity.setStateName(OrderStateEnum.getNameByCode(orderEntity.getState()));
                orderEntity.getProductOrderMaterials().get(0).setMaterialFields(JacksonUtil.parseObject(JSON.toJSONString(materialService.getEntityByCodeAndSkuId(materialCode, skuId)), com.yelink.dfscommon.entity.dfs.MaterialEntity.class));
                orderEntity.getProductOrderMaterials().get(0).setStateName(OrderStateEnum.getNameByCode(orderEntity.getProductOrderMaterials().get(0).getState()));

                // 工单详情页面查询关联订单所有工序的生产备注信息 --2.6.1 饶燕生
                List<ProductOrderReportRemarkEntity> productOrderReportRemarkEntities = extProductOrderInterface.getReportRemarkListByProductOrderNumber(ProductOrderDetailDTO.builder().productOrderNumber(orderEntity.getProductOrderNumber()).build());

                orderEntity.setProductOrderReportRemarkEntities(productOrderReportRemarkEntities);

                return orderEntity;
            }
        } else if (orderWorkOrderEntities.size() > 1) {
            throw new ResponseException(RespCodeEnum.MULTIPLE_PRODUCTION_ORDERS_ARE_ASSOCIATED);
        }
        throw new ResponseException(RespCodeEnum.NO_WORK_ORDER_RELATED_ORDER_INFORMATION_FOUND);
    }


    @Override
    public JudgeOrderVO judgeWorkOrderOrProductOrder(String orderNumber, String username) {
        ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(orderNumber).build());

        if (productOrderEntity != null) {
            return JudgeOrderVO.builder()
                    .orderNumber(productOrderEntity.getProductOrderNumber())
                    .orderType(OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode())
                    .orderId(productOrderEntity.getProductOrderId()).build();
        } else {
            LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(WorkOrderEntity::getWorkOrderNumber, WorkOrderEntity::getWorkOrderId, WorkOrderEntity::getState)
                    .eq(WorkOrderEntity::getWorkOrderNumber, orderNumber);
            // 有配权限才进行数据隔离
            Boolean hasPermission = isolationService.dataIsolation(username, wrapper);
            if (!hasPermission) {
                throw new ResponseException(RespCodeEnum.NO_WORK_CENTER_PERMISSION);
            }
            WorkOrderEntity workOrderEntity = this.getOne(wrapper);
            if (workOrderEntity == null) {
                throw new ResponseException(RespCodeEnum.THE_TRACKING_NUMBER_DOES_NOT_EXIST);
            }
            if (workOrderEntity.getState().equals(WorkOrderStateEnum.CREATED.getCode())) {
                throw new ResponseException(RespCodeEnum.NOT_SCAN_CREATED_WORK_ORDER);
            }
            return JudgeOrderVO.builder()
                    .orderNumber(workOrderEntity.getWorkOrderNumber())
                    .orderId(workOrderEntity.getWorkOrderId())
                    .orderType(OrderNumTypeEnum.WORK_ORDER.getTypeCode()).build();
        }
    }

    @Override
    public WorkOrderEntity getWorkOrderByNumberWithIsolation(String workOrderNumber, String username) {
        if (StringUtils.isBlank(workOrderNumber)) {
            return null;
        }
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            return null;
        }
        // 数据隔离，只能查询角色绑定工作中心下的工单
        if (!workOrderIsolation(username, workOrderEntity)) {
            throw new ResponseException(RespCodeEnum.NO_WORK_ORDER_PERMISSION);
        }
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(workOrderNumber);
        detailDTO.setIsShowRelateOrder(false);
        workOrderEntity = this.getWorkOrderById(detailDTO);
        showStateAndName(Stream.of(workOrderEntity).collect(Collectors.toList()));
        return workOrderEntity;
    }

    @Override
    public Page<ImportDataRecordEntity> recordList(Integer current, Integer size, String fileName, String startTime, String endTime) {
        return importDataRecordService.getList(ImportTypeEnum.WORK_ORDER_IMPORT.getType(), fileName, startTime, endTime, current, size);
    }

    @Override
    public void uploadCustomTemplate(MultipartFile file, String operationUsername) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        // 上传
        uploadService.uploadReferencedFile(UploadFileCodeEnum.WORK_ORDER_TEMPLATE_NAME.getCode(), operationUsername, file);
    }

    @Override
    public byte[] downloadImportTemplate() throws IOException {
        Workbook templateWorkbook = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        InputStream inputStream = null;
        try {
            inputStream = getTransferTemplate();
            templateWorkbook = WorkbookFactory.create(inputStream);
            //删除说明sheet和目标数据sheet
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } finally {
            ExcelUtil.closeWorkBook(templateWorkbook);
            IOUtils.closeQuietly(byteArrayOutputStream);
            IOUtils.closeQuietly(inputStream);
        }
    }

    @Override
    public InputStream downloadCustomImportTemplate() throws IOException {
        byte[] download = uploadService.download(UploadFileCodeEnum.WORK_ORDER_TEMPLATE_NAME.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            return new ByteArrayInputStream(download);
        }
        throw new ResponseException("未配置自定义转换模板");
    }

    /**
     * 获取转换表格文件
     *
     * @return
     * @throws IOException
     */
    private InputStream getTransferTemplate() throws IOException {
        InputStream inputStream;
        byte[] download = uploadService.download(UploadFileCodeEnum.WORK_ORDER_TEMPLATE_NAME.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            inputStream = new ByteArrayInputStream(download);
        } else {
            //默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template/workOrderTemplate.xlsx");
            inputStream = resource.getInputStream();
        }
        return inputStream;
    }

    @Override
    @Async
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        //初始化锁 + 进度
        importProgressService.initLockProgress(RedisKeyPrefix.WORK_ORDER_IMPORT_LOCK, importProgressKey);
        InputStream templateInputStream = null;
        File templateFile = null;
        try {
            //1、获取模板文件 ： 用户自定义/默认模板,转成文件
            templateInputStream = getTransferTemplate();
            templateFile = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!templateFile.getParentFile().exists()) {
                templateFile.getParentFile().mkdirs();
            }
            FileUtils.copyInputStreamToFile(templateInputStream, templateFile);
            //2、读取模板配置
            ExcelTemplateSetDTO excelTemplateSetDTO = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ExcelTemplateSetDTO.class, 0, 3).get(0);
            //3、通过配置将数据转换并复制到模板原始数据
            List<WorkOrderExcelDTO> workOrderExcelDTOS = ExcelUtil.executeDataToTarget(WorkbookFactory.create(inputStream), templateFile, excelTemplateSetDTO, WorkOrderExcelDTO.class);
            //4、校验并转换数据
            SpringUtil.getBean(WorkOrderExtendService.class).verifyFormat(workOrderExcelDTOS);
            List<WorkOrderExcelDTO> workOrderExcelDTOList = workOrderExcelDTOS.stream().filter(WorkOrderExcelDTO::getVerifyPass).collect(Collectors.toList());
            //5、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(workOrderExcelDTOS, WorkOrderExcelDTO.class, operationUsername);
            //6、保存导入记录
            int countPass = CollectionUtils.isEmpty(workOrderExcelDTOS) ? 0 : (int) workOrderExcelDTOS.stream().filter(WorkOrderExcelDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.WORK_ORDER_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.WORK_ORDER_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(workOrderExcelDTOS.size() - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //7、保存数据
            int rowTotal = workOrderExcelDTOList.size();
            if (rowTotal > 0) {
                for (int i = 0; i < rowTotal; i++) {
                    WorkOrderExcelDTO workOrderExcelDTO = workOrderExcelDTOList.get(i);
                    if (workOrderExcelDTO.getIsUpdate()) {
                        WorkOrderEntity entity = this.getSimpleWorkOrderByNumber(workOrderExcelDTO.getWorkOrderNumber());
                        updateImportDto(workOrderExcelDTO, entity, operationUsername);
                    } else {
                        addImportDto(workOrderExcelDTO, operationUsername);
                    }
                    importProgressService.updateProgress(importProgressKey, importUrl, workOrderExcelDTOS.size(), rowTotal, i + 1);
                }
            } else {
                importProgressService.updateProgress(importProgressKey, importUrl, workOrderExcelDTOS.size(), 0, 0);
            }
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.error("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(templateInputStream);
            IOUtils.closeQuietly(inputStream);
            FileUtils.deleteQuietly(templateFile);
            importProgressService.releaseLock(RedisKeyPrefix.WORK_ORDER_IMPORT_LOCK);
        }
    }

    @Override
    public com.yelink.dfscommon.dto.ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.WORK_ORDER_IMPORT_PROGRESS);
    }

    @Override
    public List<WorkOrderSimpleVO> getWorkOrderEntityList(WorkOrderSelectDTO dto) {
        List<WorkOrderEntity> workOrders = list(Wrappers.lambdaQuery(WorkOrderEntity.class)
                .like(dto.getWorkOrderNumber() != null, WorkOrderEntity::getWorkOrderNumber, dto.getWorkOrderNumber())
                .eq(dto.getLineId() != null, WorkOrderEntity::getLineId, dto.getLineId())
                .in(WorkOrderEntity::getState, Arrays.asList(2, 3, 4, 5, 6))
        );
        List<WorkOrderSimpleVO> result = BeanUtil.copyToList(workOrders, WorkOrderSimpleVO.class);
        List<MaterialEntity> materials = materialService.getMaterialsByCodes(workOrders.stream().map(WorkOrderEntity::getMaterialCode).collect(Collectors.toList()));
        Map<String, MaterialEntity> materialCodeMap = materials.stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));
        result.forEach(e -> {
            MaterialEntity materialEntity = materialCodeMap.get(e.getMaterialCode());
            e.setMaterialName(Optional.ofNullable(materialEntity).map(MaterialEntity::getName).orElse(null));
        });
        return result;
    }

    @Override
    public WorkOrderEntity getWorkOrderByNumberForAppScan(String workOrderNumber) {
        Integer[] states = {2, 3, 4, 5};
        List<Integer> integers = Arrays.asList(states);
        return this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).in(WorkOrderEntity::getState, integers).one();
    }

    @Override
    public List<BatchGenerateCodeDTO> generateCodeList(BatchGenerateCodeReqDTO dto) {
        List<String> orderNumbers = dto.getOrderNumbers();
        if (CollectionUtils.isEmpty(orderNumbers)) {
            throw new ResponseException("单号列表不能为空");
        }
        // 查询流水码生成数量
        Map<String, Double> map = productFlowCodeService.generateCount(dto)
                .stream().collect(Collectors.toMap(BatchGenerateCodeDTO::getOrderNumber, BatchGenerateCodeDTO::getExistQuantity, Double::sum));

        Map<String, WorkOrderEntity> entityMap = this.lambdaQuery()
                .in(WorkOrderEntity::getWorkOrderNumber, orderNumbers)
                .list()
                .stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, v -> v));
        List<BatchGenerateCodeDTO> results = new ArrayList<>();
        for (String orderNumber : orderNumbers) {
            WorkOrderEntity entity = entityMap.get(orderNumber);
            if (entity == null) {
                log.info("未找到单据信息，单号：" + orderNumber);
                continue;
            }
            Double existQuantity = map.getOrDefault(entity.getWorkOrderNumber(), 0.0);
            if (existQuantity == null) {
                existQuantity = 0.0;
            }
            double quantity = MathUtil.sub(entity.getPlanQuantity(), existQuantity);
            if (quantity < 0) {
                quantity = 0.0;
            }
            results.add(BatchGenerateCodeDTO.builder().orderNumber(entity.getWorkOrderNumber())
                    .materialCode(entity.getMaterialCode())
                    .skuId(entity.getSkuId())
                    .planQuantity(entity.getPlanQuantity())
                    .existQuantity(existQuantity)
                    .quantity(quantity)
                    .build()
            );
        }
        return results;
    }

    @Override
    public void judgeBeforePrint(WorkOrderSelectDTO selectDTO) {
        long count;
        // 条件查询
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(selectDTO.getWorkOrderNumber())) {
            List<String> workOrderNumbers = Arrays.asList(selectDTO.getWorkOrderNumber().split(Constant.SEP));
            count = this.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers).eq(WorkOrderEntity::getIsPrint, true).count();
        } else {
            conditionQuery(selectDTO, wrapper);
            // 如果存在已打印的数据，抛出异常
            wrapper.eq(WorkOrderEntity::getIsPrint, true);
            count = this.count(wrapper);
        }
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.FOUND_PRINTED_DATA);
        }
    }

    /**
     * 获取工单列表
     */
    private List<WorkOrderEntity> getWorkOrderEntities(WorkOrderSelectDTO selectDTO) {
        List<WorkOrderEntity> workOrderEntities;
        List<String> workOrderNumbers;
        if (Objects.nonNull(selectDTO.getIsSelectAllPrint()) && selectDTO.getIsSelectAllPrint()) {
            selectDTO.setIsShowSimpleInfo(true);
            Page<WorkOrderEntity> page = this.getWorkOrderEntityPage(selectDTO, null);
            workOrderEntities = page.getRecords();
        } else {
            workOrderNumbers = Arrays.asList(selectDTO.getWorkOrderNumber().split(Constant.SEP));
            workOrderEntities = this.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers).list();
        }

        if (CollectionUtils.isEmpty(workOrderEntities)) {
            throw new ResponseException(RespCodeEnum.PRINT_DATA_IS_LOSE);
        }
        return workOrderEntities;
    }

    /**
     * 自动创建流水码
     *
     * @param workOrderEntities
     */
    @Async
    @Override
    public void autoGenerate(List<WorkOrderEntity> workOrderEntities) {
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return;
        }
        List<BatchGenerateCodeDTO> list = new ArrayList<>();
        // 获取流水码自动创建配置
        FullPathCodeDTO dto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.PRODUCT_FLOW_CODE_AUTO_CREATE_CONFIG).build();
        ProductFlowCodeConfigDTO configDTO = businessConfigService.getValueDto(dto, ProductFlowCodeConfigDTO.class);
        // 未启用自动创建 || 规则未配置 || 原单状态未包含该单据状态，则直接返回
        if (configDTO == null || !configDTO.getEnable() || configDTO.getRuleId() == null) {
            log.info("生产流水码自动创建不符合条件");
            return;
        }
        for (WorkOrderEntity entity : workOrderEntities) {
            // 原单状态未包含该单据状态，则直接返回
            if (!configDTO.getOriginalOrderStates().contains(entity.getState())) {
                log.info("生产流水码自动创建不符合条件");
                continue;
            }
            // 设置制造单元模型编号
            if (entity.getLineId() != null && StringUtils.isBlank(entity.getLineModelCode())) {
                ProductionLineEntity productionLineEntity = productionLineMapper.selectById(entity.getLineId());
                if (productionLineEntity != null) {
                    ModelEntity modelEntity = modelService.getById(productionLineEntity.getModelId());
                    entity.setLineModelCode(modelEntity == null ? null : modelEntity.getCode());
                }
            }

            list.add(BatchGenerateCodeDTO.builder().orderNumber(entity.getWorkOrderNumber())
                    .materialCode(entity.getMaterialCode())
                    .skuId(entity.getSkuId())
                    .planQuantity(entity.getPlanQuantity())
                    .ruleId(configDTO.getRuleId())
                    .type(ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode())
                    .userName(entity.getUpdateBy())
                    .lineCode(entity.getLineCode())
                    .lineModelCode(entity.getLineModelCode()).build());
        }
        productFlowCodeService.autoGenerate(list);
    }

    @Override
    public RelevanceResourceDTO getWorkOrderRelevanceResourceList(String workOrder) {
        RelevanceResourceDTO resourceDTO = RelevanceResourceDTO.builder().build();
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrder);
        if (workOrderEntity != null) {
            WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getWorkCenterId());
            if (workCenterEntity == null) {
                return resourceDTO;
            }
            if (WorkCenterTypeEnum.LINE.getCode().equals(workCenterEntity.getRelevanceType())) {
                List<ProductionLineEntity> relevanceLine = workCenterService.getRelevanceLines(workCenterEntity.getId());
                if (CollectionUtils.isNotEmpty(relevanceLine)) {
                    resourceDTO.setType(WorkCenterTypeEnum.LINE.getCode());
                    resourceDTO.setLineEntities(relevanceLine);
                }
            } else if (WorkCenterTypeEnum.TEAM.getCode().equals(workCenterEntity.getRelevanceType())) {
                List<SysTeamEntity> relevanceTeam = workCenterService.getRelevanceTeams(workCenterEntity.getId());
                if (CollectionUtils.isNotEmpty(relevanceTeam)) {
                    resourceDTO.setType(WorkCenterTypeEnum.TEAM.getCode());
                    resourceDTO.setTeamEntities(relevanceTeam);
                }
            } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterEntity.getRelevanceType())) {
                List<DeviceEntity> relevanceDevice = workCenterService.getRelevanceDevice(workCenterEntity.getId());
                if (CollectionUtils.isNotEmpty(relevanceDevice)) {
                    resourceDTO.setType(WorkCenterTypeEnum.DEVICE.getCode());
                    resourceDTO.setDeviceEntities(relevanceDevice);
                }
            } else {
                return resourceDTO;
            }
        }
        return resourceDTO;
    }

    @Override
    public Page<WorkOrderEntity> getWorkOrderPageByOpenApi(WorkOrderOpenSelectExtendDTO selectExtendDTO) {
        LambdaQueryWrapper<WorkOrderEntity> wrapper = new LambdaQueryWrapper<>();
        // 条件查询
        WorkOrderSelectDTO workOrderSelectDTO = JacksonUtil.convertObject(selectExtendDTO, WorkOrderSelectDTO.class);
        conditionQuery(workOrderSelectDTO, wrapper);

        if (StringUtils.isNotBlank(selectExtendDTO.getUsername()) &&
                selectExtendDTO.getIsDataIsolation() != null && selectExtendDTO.getIsDataIsolation()) {
            // 数据隔离，只能查询角色绑定工作中心下的工单
            Boolean hasPermission = isolationService.dataIsolation(selectExtendDTO.getUsername(), wrapper);
            if (!hasPermission) {
                throw new ResponseException(RespCodeEnum.NO_WORK_CENTER_PERMISSION);
            }
        }

        Integer current = workOrderSelectDTO.getCurrent();
        Integer size = workOrderSelectDTO.getSize();
        Page<WorkOrderEntity> page = new Page<>();
        if (current == null || size == null) {
            List<WorkOrderEntity> list = list(wrapper);
            page.setRecords(list).setTotal(list.size()).setCurrent(1).setSize(list.size());
        } else {
            page = page(new Page<>(current, size), wrapper);
        }
        // 为空，直接返回
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        // 为提升查询效率，如果仅查询简单信息，直接返回
        if (Objects.nonNull(selectExtendDTO.getIsShowSimpleInfo()) && selectExtendDTO.getIsShowSimpleInfo()) {
            return page;
        }
        // 获取用户相关中文名称
        Map<String, String> nickNames = new HashMap<>();
        Map<String, String> signatureUrlMap = new HashMap<>();
        if (Objects.nonNull(selectExtendDTO.getIsShowUserInfo()) && selectExtendDTO.getIsShowUserInfo()) {
            List<SysUserEntity> sysUserEntities = userService.selectList();
            nickNames = sysUserEntities.stream().collect(Collectors.toMap(SysUserEntity::getUsername, SysUserEntity::getNickname));
            signatureUrlMap = userService.getSignatureUrlMap(null);
        }
        // 班组名称
        Set<Integer> teamIdSet = page.getRecords().stream().map(WorkOrderEntity::getTeamId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, String> teamIdNameMap = CollectionUtils.isEmpty(teamIdSet) ? new HashMap<>(4) : sysTeamService.listByIds(teamIdSet).stream().collect(Collectors.toMap(SysTeamEntity::getId, SysTeamEntity::getTeamName));
        Set<Integer> workCenterIdSet = page.getRecords().stream().map(WorkOrderEntity::getWorkCenterId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, WorkCenterEntity> workCenterIdEntityMap = CollectionUtils.isEmpty(workCenterIdSet) ? new HashMap<>(4) : workCenterMapper.selectBatchIds(workCenterIdSet).stream().collect(Collectors.toMap(WorkCenterEntity::getId, v -> v));
        Set<Integer> deviceIdSet = page.getRecords().stream().map(WorkOrderEntity::getDeviceId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, String> deviceIdNameMap = CollectionUtils.isEmpty(deviceIdSet) ? new HashMap<>(4) : deviceService.listByIds(deviceIdSet).stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceName));
        Map<Integer, String> deviceIdCodeMap = CollectionUtils.isEmpty(deviceIdSet) ? new HashMap<>(4) : deviceService.listByIds(deviceIdSet).stream().collect(Collectors.toMap(DeviceEntity::getDeviceId, DeviceEntity::getDeviceCode));

        // 制造单元id
        Set<Integer> lineIdSet = page.getRecords().stream().map(WorkOrderEntity::getLineId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, ProductionLineEntity> lineIdEntityMap = CollectionUtils.isEmpty(lineIdSet) ? new HashMap<>() : productionLineMapper.selectBatchIds(lineIdSet).stream().collect(Collectors.toMap(ProductionLineEntity::getProductionLineId, v -> v));
        // 查询工单关联的工艺信息
        List<Integer> craftIds = page.getRecords().stream().map(WorkOrderEntity::getCraftId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, String> relateCraftInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(craftIds)) {
            List<CraftEntity> craftEntities = craftService.listByIds(craftIds);
            // 根据craftId分组,如果isTemplate字段为true代表 "关联工艺模板"，为false代表"关联工艺"，为null代表 "无工艺配置"
            relateCraftInfoMap = craftEntities.stream()
                    .collect(Collectors.toMap(CraftEntity::getCraftId, craftEntity -> craftEntity.getIsTemplate() ? "关联工艺模板" : "关联工艺"));
        }

        for (WorkOrderEntity entity : page.getRecords()) {
            OrderTypeItemEntity orderTypeEntity = orderTypeItemService.lambdaQuery()
                    .eq(OrderTypeItemEntity::getBusinessTypeCode, entity.getBusinessType())
                    .eq(OrderTypeItemEntity::getCode, entity.getOrderType())
                    .one();
            entity.setOrderTypeName(orderTypeEntity.getName());
            entity.setStateName(WorkOrderStateEnum.getNameByCode(entity.getState()));
            entity.setAssignmentStateName(AssignmentStateEnum.getNameByType(entity.getAssignmentState()));
            //展示工单执行状态
            entity.setExecutionStatusName(WorkOrderExecutionStateEnum.getNameByCode(entity.getExecutionStatus()));
            entity.setInvestCheckResultName(InvestCheckResultEnum.getNameByCode(entity.getInvestCheckResult()));
            // 查询工艺工序
            if (Objects.nonNull(selectExtendDTO.getIsShowCraftProcedureInfo()) && selectExtendDTO.getIsShowCraftProcedureInfo()) {
                setCraftProcedure(entity);
            }
            // 设置入库数量
//            if (Objects.isNull(selectExtendDTO.getIsShowWarehouseInfo()) || selectExtendDTO.getIsShowWarehouseInfo()) {
//                entity.setInputCount(getInventoryQuantity(entity.getWorkOrderNumber(), entity.getMaterialCode()));
            entity.setInputCount(entity.getInventoryQuantity());
//            }
            // 获取包装方案
            if (Objects.nonNull(selectExtendDTO.getIsShowPackageSchemeInfo()) && selectExtendDTO.getIsShowPackageSchemeInfo()) {
                PackageSchemeEntity packageSchemeEntity = packageSchemeService.getDetailByCode(entity.getPackageSchemeCode());
                entity.setPackageSchemeEntities(packageSchemeEntity == null ? new ArrayList<>() : Stream.of(packageSchemeEntity).collect(Collectors.toList()));
            }
            // 展示审核状态名称
            entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
            entity.setActualApproverName(nickNames.getOrDefault(entity.getActualApprover(), entity.getActualApprover()));
            entity.setActualApproverSignatureUrl(signatureUrlMap.get(entity.getActualApprover()));
            entity.setApproverName(nickNames.getOrDefault(entity.getApprover(), entity.getApprover()));
            entity.setCreateName(nickNames.getOrDefault(entity.getCreateBy(), entity.getCreateBy()));
            //设置物料字段
            if (Objects.nonNull(selectExtendDTO.getIsShowMaterialFieldInfo()) && selectExtendDTO.getIsShowMaterialFieldInfo()) {
                entity.setMaterialFields(materialService.getEntityByCodeAndSkuId(entity.getMaterialCode(), entity.getSkuId()));
            }
            //展示班组信息
            entity.setTeamName(teamIdNameMap.get(entity.getTeamId()));
            List<WorkOrderTeamEntity> workOrderTeams = workOrderTeamService.getWorkOrderTeams(entity.getWorkOrderNumber());
            entity.setWorkOrderTeamEntities(workOrderTeams);
            WorkCenterEntity workCenterEntity = workCenterIdEntityMap.get(entity.getWorkCenterId());
            //展示设备名称
            entity.setDeviceCode(deviceIdCodeMap.get(entity.getDeviceId()));
            entity.setDeviceName(deviceIdNameMap.get(entity.getDeviceId()));
            //拿到工作中心type
            entity.setWorkCenterType(workCenterEntity == null ? null : workCenterEntity.getType());
            entity.setWorkCenterTypeName(workCenterEntity == null ? null : WorkCenterTypeEnum.getNameByCode(workCenterEntity.getType()));
            entity.setWorkCenterRelevanceType(workCenterEntity == null ? null : workCenterEntity.getRelevanceType());
            // 合格率
            double finishCount = entity.getFinishCount() == null ? 0 : entity.getFinishCount();
            double unqualified = entity.getUnqualified() == null ? 0 : entity.getUnqualified();
            if (finishCount == 0 && unqualified == 0) {
                entity.setPassRate(0.0);
            } else {
                entity.setPassRate(MathUtil.divideDouble(finishCount, unqualified + finishCount, 4));
            }
            // 展示制造单元模型编码
            if (entity.getLineId() != null) {
                ProductionLineEntity productionLineEntity = lineIdEntityMap.get(entity.getLineId());
                if (productionLineEntity != null) {
                    ModelEntity modelEntity = modelService.getById(productionLineEntity.getModelId());
                    entity.setLineModelCode(modelEntity == null ? null : modelEntity.getCode());
                    entity.setLineModelName(modelEntity == null ? null : modelEntity.getName());
                }
            }
            // 如果工作中心为制造单元/班组/设备，则基本生产单元为制造单元实例/班组实例/设备实例
            setProductionBasicUnit(entity);
            // 工艺信息应该读取工单本身关联的信息，而不是物料关联的工艺信息
            entity.setHaveCraft(relateCraftInfoMap.getOrDefault(entity.getCraftId(), "无工艺配置"));
        }
        // 单件理论工时
        if (Objects.nonNull(selectExtendDTO.getIsShowPlanTheoryHourInfo()) && selectExtendDTO.getIsShowPlanTheoryHourInfo()) {
            Map<String, Double> theoryHourMap = workOrderTheoryHourMap(page.getRecords());
            page.getRecords().forEach(e -> {
                Double theoryHour = theoryHourMap.get(e.getWorkOrderNumber());
                e.setTheoryHour(theoryHour);
                e.setProduceTheoryHour(NullableDouble.of(theoryHour).mul(e.getFinishCount()).scale(2).cal());
                e.setPlanTheoryHour(NullableDouble.of(theoryHour).mul(e.getPlanQuantity()).scale(2).cal());
                e.setCapacityUnitName((String) redisTemplate.opsForValue().get(RedisKeyPrefix.WORK_ORDER_CAPACITY_UNIT_NAME_ + e.getWorkOrderNumber()));
            });
        }
        return page;
    }

    @Override
    public CheckReportBackDTO quantityVerify(List<WorkOrderQuantityVerifyDTO> quantityVerifyDTOS) {
        CheckReportBackDTO checkBackDTO = CheckReportBackDTO.builder().isLimit(false).isTips(false).build();
        // 改成获取下推数量校验配置
        Page<AbstractOrderPushDownItemVO> page = orderPushDownItemService.getPage(PushDownItemSelectDTO.builder()
                .enable(true)
                .code(PushDownItemVerifyEnum.PRODUCT_ORDER_TO_WORK_ORDER_NUM_LIMIT.getCode())
                .configPath(PushDownItemVerifyEnum.PRODUCT_ORDER_TO_WORK_ORDER_NUM_LIMIT.getConfigPath())
                .type(PushDownItemTypeEnum.VERIFY)
                .build());
        List<AbstractOrderPushDownItemVO> pushDownItemVOS = page.getRecords();
        if (CollectionUtils.isEmpty(pushDownItemVOS)) {
            return checkBackDTO;
        }
        // 数量检验方法：关联同一生产订单，最后一道工序的生产工单的计划数量之和；无工序的工单，按最后一道工序处理。
        for (WorkOrderQuantityVerifyDTO quantityVerifyDTO : quantityVerifyDTOS) {
            // 如果为页面新增工单，非关联最后一道工序则不进行校验
            if (CollectionUtils.isNotEmpty(quantityVerifyDTO.getCraftProcedureEntities())) {
                // 获取关联工艺工序的最后一道工序
                CraftProcedureEntity lastCraftProcedureEntity = craftProcedureService.getLastCraftProcedureEntity(quantityVerifyDTO.getCraftProcedureEntities());
                // 如果不是工艺下的最后一道工序，则直接返回
                boolean isLastProcedure = lastCraftProcedureEntity.getIsLastProcedure().equals(Constant.YES);
                if (!isLastProcedure) {
                    return checkBackDTO;
                }
            }
            // 获取所有关联生产订单号的非取消状态的工单
            List<WorkOrderEntity> originWorkOrders = this.lambdaQuery()
                    .ne(WorkOrderEntity::getState, WorkOrderStateEnum.CANCELED.getCode())
                    .eq(WorkOrderEntity::getProductOrderNumber, quantityVerifyDTO.getProductOrderNumber())
                    .list();
            List<WorkOrderEntity> workOrders = originWorkOrders.stream().filter(e -> !Objects.equals(quantityVerifyDTO.getWorkOrderNumber(), e.getWorkOrderNumber())).collect(Collectors.toList());
            double noProcedureWorkOrderPlanQuantitySum = 0;
            double lastProcedureWorkOrderPlanQuantitySum = 0;
            if (CollectionUtils.isNotEmpty(workOrders)) {
                List<String> workOrderNumbers = workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
                List<WorkOrderProcedureRelationEntity> procedureRelationEntities = workOrderProcedureRelationService.lambdaQuery()
                        .in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumbers)
                        .list();
                // 获取工序未空的生产工单计划总数（当作最后一道工序处理）
                List<String> hasProcedureWorkOrderNumbers = procedureRelationEntities.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
                noProcedureWorkOrderPlanQuantitySum = workOrders.stream()
                        .filter(o -> !hasProcedureWorkOrderNumbers.contains(o.getWorkOrderNumber()))
                        .mapToDouble(WorkOrderEntity::getPlanQuantity).sum();

                // 求和同工序下的计划数量
                Map<Integer, List<String>> sameProcedures = procedureRelationEntities
                        .stream().collect(Collectors.groupingBy(WorkOrderProcedureRelationEntity::getCraftProcedureId,
                                Collectors.mapping(WorkOrderProcedureRelationEntity::getWorkOrderNumber, Collectors.toList())));
                // 获取所有绑定了最后一道工序的工单
                for (Map.Entry<Integer, List<String>> entry : sameProcedures.entrySet()) {
                    Integer craftProcedureId = entry.getKey();
                    CraftProcedureEntity craftProcedureEntity = craftProcedureService.lambdaQuery().eq(CraftProcedureEntity::getId, craftProcedureId).one();
                    boolean isLastProcedure = craftProcedureEntity.getIsLastProcedure().equals(Constant.YES);
                    if (!isLastProcedure) {
                        continue;
                    }
                    List<String> bindLastProcedureWorkOrderNumbers = entry.getValue();
                    lastProcedureWorkOrderPlanQuantitySum += workOrders.stream()
                            .filter(o -> bindLastProcedureWorkOrderNumbers.contains(o.getWorkOrderNumber()))
                            .mapToDouble(WorkOrderEntity::getPlanQuantity).sum();
                }
            }

            // 总数 = 历史关联工序的工单物料总数 + 无工序的工单 + 本次单据添加的数量
            double sum = lastProcedureWorkOrderPlanQuantitySum + noProcedureWorkOrderPlanQuantitySum + quantityVerifyDTO.getAddQuantity();
            if (quantityVerifyDTO.getProductOrderPlanQuantity() < sum) {
                checkBackDTO.setIsTips(true);
                checkBackDTO.setTipsMessage(pushDownItemVOS.get(0).getDescription());
                return checkBackDTO;
            }
        }
        return checkBackDTO;
    }

    @Override
    public void uploadListExportTemplate(MultipartFile file, String username) throws IOException {
        EasyExcelUtil.isValidExcelFile(file);
        MultipartFile modifiedFile = new MockMultipartFile(
                file.getName(),
                file.getOriginalFilename(),
                file.getContentType(),
                EasyExcelUtil.clearAndConvertToBytes(file, "数据源"));
        modelUploadFileService.uploadReferencedFile(modifiedFile, ModelUploadFileEnum.WORK_ORDER_EXPORT.getCode(), username);
    }

    @Override
    public Long exportTask(WorkOrderSelectDTO workOrderSelectDTO, String username) {
        DataExportParam<WorkOrderSelectDTO> dataExportParam = new DataExportParam<>();
        dataExportParam.setExportFileName(BusinessCodeEnum.WORK_ORDER_RECORD.getCodeName());
        dataExportParam.setLimit(10000);
        dataExportParam.setBusinessCode(BusinessCodeEnum.WORK_ORDER_RECORD.name());
        dataExportParam.setCreateUserCode(username);
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(WorkOrderSelectDTO.class.getName(), workOrderSelectDTO);
        parameters.put("cleanSheetNames", "数据源");
        parameters.put("templateId", workOrderSelectDTO.getTemplateId());
        parameters.put("workOrder", "数据源");
        dataExportParam.setParameters(parameters);
        return excelService.doExport(dataExportParam, WorkOrderListExportHandler.class);

    }

    @Override
    public IPage<ExcelTask> taskPage(Integer currentPage, Integer pageSize) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setBusinessCode(BusinessCodeEnum.WORK_ORDER_RECORD.name());
        return excelService.listPage(excelTask, currentPage, pageSize);
    }

    @Override
    public ExcelTask taskById(Long taskId) {
        ExcelTask excelTask = new ExcelTask();
        excelTask.setId(taskId);
        excelTask.setBusinessCode(BusinessCodeEnum.WORK_ORDER_RECORD.name());
        IPage<ExcelTask> excelTaskIPage = excelService.listPage(excelTask, 1, 1);
        List<ExcelTask> records = excelTaskIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            throw new ResponseException("没有查询到对应的导出任务详情");
        }
        return records.get(0);
    }

    @Override
    public void updateByIdAndDefectConfig(WorkOrderEntity workOrderEntity, @NotNull WorkOrderUnqualifiedSourceEnum sourceEnum) {
        FullPathCodeDTO configDto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.QUALITY_DEFECT_CONFIG).build();
        DefectConfigDTO defectConfig = businessConfigService.getValueDto(configDto, DefectConfigDTO.class);
        List<Integer> source = defectConfig.getWorkOrderUnqualifiedSource();
        //存在配置但不包含 （如不存在配置则按照原先的逻辑）
        if (source != null && !source.contains(sourceEnum.getCode())) {
            workOrderEntity.setUnqualified(null);
        }
        // 如果更新时只有id有值，将导致sql错误
        if (!FieldUtil.isAllFieldNull(workOrderEntity, true, true)) {
            this.updateById(workOrderEntity);
        }
    }

    @Override
    public void buildWrapper(LambdaQueryWrapper<WorkOrderEntity> wrapper, WorkOrderWrapperDTO dto) {
        if (StringUtils.isNotBlank(dto.getProductOrderNumber())) {
            wrapper.like(WorkOrderEntity::getProductOrderNumber, dto.getProductOrderNumber());
        }
        List<Integer> workCenterIds = WorkOrderWrapperDTO.parseIdToList(dto.getWorkCenterIds());
        if (CollectionUtils.isNotEmpty(workCenterIds)) {
            wrapper.in(WorkOrderEntity::getWorkCenterId, workCenterIds);
        }
        List<Integer> procedureIds = WorkOrderWrapperDTO.parseIdToList(dto.getProcedureIds());
        if (CollectionUtils.isNotEmpty(procedureIds)) {
            List<WorkOrderProcedureRelationEntity> workOrderProcedureRelations = workOrderProcedureRelationService.lambdaQuery()
                    .in(WorkOrderProcedureRelationEntity::getProcedureId, procedureIds)
                    .list();
            if (CollectionUtils.isEmpty(workOrderProcedureRelations)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
                return;
            } else {
                List<String> workOrderNumbers = workOrderProcedureRelations.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
                wrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
            }
        }
        List<Integer> lineIds = WorkOrderWrapperDTO.parseIdToList(dto.getLineIds());
        if (CollectionUtils.isNotEmpty(lineIds)) {
            wrapper.in(WorkOrderEntity::getLineId, lineIds);
        }
        List<Integer> teamIds = WorkOrderWrapperDTO.parseIdToList(dto.getTeamIds());
        if (CollectionUtils.isNotEmpty(teamIds)) {
            List<String> lineWorkOrderNumbers = basicUnitRelationService.queryWorkOrderNumbers(WorkOrderBasicUnitQueryDTO.buildTeam(teamIds, null));
            if (CollectionUtils.isEmpty(lineWorkOrderNumbers)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
                return;
            }
            wrapper.in(CollectionUtils.isNotEmpty(lineWorkOrderNumbers), WorkOrderEntity::getWorkOrderNumber, lineWorkOrderNumbers);
        }
        List<Integer> deviceIds = WorkOrderWrapperDTO.parseIdToList(dto.getDeviceIds());
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            List<String> deviceWorkOrderNumbers = basicUnitRelationService.queryWorkOrderNumbers(WorkOrderBasicUnitQueryDTO.buildDevice(deviceIds, null));
            if (CollectionUtils.isEmpty(deviceWorkOrderNumbers)) {
                wrapper.isNull(WorkOrderEntity::getWorkOrderNumber);
                return;
            }
            wrapper.in(CollectionUtils.isNotEmpty(deviceWorkOrderNumbers), WorkOrderEntity::getWorkOrderNumber, deviceWorkOrderNumbers);
        }
    }

    @Override
    public void showWorkOrdersExtraName(List<WorkOrderEntity> workOrders, ShowExtraNameDTO dto) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return;
        }
        List<String> workOrderNumbers = workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        // 班组
        Map<String, List<WorkOrderBasicUnitRelationEntity>> groupTeamRelations = Collections.emptyMap();
        if (Boolean.TRUE.equals(dto.getTeam())) {
            groupTeamRelations = basicUnitRelationService.groupTeamRelations(workOrderNumbers);
        }
        // 设备
        Map<String, List<WorkOrderBasicUnitRelationEntity>> groupDeviceRelations = Collections.emptyMap();
        if (Boolean.TRUE.equals(dto.getDevice())) {
            groupDeviceRelations = basicUnitRelationService.groupDeviceRelations(workOrderNumbers);
        }
        // 工艺
        List<Integer> craftIds = !Boolean.TRUE.equals(dto.getCraft()) ? Collections.emptyList() : workOrders.stream().map(WorkOrderEntity::getCraftId).collect(Collectors.toList());
        List<CraftEntity> crafts = CollUtil.isEmpty(craftIds) ? Collections.emptyList() : craftService.listByIds(craftIds);
        Map<Integer, CraftEntity> craftIdMap = crafts.stream().collect(Collectors.toMap(CraftEntity::getCraftId, Function.identity()));

        // 工艺工序
        List<String> craftProcedureWorkOrderNumbers = !Boolean.TRUE.equals(dto.getProcedure()) ? Collections.emptyList() : workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        Map<String, List<CraftProcedureEntity>> workOrderNumberProceduresMap = workOrderProcedureRelationService.workOrderCraftProceduresGroup(craftProcedureWorkOrderNumbers);
        // 工作中心类型
        List<Integer> workCenterIds = !Boolean.TRUE.equals(dto.getWorkCenterType()) ? Collections.emptyList() : workOrders.stream().map(WorkOrderEntity::getWorkCenterId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, WorkCenterEntity> workCenterIdMap = CollectionUtils.isEmpty(workCenterIds) ? Maps.newHashMap() : workCenterService.listByIds(workCenterIds).stream().collect(Collectors.toMap(WorkCenterEntity::getId, Function.identity()));

        // 物料
        List<String> materialCodes = !Boolean.TRUE.equals(dto.getMaterial()) ? Collections.emptyList() : workOrders.stream().map(WorkOrderEntity::getMaterialCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, MaterialEntity> materialCodeMap = CollectionUtils.isEmpty(materialCodes) ? Maps.newHashMap() : materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list().stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));

        // 组装数据
        for (WorkOrderEntity workOrder : workOrders) {
            if (WorkCenterTypeEnum.TEAM.getCode().equals(workOrder.getWorkCenterType())) {
                workOrder.setProductBasicUnits(groupTeamRelations.get(workOrder.getWorkOrderNumber()));
            } else if (WorkCenterTypeEnum.DEVICE.getCode().equals(workOrder.getWorkCenterType())) {
                workOrder.setProductBasicUnits(groupDeviceRelations.get(workOrder.getWorkOrderNumber()));
            } else if (WorkCenterTypeEnum.LINE.getCode().equals(workOrder.getWorkCenterType()) && workOrder.getLineId() != null) {
                // 产线直接带出来就行
                workOrder.setProductBasicUnits(Collections.singletonList(
                        WorkOrderBasicUnitRelationEntity.builder()
                                .workOrderNumber(workOrder.getWorkOrderNumber())
                                .workCenterId(workOrder.getWorkCenterId())
                                .workCenterType(workOrder.getWorkCenterType())
                                .productionBasicUnitId(workOrder.getLineId())
                                .productionBasicUnitName(workOrder.getLineName())
                                .isolationId(workOrder.getWorkCenterId() + Constants.CROSSBAR + workOrder.getLineId())
                                .build())
                );
            }
            MaterialEntity material = materialCodeMap.get(workOrder.getMaterialCode());
            workOrder.setMaterialFields(material);
            workOrder.setMaterialName(material == null ? null : material.getName());
            List<CraftProcedureEntity> craftProcedures = workOrderNumberProceduresMap.get(workOrder.getWorkOrderNumber());

            if (CollectionUtils.isNotEmpty(craftProcedures)) {
                String procedureIds = craftProcedures.stream().map(CraftProcedureEntity::getProcedureId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(Constant.SEP));
                String procedureName = craftProcedures.stream().map(CraftProcedureEntity::getProcedureName).filter(Objects::nonNull).collect(Collectors.joining(Constant.SEP));
                String procedureAlias = craftProcedures.stream().map(CraftProcedureEntity::getAlias).filter(Objects::nonNull).collect(Collectors.joining(Constant.SEP));
                workOrder.setProcedureIds(procedureIds);
                workOrder.setProcedureName(procedureName);
                workOrder.setProcedureAlias(procedureAlias);
                workOrder.setCraftProcedureEntities(craftProcedures);
            }
            WorkCenterEntity workCenter = workCenterIdMap.get(workOrder.getWorkCenterId());
            if (workCenter != null) {
                String workCenterType = workCenter.getType();
                workOrder.setWorkCenterType(workCenterType);
                workOrder.setWorkCenterTypeName(WorkCenterTypeEnum.getNameByCode(workCenterType));
            }
            CraftEntity craft = craftIdMap.get(workOrder.getCraftId());
            if (craft != null) {
                workOrder.setCraftName(craft.getName());
            }
        }

    }

    @Override
    public List<WorkOrderEntity> batchEditVerify(WorkOrderSelectDTO workOrderSelectDTO) {
        workOrderSelectDTO.setIsShowSimpleInfo(true);
        Page<WorkOrderEntity> page = getWorkOrderEntityPage(workOrderSelectDTO, null);
        List<Integer> states = page.getRecords().stream().map(WorkOrderEntity::getState).distinct().collect(Collectors.toList());
        if (states.size() != 1) {
            throw new ResponseException(RespCodeEnum.MUST_BE_IN_THE_SAME_STATE_TO_MODIFY);
        }
        return page.getRecords();

    }

    /**
     * 删除工单前的判断
     * 工单只要在生产中或者生产完成（投产/挂起/完成），完成数量finishCount>0 || 投入数量inputCount>0 || （存在扫码记录，需要先删除流水码，才能删除工单）时，
     * 提示"该工单在生产中，是否删除"，其他情况允许直接删除
     */
    @Override
    public void judgeBeforeDelete(String workOrderNumber) {
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity.getFinishCount() > 0 || workOrderEntity.getInputTotal() > 0) {
            List<Integer> states = Arrays.asList(WorkOrderStateEnum.INVESTMENT.getCode(), WorkOrderStateEnum.HANG_UP.getCode(), WorkOrderStateEnum.FINISHED.getCode());
            if (states.contains(workOrderEntity.getState())) {
                throw new ResponseException(RespCodeEnum.WORK_ORDER_IS_PRODUCING);
            }
        }
        // 如果存在流水码扫码记录，也不应该删除
        LambdaQueryWrapper<ProductFlowCodeRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductFlowCodeRecordEntity::getRelationNumber, workOrderNumber)
                .eq(ProductFlowCodeRecordEntity::getType, ProductFlowCodeTypeEnum.PRODUCT_FLOW_CODE.getCode());
        Long count = productFlowCodeRecordMapper.selectCount(wrapper);
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_IS_PRODUCING);
        }
    }

    @Override
    public List<PrintSourceOrderVO> getNumberListByWorkOrderNumber(String workOrderNumber) {
        return this.baseMapper.getNumberListByWorkOrderNumber(workOrderNumber);
    }

    @Override
    public List<String> getWorkerNumbersByProcedureName(String workOrderNumber, String procedureIds) {
        List<String> workOrderNumbers = new ArrayList<>();
        workOrderNumbers.add(workOrderNumber);
        WorkOrderEntity one = this.lambdaQuery().select(WorkOrderEntity::getProductOrderNumber).eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).one();
        if (Objects.isNull(one) || Objects.isNull(procedureIds) || "[]".equals(procedureIds)) {
            return workOrderNumbers;
        }
        List<WorkOrderEntity> workOrderEntities = this.lambdaQuery().eq(StringUtils.isNotBlank(one.getProductOrderNumber()), WorkOrderEntity::getProductOrderNumber, one.getProductOrderNumber()).list();
        if (CollectionUtils.isEmpty(workOrderEntities)) {
            return workOrderNumbers;
        }
        //获取配置工序的id
        String substring = procedureIds.substring(1, procedureIds.length() - 1);
        List<Integer> collect = Arrays.stream(substring.split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            List<Integer> procedureNameIds = workOrderProcedureRelationService.getProcedureIdsByWorkerOrderId(workOrderEntity.getWorkOrderId());
            if (CollectionUtils.isNotEmpty(procedureNameIds)) {
                for (Integer e : procedureNameIds) {
                    if (collect.contains(e)) {
                        workOrderNumbers.add(workOrderEntity.getWorkOrderNumber());
                    }
                }
            }
        }
        return workOrderNumbers;
    }

    @Override
    public List<WorkOrderEntity> filterLastProcedureWorkOrders(List<WorkOrderEntity> workOrders) {
        if (CollUtil.isNotEmpty(workOrders)) {
            List<String> workOrderNumbers = workOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
            // 查工艺工序
            List<WorkOrderProcedureRelationEntity> procedureRelations = workOrderProcedureRelationService.lambdaQuery().in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumbers).list();
            // 存在工序的工单号
            Set<String> exitsProcedureWorkOrderNumberSet = procedureRelations.stream().map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toSet());
            // 不存在工序的工单集合
            List<WorkOrderEntity> noExitsProcedureWorkOrders = workOrders.stream().filter(e -> !exitsProcedureWorkOrderNumberSet.contains(e.getWorkOrderNumber())).collect(Collectors.toList());
            List<WorkOrderEntity> lastProcedureWorkOrders = Collections.emptyList();
            if (CollUtil.isNotEmpty(procedureRelations)) {
                List<Integer> craftProcedureIds = procedureRelations.stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toList());
                List<CraftProcedureEntity> craftProcedures = craftProcedureService.listByIds(craftProcedureIds);
                // 最后一道工序的工艺工序id
                Set<Integer> lastCraftProcedureIdSet = craftProcedures.stream().filter(e -> Constant.YES.equals(e.getIsLastProcedure())).map(CraftProcedureEntity::getId).collect(Collectors.toSet());
                // 最后一道工序的工单
                Set<String> lastCraftProcedureWorkOrderNumberSet = procedureRelations.stream()
                        .filter(e -> lastCraftProcedureIdSet.contains(e.getCraftProcedureId()))
                        .map(WorkOrderProcedureRelationEntity::getWorkOrderNumber)
                        .collect(Collectors.toSet());
                // 最后一道工序
                lastProcedureWorkOrders = workOrders.stream().filter(e -> lastCraftProcedureWorkOrderNumberSet.contains(e.getWorkOrderNumber())).collect(Collectors.toList());
            }
            return Stream.concat(
                    noExitsProcedureWorkOrders.stream(),
                    lastProcedureWorkOrders.stream()
            ).collect(Collectors.toList());
        }
        return workOrders;
    }

    @Override
    public List<WorkOrderEntity> listByProductOrderOrderNumbers(Collection<String> productOrderNumbers) {
        return this.lambdaQuery()
                .in(WorkOrderEntity::getProductOrderNumber, productOrderNumbers)
                .notIn(WorkOrderEntity::getState, WorkOrderStateEnum.CANCELED.getCode())
                .list();
    }

    @Override
    public WorkOrderEntity materialCheckUpdate(WorkOrderEntity entity, String username) {
        this.lambdaUpdate()
                .eq(WorkOrderEntity::getWorkOrderNumber, entity.getWorkOrderNumber())
                .set(WorkOrderEntity::getMaterialCheckType, entity.getMaterialCheckType())
                .set(WorkOrderEntity::getRemark, entity.getRemark())
                .update();
        // 更新工单上料防错物料信息
        checkMaterialService.update(entity);
        return entity;
    }

    @Override
    public WorkOrderEntity selectMaterialCheck(Integer workOrderId) {
        WorkOrderEntity workOrderEntity = this.getById(workOrderId);
        if (workOrderEntity == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        showStateAndName(Stream.of(workOrderEntity).collect(Collectors.toList()));
        // 设置展示物料相关字段
        setMaterialFieldsForWorkOrderEntity(workOrderEntity);
        List<WorkOrderMaterialCheckMaterialEntity> checkMaterialList = checkMaterialService.getCheckMaterials(workOrderEntity);
        // 排序
        checkMaterialList.forEach(o -> {
            if (o.getSeq() == null) {
                o.setSeq(0);
            }
        });
        checkMaterialList.sort(Comparator.comparing(WorkOrderMaterialCheckMaterialEntity::getSeq)
                .thenComparing(WorkOrderMaterialCheckMaterialEntity::getId));
        workOrderEntity.setCheckMaterialList(checkMaterialList);
        return workOrderEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateWorkOrderMaterialCheck(BatchUpdateWorkOrderDTO dto, String username) {
        List<WorkOrderEntity> list = dto.getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new ResponseException("参数列表不能为空");
        }
        String materialCheckType = dto.getMaterialCheckType();
        if (StringUtils.isBlank(materialCheckType)) {
            throw new ResponseException("上料防错类型不能为空");
        }
        List<Integer> workOrderIds = list.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        LambdaUpdateWrapper<WorkOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(WorkOrderEntity::getWorkOrderId, workOrderIds)
                .set(WorkOrderEntity::getMaterialCheckType, materialCheckType);
        update(updateWrapper);
    }

    @Override
    public WorkOrderEntity selectMaterialCheck(Integer workOrderId, Integer craftProcedureId) {
        WorkOrderEntity workOrderEntity = this.getById(workOrderId);
        if (workOrderEntity == null) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_ID_SELECT_FAIL.fmtDes(workOrderId));
        }
        workOrderEntity.setMaterialCheckTypeName(MaterialMatchingEnum.getNameByType(workOrderEntity.getMaterialCheckType()));
        if (MaterialMatchingEnum.NO.getType().equals(workOrderEntity.getMaterialCheckType()) ||
                MaterialMatchingEnum.MATERIAL.getType().equals(workOrderEntity.getMaterialCheckType())) {
            return workOrderEntity;
        }
        List<WorkOrderMaterialCheckMaterialEntity> checkMaterialList = checkMaterialService.getCheckMaterials(workOrderEntity);
        if (MaterialMatchingEnum.PROCEDURE_MATERIAL.getType().equals(workOrderEntity.getMaterialCheckType())) {
            // 查询工序对应的工序物料
            List<ProcedureMaterialUsedEntity> procedureMaterials = procedureMaterialUsedService.lambdaQuery()
                    .eq(ProcedureMaterialUsedEntity::getProcedureId, craftProcedureId)
                    .list();
            if (CollectionUtils.isEmpty(procedureMaterials)) {
                throw new ResponseException("工序的工序物料为空");
            }
            List<String> materialCodeList = procedureMaterials.stream().map(ProcedureMaterialUsedEntity::getMaterialCode).collect(Collectors.toList());
            checkMaterialList = checkMaterialList.stream().filter(o -> materialCodeList.contains(o.getMaterialCode()) ||
                    materialCodeList.contains(o.getMainMaterialCode())).collect(Collectors.toList());
        }
        // 排序
        checkMaterialList.forEach(o -> {
            if (o.getSeq() == null) {
                o.setSeq(0);
            }
        });
        checkMaterialList.sort(Comparator.comparing(WorkOrderMaterialCheckMaterialEntity::getSeq)
                .thenComparing(WorkOrderMaterialCheckMaterialEntity::getId));
        workOrderEntity.setCheckMaterialList(checkMaterialList);
        return workOrderEntity;
    }

    @Override
    public List<String> batchUpdatePlaneTime(BatchUpdateWorkOrderDTO entity, String username) {
        if (StringUtils.isBlank(entity.getProductOrderNumbers())) {
            return new ArrayList<>();
        }
        List<String> collect = Arrays.stream(entity.getProductOrderNumbers().split(Constant.SEP)).map(String::new).collect(Collectors.toList());
        List<WorkOrderEntity> list = this.lambdaQuery().in(WorkOrderEntity::getProductOrderNumber, collect).list();
        //过滤出完成之前的工单，即需要修改的工单
        List<WorkOrderEntity> workOrderEntities = list.stream().filter(workOrderEntity -> workOrderEntity.getState() < WorkOrderStateEnum.FINISHED.getCode()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return new ArrayList<>();
        }
        //过滤掉不符合条件的
        List<Integer> workOrderIds = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderId).collect(Collectors.toList());
        List<String> inconsistent = new ArrayList<>(16);
        List<Integer> removeIds = new ArrayList<>(16);
        workOrderEntities.forEach(workOrderEntity -> {
            // 计划时间
            if (entity.getStartDate() != null) {
                Date endDate = entity.getEndDate() != null ? entity.getEndDate() : workOrderEntity.getEndDate();
                if (endDate != null && endDate.before(entity.getStartDate())) {
                    inconsistent.add("工单:" + workOrderEntity.getWorkOrderNumber() + " 结束时间需大于开始时间");
                    removeIds.add(workOrderEntity.getWorkOrderId());
                }
            }
            if (entity.getEndDate() != null) {
                Date startDate = entity.getStartDate() != null ? entity.getStartDate() : workOrderEntity.getStartDate();
                if (startDate != null && startDate.after(entity.getEndDate())) {
                    inconsistent.add("工单:" + workOrderEntity.getWorkOrderNumber() + " 开始时间需小于结束时间");
                    removeIds.add(workOrderEntity.getWorkOrderId());
                }
            }
        });
        workOrderIds.removeAll(removeIds);
        if (CollectionUtils.isNotEmpty(workOrderIds)) {
            this.lambdaUpdate().in(WorkOrderEntity::getWorkOrderId, workOrderIds)
                    .set(entity.getStartDate() != null, WorkOrderEntity::getStartDate, entity.getStartDate())
                    .set(entity.getEndDate() != null, WorkOrderEntity::getEndDate, entity.getEndDate())
                    .set(WorkOrderEntity::getUpdateBy, username)
                    .update();
        }
        return inconsistent;
    }

    @Override
    public Boolean judgeIsAutoReportRecord(String workOrderNumber) {
        FullPathCodeDTO autoDto = FullPathCodeDTO.builder()
                .fullPathCode(ConfigConstant.AUTO_REPORT_AFTER_FINISH_WORK).build();
        AutoReportAfterFinishWorkConfigDTO autoConfigDTO = businessConfigService.getValueDto(autoDto, AutoReportAfterFinishWorkConfigDTO.class);
        if (!autoConfigDTO.getEnable()) {
            return false;
        }
        ReportLineService reportLineService = SpringUtil.getBean(ReportLineService.class);
        // 允许自动补充报工记录条件
        // 1、没有报工记录
        // 2、配置中没有配置任何的工序或者工作中心 or 在配置工序和工作中心范围内
        double sum = reportLineService.lambdaQuery().eq(ReportLineEntity::getWorkOrder, workOrderNumber).list().stream().mapToDouble(ReportLineEntity::getFinishCount).sum();
        if (sum > 0) {
            return false;
        }
        WorkOrderEntity workOrderEntity = this.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber).one();
        if (CollectionUtils.isNotEmpty(autoConfigDTO.getEnableProcedure())) {
            List<WorkOrderProcedureRelationEntity> relationEntities = workOrderProcedureRelationService.lambdaQuery().eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumber).list();
            if (CollectionUtils.isEmpty(relationEntities)) {
                return false;
            }
            List<Integer> procedureIds = relationEntities.stream().map(WorkOrderProcedureRelationEntity::getProcedureId).collect(Collectors.toList());
            List<ProcedureEntity> procedureEntities = procedureService.listByIds(procedureIds);
            List<String> procedureCodes = procedureEntities.stream().map(ProcedureEntity::getProcedureCode).collect(Collectors.toList());
            for (String procedureCode : procedureCodes) {
                if (autoConfigDTO.getEnableProcedure().contains(procedureCode)) {
                    return true;
                }
            }
            return false;
        }
        if (CollectionUtils.isNotEmpty(autoConfigDTO.getEnableWorkCenter())) {
            return autoConfigDTO.getEnableWorkCenter().contains(workOrderEntity.getWorkCenterId());
        }
        return true;
    }

    @Override
    public List<WorkOrderEntity> getWorkOrderByFidProcedure(String productOrderNumber, Integer fid) {
        List<WorkOrderEntity> list = new ArrayList<>();
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(fid);
        if (facilitiesEntity == null) {
            throw new ResponseException("未找到工位");
        }
        // 查制造单元下正在投产的工单
        List<WorkOrderEntity> workOrderList = this.lambdaQuery()
                .eq(WorkOrderEntity::getProductOrderNumber, productOrderNumber)
                .eq(WorkOrderEntity::getLineId, facilitiesEntity.getProductionLineId())
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode()).list();
        List<String> workOrderNumbers = workOrderList.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return list;
        }
        // 工单关联的工序
        List<WorkOrderProcedureRelationEntity> procedureRelationEntities = workOrderProcedureRelationService.lambdaQuery()
                .in(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderNumbers)
                .list();
        if (CollectionUtils.isEmpty(procedureRelationEntities)) {
            return list;
        }
        List<Integer> craftProcedureIds = procedureRelationEntities.stream().map(WorkOrderProcedureRelationEntity::getCraftProcedureId).collect(Collectors.toList());
        // 工位类型名称
        String facType = modelService.getModelNameById(facilitiesEntity.getModelId());
        List<Integer> facCraftProcedureIds = craftProcedureService.lambdaQuery()
                .select(CraftProcedureEntity::getId)
                .in(CraftProcedureEntity::getId, craftProcedureIds)
                .eq(CraftProcedureEntity::getFacType, facType).list()
                .stream().map(CraftProcedureEntity::getId).collect(Collectors.toList());
        List<String> filterWorkOrderNumbers = procedureRelationEntities.stream().filter(o -> facCraftProcedureIds.contains(o.getCraftProcedureId()))
                .map(WorkOrderProcedureRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
        list = workOrderList.stream().filter(o -> filterWorkOrderNumbers.contains(o.getWorkOrderNumber())).collect(Collectors.toList());
        return list;
    }


    /**
     * 判断用户对工单是否有权限
     *
     * @param userName
     * @param workOrderNumber
     * @return
     */
    @Override
    public Boolean workOrderIsolation(String userName, String workOrderNumber) {
        WorkOrderEntity workOrderEntity = this.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            throw new ResponseException("无" + workOrderNumber + "对应工单");
        }
        return workOrderIsolation(userName, workOrderEntity);

    }

    private boolean workOrderIsolation(String userName, WorkOrderEntity workOrder) {
        IsolationDTO dto = isolationService.getIsolationDTO(userName);
        if (dto.getAllWorkCenter()) {
            return true;
        }
        List<String> targetIsolation = basicUnitRelationService.getIsolation(workOrder);
        return dto.isShow(targetIsolation, workOrder.getIsolationId());
    }

    @Override
    public CheckReportBackDTO investVerify(List<Integer> workOrderIds) {
        // 获取生产工单业务配置
        FullPathCodeDTO dto = FullPathCodeDTO.builder().fullPathCode(ConfigConstant.WORK_ORDER_INVEST_CHECK_CONFIG).build();
        InvestCheckVerifyConfigDTO configDTO = businessConfigService.getValueDto(dto, InvestCheckVerifyConfigDTO.class);
        CheckReportBackDTO checkBackDTO = CheckReportBackDTO.builder().isLimit(false).isTips(false).build();
        if (!configDTO.getEnable()) {
            return checkBackDTO;
        }
        if (Constants.NULL_STR.equals(configDTO.getVerWorkOrderInvestCheckEnable())) {
            return checkBackDTO;
        }
        // 如果为不通过或者为空，生产工单都不允许投产
        long count = this.lambdaQuery().select(WorkOrderEntity::getInvestCheckResult)
                .in(WorkOrderEntity::getWorkOrderId, workOrderIds)
                .isNotNull(WorkOrderEntity::getInvestCheckResult)
                .list().stream()
                .filter(o -> !o.getInvestCheckResult() || Objects.isNull(o.getInvestCheckResult())).count();
        if (count > 0) {
            if (Constants.LIMIT.equals(configDTO.getVerWorkOrderInvestCheckEnable())) {
                checkBackDTO.setIsLimit(true);
                checkBackDTO.setLimitMessage(configDTO.getVerWorkOrderInvestCheckLimit());
                return checkBackDTO;
            } else if (Constants.TIPS.equals(configDTO.getVerWorkOrderInvestCheckEnable())) {
                checkBackDTO.setIsTips(true);
                checkBackDTO.setTipsMessage(configDTO.getVerWorkOrderInvestCheckTips());
                return checkBackDTO;
            }
        }
        return checkBackDTO;
    }

    @Override
    @Async
    public void investCheck(List<String> workOrderNumbers) {
        // 初始化处理进度
        ImportProgressDTO build = ImportProgressDTO.builder()
                .code(Result.SUCCESS_CODE)
                .progress(Double.valueOf(Constant.ZERO_VALUE))
                .executionDescription("正在处理中，请耐心等候...")
                .executionStatus(false)
                .build();
        redisTemplate.opsForValue().set(RedisKeyPrefix.WORK_ORDER_BATCH_INVEST_CHECK_TASK, JacksonUtil.toJSONString(build), 1, TimeUnit.HOURS);
        for (int i = 0; i < workOrderNumbers.size(); i++) {
            String workOrderNumber = workOrderNumbers.get(i);
            Double processPercent = MathUtil.divideDouble(i + 1, workOrderNumbers.size(), 2);
            boolean finish = processPercent.compareTo(1.0) == 0;
            build = ImportProgressDTO.builder()
                    .code(Result.SUCCESS_CODE)
                    .progress(processPercent)
                    .executionDescription(finish ? "处理完成!" : "正在处理中，请耐心等候...")
                    .executionStatus(finish)
                    .importUrl(null)
                    .build();
            try {
                // 调用第三方接口获取投产检查结果
                OpenApiDTO openApiDTO = OpenApiDTO.builder()
                        .sendReqObject(WorkOrderDetailDTO.builder().workOrderNumber(workOrderNumber).build())
                        .modelCode(OpenApiEnum.WORK_ORDER_INVEST_CHECK.getModelCode())
                        .interfaceCode(OpenApiEnum.WORK_ORDER_INVEST_CHECK.getInterfaceCode())
                        .build();
                WorkOrderInvestCheckResultDTO checkFinish = openApiConfigService.callOpenUrlToGetObject(openApiDTO, WorkOrderInvestCheckResultDTO.class);
                // 更新投产检查结果
                this.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, workOrderNumber)
                        .set(WorkOrderEntity::getInvestCheckResult, checkFinish.getCheckResult())
                        .update();
                WorkOrderInvestCheckResultEntity investCheckResultEntity = investCheckResultService.lambdaQuery().eq(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, workOrderNumber).one();
                if (Objects.isNull(investCheckResultEntity)) {
                    investCheckResultService.save(
                            WorkOrderInvestCheckResultEntity.builder()
                                    .workOrderNumber(workOrderNumber)
                                    .investCheckResultDetail(checkFinish.getMessage())
                                    .resultTime(new Date())
                                    .build()
                    );
                } else {
                    investCheckResultService.lambdaUpdate().eq(WorkOrderInvestCheckResultEntity::getWorkOrderNumber, workOrderNumber)
                            .set(WorkOrderInvestCheckResultEntity::getInvestCheckResultDetail, checkFinish.getMessage())
                            .set(WorkOrderInvestCheckResultEntity::getResultTime, new Date())
                            .update();
                }
                redisTemplate.opsForValue().set(RedisKeyPrefix.WORK_ORDER_BATCH_INVEST_CHECK_TASK, JacksonUtil.toJSONString(build), 2, TimeUnit.HOURS);
            } catch (Exception e) {
                CommonService commonService = SpringUtil.getBean(CommonService.class);
                build = commonService.getImportProgressDTO(RedisKeyPrefix.WORK_ORDER_BATCH_INVEST_CHECK_TASK, e);
                redisTemplate.opsForValue().set(RedisKeyPrefix.WORK_ORDER_BATCH_INVEST_CHECK_TASK, JacksonUtil.toJSONString(build), 2, TimeUnit.HOURS);
                // 手动触发事务回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
    }

    @Override
    public String getBatchInvestCheckProgress() {
        return (String) redisTemplate.opsForValue().get(RedisKeyPrefix.WORK_ORDER_BATCH_INVEST_CHECK_TASK);
    }

    @Override
    public List<WorkOrderEntity> simpleList(Collection<String> workOrderNumbers) {
        return CollUtil.isNotEmpty(workOrderNumbers) ?
                lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers).list()
                : Collections.emptyList();
    }

    @Override
    public WorkOrderEntity lastStateChangeOrder(String lineId) {
        return this.lambdaQuery().eq(WorkOrderEntity::getLineId, lineId)
                .orderByDesc(WorkOrderEntity::getStateChangeTime)
                .orderByDesc(WorkOrderEntity::getWorkOrderId)
                .last(Constant.LIMIT_ONE).one();
    }

    @Override
    public Map<String, Double> workOrderTheoryHourMap(List<WorkOrderEntity> workOrders) {
        List<WorkOrderEntity> workOrders1 = workOrders.stream().filter(e -> CollectionUtils.isNotEmpty(e.getCraftProcedureEntities())).collect(Collectors.toList());
        List<WorkOrderEntity> workOrders2 = workOrders.stream().filter(e -> CollectionUtils.isEmpty(e.getCraftProcedureEntities())).collect(Collectors.toList());
        Map<String, WorkOrderEntity> workOrderMap = workOrders1.stream().collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, o -> o));

        // 1. 所有的工艺工序id
        List<Integer> craftProcedureId = workOrders1.stream()
                .map(WorkOrderEntity::getCraftProcedureEntities)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(CraftProcedureEntity::getId)
                .distinct()
                .collect(Collectors.toList());
        // 工艺工时
        List<ProcedureRelationWorkHoursEntity> procedureHours = CollectionUtils.isEmpty(craftProcedureId) ? Collections.emptyList() :
                procedureRelationWorkHoursService.lambdaQuery().in(ProcedureRelationWorkHoursEntity::getProcedureId, craftProcedureId).list();
        Map<Integer, ProcedureRelationWorkHoursEntity> hourProcedureIdMap = procedureHours.stream().collect(Collectors.toMap(ProcedureRelationWorkHoursEntity::getProcedureId, Function.identity()));
        // 计算
        // 1.先找工艺工时
        Map<String, Double> result1 = workOrders1.stream().collect(Collectors.toMap(
                WorkOrderEntity::getWorkOrderNumber,
                workOrder -> this.getTheoryHourByProcedure(hourProcedureIdMap, workOrder.getCraftProcedureEntities()),
                (v1, v2) -> v1
        ));

        // 2. 产能的
        Iterator<Map.Entry<String, Double>> iterator = result1.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Double> next = iterator.next();
            if (next.getValue() == 0.0) {
                workOrders2.add(workOrderMap.get(next.getKey()));
                iterator.remove();
            }
        }
        Map<String, CapacityVO> workOrderListCapacity = capacityService.getWorkOrderListCapacity(workOrders2);
        Map<String, Double> result2 = workOrderListCapacity.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                // 单位是 {x}/h, 即每小时的输出量，因此需要取倒数
                entry -> NullableDouble.of(1).div(entry.getValue().getCapacity()).cal(0),
                (v1, v2) -> v1
        ));

        // 多个Map合并
        return Stream.of(result1.entrySet(), result2.entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(Map.Entry::getKey, e -> BigDecimal.valueOf(e.getValue()).setScale(8, RoundingMode.HALF_UP).doubleValue(), (v1, v2) -> v1));
    }

    private Double coverToMin(String unit, Double val) {
        if (Constant.HOUR.equals(unit)) {
            return NullableDouble.of(val).mul(60).cal();
        }
        if (Constant.SECOND.equals(unit)) {
            return NullableDouble.of(val).div(60).cal();
        }
        return val;
    }

    @Override
    public Double workOrderTheoryHour(WorkOrderEntity workOrder) {
        return workOrderTheoryHourMap(Collections.singletonList(workOrder)).get(workOrder.getWorkOrderNumber());
    }

    @Override
    public Map<String, Double> workOrderNumberTheoryHourMap(List<String> workOrderNumbers) {
        if (CollectionUtils.isEmpty(workOrderNumbers)) {
            return new HashMap<>(8);
        }
        List<WorkOrderEntity> workOrders = lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers).list();
        showWorkOrdersExtraName(workOrders, ShowExtraNameDTO.builder().procedure(true).build());
        return workOrderTheoryHourMap(workOrders);
    }

    @Override
    public void setSimpleWorkOrderTheoryHour(List<WorkOrderEntity> workOrders) {
        showWorkOrdersExtraName(workOrders, ShowExtraNameDTO.builder().procedure(true).build());
        Map<String, Double> theoryHourMap = workOrderTheoryHourMap(workOrders);
        workOrders.forEach(e -> {
            Double theoryHour = theoryHourMap.get(e.getWorkOrderNumber());
            e.setTheoryHour(theoryHour);
            e.setProduceTheoryHour(NullableDouble.of(theoryHour).mul(e.getFinishCount()).scale(2).cal());
            e.setPlanTheoryHour(NullableDouble.of(theoryHour).mul(e.getPlanQuantity()).scale(2).cal());
            e.setCapacityUnitName((String) redisTemplate.opsForValue().get(RedisKeyPrefix.WORK_ORDER_CAPACITY_UNIT_NAME_ + e.getWorkOrderNumber()));
        });
    }

    private double getTheoryHourByProcedure(Map<Integer, ProcedureRelationWorkHoursEntity> hourProcedureIdMap, List<CraftProcedureEntity> craftProcedures) {
        return craftProcedures.stream()
                .filter(Objects::nonNull)
                .map(e1 -> hourProcedureIdMap.get(e1.getId()))
                .filter(Objects::nonNull)
                // (标准调试工时 min + 加工工时 min) /60 -> 工时 h
                .mapToDouble(e2 -> {
                    Double preparationMin = coverToMin(e2.getPreparationTimeUnit(), e2.getPreparationTime());
                    Double processingMin = coverToMin(e2.getProcessingHoursUnit(), e2.getProcessingHours());
                    return NullableDouble.of(preparationMin).add(processingMin).div(60).cal(0);
                })
                .sum();
    }

    @Override
    public Double workOrderNumberTheoryHour(String workOrderNumber) {
        return workOrderNumberTheoryHourMap(Collections.singletonList(workOrderNumber)).get(workOrderNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WorkOrderEntity> createFromWorkOrder(List<WorkOrderCreateDTO> workOrderCreateDTOS, String username) {
        //找到原始工单
        Set<String> fromWorkOrderNumbers = workOrderCreateDTOS.stream()
                .map(WorkOrderCreateDTO::getFromWorkOrderNumber)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(fromWorkOrderNumbers)) {
            throw new ResponseException("没有拆分的单据");
        }
        List<WorkOrderEntity> list = new ArrayList<>();
        // 按原工单编号存储
        Map<String, WorkOrderEntity> fromWorkOrderEntityMap = new HashMap<>();
        for (String fromWorkOrderNumber : fromWorkOrderNumbers) {
            // 查询工单详情
            WorkOrderEntity entity = this.getWorkOrderById(new WorkOrderDetailDTO(fromWorkOrderNumber));
            if (entity == null) {
                throw new ResponseException("未找到工单信息，单号为：" + fromWorkOrderNumber);
            }
            fromWorkOrderEntityMap.put(fromWorkOrderNumber, entity);
        }
        for (int i = 0; i < workOrderCreateDTOS.size(); i++) {
            WorkOrderCreateDTO workOrderCreateDTO = workOrderCreateDTOS.get(i);
            String fromWorkOrderNumber = workOrderCreateDTO.getFromWorkOrderNumber();
            // 原工单
            if (StringUtils.isEmpty(fromWorkOrderNumber)) {
                WorkOrderEntity workOrderEntity = fromWorkOrderEntityMap.get(workOrderCreateDTO.getWorkOrderNumber());
                workOrderEntity.setPlanQuantity(workOrderCreateDTO.getPlanQuantity());
                workOrderEntity.setWorkCenterId(workOrderCreateDTO.getWorkCenterId());
                workOrderEntity.setWorkCenterName(workOrderCreateDTO.getWorkCenterName());
//                workOrderEntity.setDeviceId(workOrderCreateDTO.getDeviceId());
                workOrderEntity.setLineId(workOrderCreateDTO.getLineId());
//                workOrderEntity.setTeamId(workOrderCreateDTO.getTeamId());
                workOrderEntity.setProductBasicUnits(JacksonUtil.convertArray(workOrderCreateDTO.getProductBasicUnits(), WorkOrderBasicUnitRelationEntity.class));
                workOrderEntity.setRelevanceDeviceIds(workOrderCreateDTO.getRelevanceDeviceIds());
                workOrderEntity.setRelevanceTeamIds(workOrderCreateDTO.getRelevanceTeamIds());
                workOrderEntity.setRelevanceLineIds(workOrderCreateDTO.getRelevanceLineIds());
                workOrderEntity.setEndDate(workOrderCreateDTO.getEndDate());
                workOrderEntity.setStartDate(workOrderCreateDTO.getStartDate());
                workOrderEntity.setSchedulingState(workOrderCreateDTO.getSchedulingState());
                workOrderService.updateByWorkId(workOrderEntity, username);
                list.add(workOrderEntity);
            } else {
                // 新工单
                WorkOrderEntity formWorkOrderEntity = fromWorkOrderEntityMap.get(fromWorkOrderNumber);
                // 复制值
                WorkOrderEntity workOrderEntity = JacksonUtil.convertObject(formWorkOrderEntity, WorkOrderEntity.class);
                workOrderEntity.setWorkOrderNumber(workOrderCreateDTO.getWorkOrderNumber());
                workOrderEntity.setWorkOrderName(workOrderCreateDTO.getWorkOrderName() == null ? workOrderCreateDTO.getWorkOrderNumber() : workOrderCreateDTO.getWorkOrderName());
                workOrderEntity.setOriginalWorkOrderNumber(fromWorkOrderNumber);
                workOrderEntity.setPlanQuantity(workOrderCreateDTO.getPlanQuantity());
                workOrderEntity.setWorkCenterId(workOrderCreateDTO.getWorkCenterId());
                workOrderEntity.setWorkCenterName(workOrderCreateDTO.getWorkCenterName());
//                workOrderEntity.setDeviceId(workOrderCreateDTO.getDeviceId());
                workOrderEntity.setLineId(workOrderCreateDTO.getLineId());
//                workOrderEntity.setTeamId(workOrderCreateDTO.getTeamId());
                workOrderEntity.setProductBasicUnits(JacksonUtil.convertArray(workOrderCreateDTO.getProductBasicUnits(), WorkOrderBasicUnitRelationEntity.class));
                workOrderEntity.setRelevanceDeviceIds(workOrderCreateDTO.getRelevanceDeviceIds());
                workOrderEntity.setRelevanceTeamIds(workOrderCreateDTO.getRelevanceTeamIds());
                workOrderEntity.setRelevanceLineIds(workOrderCreateDTO.getRelevanceLineIds());
                workOrderEntity.setEndDate(workOrderCreateDTO.getEndDate());
                workOrderEntity.setStartDate(workOrderCreateDTO.getStartDate());
                workOrderEntity.setWorkOrderId(null);
                workOrderEntity.setUpdateBy(username);
                workOrderEntity.setCreateBy(username);
                workOrderEntity.setCreateDate(new Date());
                workOrderEntity.setSchedulingState(workOrderCreateDTO.getSchedulingState());
                workOrderService.add(workOrderEntity);
                // 更新状态
                if (!WorkOrderStateEnum.CREATED.getCode().equals(formWorkOrderEntity.getState())) {
                    workOrderEntity.setState(formWorkOrderEntity.getState());
                    workOrderService.updateByWorkId(workOrderEntity, username);
                }
                list.add(workOrderEntity);
            }
        }
        return list;
    }

    @Override
    public Page<WorkOrderEntity> listInvestmentWork(WorkOrderInspectionPackageSelectDTO selectDTO) {
        FacilitiesEntity facilitiesEntity = facilitiesService.getById(selectDTO.getFacId());
        if (Objects.isNull(facilitiesEntity)) {
            return new Page<>();
        }
        //根据工位id查询对应的工位类型名称
        ModelEntity modelEntity = modelService.getById(facilitiesEntity.getModelId());
        if (Objects.isNull(modelEntity)) {
            throw new ResponseException("未找到该工位模型信息");
        }
        ProductionLineEntity productionLineEntity = productionLineService.getById(facilitiesEntity.getProductionLineId());
        if (Objects.isNull(productionLineEntity)) {
            throw new ResponseException("未找到该工位关联的制造单元");
        }
        Page<WorkOrderEntity> page = this.lambdaQuery()
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.INVESTMENT.getCode())
                .eq(WorkOrderEntity::getLineId, facilitiesEntity.getProductionLineId())
                .like(StringUtils.isNotBlank(selectDTO.getWorkOrderNumber()), WorkOrderEntity::getWorkOrderNumber, selectDTO.getWorkOrderNumber())
                .orderByDesc(WorkOrderEntity::getStateChangeTime)
                .orderByDesc(WorkOrderEntity::getWorkOrderId)
                .page(new Page<>(selectDTO.getCurrent(), selectDTO.getSize()));
        //根据工单查询工艺
        List<WorkOrderEntity> records = page.getRecords();
        for (WorkOrderEntity record : records) {
            Integer craftId = record.getCraftId();
            // 设置工序名称 工位类型结合工艺信息找到工序
            if (craftId != null) {
                List<CraftProcedureEntity> list = craftProcedureService.lambdaQuery()
                        .eq(CraftProcedureEntity::getCraftId, craftId)
                        .eq(CraftProcedureEntity::getFacType, modelEntity.getName())
                        .eq(CraftProcedureEntity::getLineModelId, productionLineEntity.getModelId())
                        .list();
                record.setCraftProcedureEntities(list);
            }
        }
        setMaterialFieldsForWorkOrderEntity(page.getRecords());
        return page;
    }

    @Override
    public void updatePackageScheme(UpdateWorkOrderPackageSchemeDTO dto) {
        this.lambdaUpdate().eq(WorkOrderEntity::getWorkOrderNumber, dto.getWorkOrderNumber())
                .set(WorkOrderEntity::getPackageSchemeCode, dto.getPackageSchemeCode())
                .update();
        // 推送消息
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(dto.getWorkOrderNumber());
        WorkOrderEntity entity = this.getWorkOrderById(detailDTO);
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_UPDATE_MESSAGE);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateState(WorkOrderUpdateStateDTO dto) {
        List<String> workOrderNumbers = dto.getWorkOrderNumbers();
        List<WorkOrderEntity> list = this.lambdaQuery()
                .in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                .list();

        for (WorkOrderEntity workOrderEntity : list) {
            workOrderEntity.setState(dto.getState());
            workOrderService.updateByWorkId(WorkOrderSmartUpdateDTO.onlyState(workOrderEntity, dto.getUsername()));
        }
    }

    /**
     * 设置基本生产单元名称
     *
     * @param entity
     */
    private void setProductionBasicUnit(WorkOrderEntity entity) {
        WorkCenterEntity workCenterEntity = workCenterMapper.selectById(entity.getWorkCenterId());
        if (Objects.isNull(workCenterEntity)) {
            return;
        }
        List<WorkOrderBasicUnitRelationEntity> basicUnitRelationEntities = basicUnitRelationService.lambdaQuery()
                .eq(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, entity.getWorkOrderNumber()).list();
        if (CollectionUtils.isEmpty(basicUnitRelationEntities)) {
            entity.setLineId(null);
            entity.setLineCode(null);
            entity.setLineName(null);
            entity.setDeviceId(null);
            entity.setTeamId(null);
            return;
        }
        // 优先将正在投产的生产基本单元赋值到旧字段上去
//        Integer productBasicUnitId = basicUnitRelationEntities.get(0).getProductionBasicUnitId();
        Integer productBasicUnitId = basicUnitRelationEntities.stream().filter(WorkOrderBasicUnitRelationEntity::getIsProducing)
                .map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).findFirst()
                .orElse(basicUnitRelationEntities.get(0).getProductionBasicUnitId());
        entity.setProductBasicUnits(basicUnitRelationEntities);
        // 为兼容历史数据，废弃的字段使用多个按逗号分隔
        String productionBasicUnitName = basicUnitRelationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).collect(Collectors.joining(Constants.SEP));
        entity.setProductionBasicUnitName(productionBasicUnitName);
        List<Integer> productBasicUnitIds = basicUnitRelationEntities.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).collect(Collectors.toList());
        // 如果工作中心为制造单元/班组/设备，则基本生产单元为制造单元实例/班组实例/设备实例
        if (workCenterEntity.getType().equals(WorkCenterTypeEnum.LINE.getCode())) {
            String productBasicCodes = productionLineService.listByIds(productBasicUnitIds).stream().map(ProductionLineEntity::getProductionLineCode).collect(Collectors.joining(","));
            entity.setProductionBasicUnitCode(productBasicCodes);
            // 制造单元取第一条数据
            entity.setLineId(productBasicUnitId);
            entity.setLineCode(productBasicCodes);
            entity.setLineName(productionBasicUnitName);
            // 展示制造单元模型编码
            if (entity.getLineId() != null) {
                ProductionLineEntity productionLineEntity = productionLineService.getById(productBasicUnitId);
                if (productionLineEntity != null) {
                    ModelEntity modelEntity = modelService.getById(productionLineEntity.getModelId());
                    entity.setLineModelCode(modelEntity == null ? null : modelEntity.getCode());
                }
            }
        } else if (workCenterEntity.getType().equals(WorkCenterTypeEnum.DEVICE.getCode())) {
            String productBasicCodes = deviceService.listByIds(productBasicUnitIds).stream().map(DeviceEntity::getDeviceCode).collect(Collectors.joining(","));
            entity.setProductionBasicUnitCode(productBasicCodes);
            // 设备取第一条数据
            entity.setDeviceId(productBasicUnitId);
            entity.setDeviceCode(productBasicCodes);
            entity.setDeviceName(productionBasicUnitName);
        } else if (workCenterEntity.getType().equals(WorkCenterTypeEnum.TEAM.getCode())) {
            String productBasicCodes = sysTeamService.listByIds(productBasicUnitIds).stream().map(SysTeamEntity::getTeamCode).collect(Collectors.joining(","));
            entity.setProductionBasicUnitCode(productBasicCodes);
            // 班组取第一条数据
            entity.setTeamId(productBasicUnitId);
            entity.setTeamName(productionBasicUnitName);
        }
    }

    @Override
    public void writeBackOrderState(StateChangeDTO dto) {
        WorkOrderStateEnum stateEnum = WorkOrderStateEnum.coverFromPushDownOrderState(dto.getChangeState());
        WorkOrderDetailDTO detailDTO = new WorkOrderDetailDTO(dto.getSourceNumber());
        WorkOrderEntity workOrder = getWorkOrderById(detailDTO);
        if (stateEnum == null || workOrder == null) {
            return;
        }
        workOrder.setState(stateEnum.getCode());
        workOrderService.updateByWorkId(WorkOrderSmartUpdateDTO.onlyState(workOrder, null));
    }

    @Override
    public String existRelateOrder(List<Integer> orderIds) {
        List<OrderWorkOrderEntity> workOrderRelations = orderWorkOrderService.listOrderWorkOrderRelations(orderIds, OrderNumTypeEnum.PRODUCT_ORDER.getTypeCode());
        List<String> productOrderNumbers = new ArrayList<>();
        List<Integer> collect = workOrderRelations.stream().map(OrderWorkOrderEntity::getOrderId).collect(Collectors.toList());
        orderIds.removeAll(collect);
        for (Integer integer : orderIds) {
            ProductOrderEntity productOrder = extProductOrderInterface.selectProductOrderById(integer);
            productOrderNumbers.add(productOrder.getProductOrderNumber());
        }
        return Joiner.on(Constants.DIAGONAL_LINE).join(productOrderNumbers);
    }

    /**
     * 设置工单的下推标识信息
     *
     * @param workOrders 工单列表
     */
    private void setPushDownIdentifierInfos(List<WorkOrderEntity> workOrders) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return;
        }

        for (WorkOrderEntity workOrder : workOrders) {
            List<PushDownIdentifierInfoDTO> identifierInfos = orderPushDownIdentifierService.getPushDownIdentifierInfos(OrderNumTypeEnum.WORK_ORDER.getTypeCode(), workOrder.getWorkOrderId().toString());
            workOrder.setPushDownIdentifierInfos(identifierInfos);
        }
    }

}
