package com.yelink.dfs.service.order;

import com.asyncexcel.core.model.ExcelTask;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.constant.defect.WorkOrderUnqualifiedSourceEnum;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitSelectDTO;
import com.yelink.dfs.entity.common.CommonState;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.model.dto.RelevanceResourceDTO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.BatchUpdateWorkOrderDTO;
import com.yelink.dfs.entity.order.dto.FieldInfoDTO;
import com.yelink.dfs.entity.order.dto.ShowExtraNameDTO;
import com.yelink.dfs.entity.order.dto.UpdateTipsDTO;
import com.yelink.dfs.entity.order.dto.UpdateWorkOrderPackageSchemeDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderCreateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderExportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderFacDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderInspectionPackageSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderOpenSelectExtendDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderQuantityVerifyDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderSmartUpdateDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderWrapperDTO;
import com.yelink.dfs.entity.order.vo.JudgeOrderVO;
import com.yelink.dfs.entity.order.vo.PrintSourceOrderVO;
import com.yelink.dfs.entity.order.vo.WorkOrderSimpleVO;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.CraftProcedureEntity;
import com.yelink.dfs.entity.product.CraftProcedureInspectControllerEntity;
import com.yelink.dfs.entity.product.ProcedureEntity;
import com.yelink.dfs.entity.product.ProductEntity;
import com.yelink.dfs.entity.product.dto.CraftFileDTO;
import com.yelink.dfs.entity.product.dto.CraftFileNewDTO;
import com.yelink.dfs.entity.screen.vo.WorkOrderEntityVO;
import com.yelink.dfs.entity.statistics.WorkOrderStateDTO;
import com.yelink.dfs.entity.target.record.dto.CheckReportBackDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderDetailDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderUpdateStateDTO;
import com.yelink.dfscommon.common.service.ImportDataExtendService;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.BatchGenerateCodeDTO;
import com.yelink.dfscommon.dto.BatchGenerateCodeReqDTO;
import com.yelink.dfscommon.dto.BillVo;
import com.yelink.dfscommon.dto.StateEnumDTO;
import com.yelink.dfscommon.dto.pushdown.writeback.StateChangeDTO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.barcode.PrintDTO;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
public interface WorkOrderService extends IService<WorkOrderEntity>, ImportDataExtendService {


    /**
     * 根据id获取工单详细信息
     *
     * @param dto
     * @return
     */
    WorkOrderEntity getWorkOrderById(WorkOrderDetailDTO dto);

    /**
     * 查询关联订单(销售订单和生产订单)
     *
     * @param workOrderEntity
     */
    void getOrderDetailList(WorkOrderEntity workOrderEntity);

    /**
     * 添加工单
     *
     * @param workOrderEntity
     * @return
     */
    WorkOrderEntity add(WorkOrderEntity workOrderEntity);


    /**
     * 获取工序实体类
     *
     * @return
     */
    List<ProcedureEntity> getProcedure();

    /**
     * 获取产线实体类列表
     *
     * @return
     */
    List<ProductionLineEntity> getProductionLine();

    void saveOrderRelation(WorkOrderEntity workOrderEntity);

    /**
     * 获取产品实体类列表
     *
     * @return
     */
    List<ProductEntity> getProduct();


    /**
     * 修改工单信息
     *
     * @param entity
     * @param username
     * @return
     */
    WorkOrderEntity updateByWorkId(WorkOrderEntity entity, String username);


    @Transactional(rollbackFor = Exception.class)
    WorkOrderEntity updateByWorkId(WorkOrderSmartUpdateDTO dto);

    /**
     * 发送消息到websocket
     *
     * @param newEntity
     * @param oldEntity
     */
    void sendWebSocketMessage(WorkOrderEntity newEntity, WorkOrderEntity oldEntity);

    /**
     * 删除工单信息（删除父工单时一并删除所关联的子工单）
     *
     * @param workOrderId
     * @return
     */
    WorkOrderEntity deleteById(Integer workOrderId, String username);

    /**
     * 获取指定产线的工单及时交付率
     *
     * @param lineCode
     * @return
     */
    Double getFinishRate(String lineCode);

    /**
     * 通过产线获取正在生产的工单
     *
     * @param lineIds
     * @return
     */
    List<WorkOrderEntity> getRunningWorkOrderByLineIds(String lineIds);

    /**
     * 获取产线的所有工单
     *
     * @param lineCodes
     */
    List<WorkOrderStateDTO> getAllWorkOrderByLine(String lineCodes);

    /**
     * 通过多个工单号获取工单
     *
     * @param workOrderNumbers
     * @return
     */
    List<WorkOrderEntity> selectByNumbers(String workOrderNumbers);

    /**
     * 获取工单所有的状态以及对应的code
     *
     * @return
     */
    List<CommonState> getWorkOrderState();

    /**
     * 获取指定状态的工单列表（非创建，关闭，取消状态的列表）
     *
     * @return
     */
    List<WorkOrderEntity> getGrantList();

    /**
     * 获取指定状态的工单列表（非创建，关闭，取消、完成状态的列表）
     *
     * @return
     */
    List<WorkOrderEntity> getAppointList();

    /**
     * 获取进度小于100%且状态为投入、挂起的工单
     *
     * @return
     */
    List<WorkOrderEntity> getProgressWorkOrder();


    /**
     * 通过产线编号获取工单号
     *
     * @param productionLineId
     * @param batchTime
     * @return
     */
    List<WorkOrderEntity> getNumbersByLineId(Integer productionLineId, String batchTime);

    /**
     * 通过工单号获取该工单信息
     *
     * @param workOrderNumber
     * @return
     */
    WorkOrderEntity getWorkOrderByNumber(String workOrderNumber);

    /**
     * 获取工单简单记录
     *
     * @param workOrderNumber
     * @return
     */
    WorkOrderEntity getSimpleWorkOrderByNumber(String workOrderNumber);

    /**
     * 获取产线下状态为发放、投入、挂起、完成的工单
     *
     * @param lineId
     * @return
     */
    Page<WorkOrderEntity> listWorkOrderByLineId(Integer lineId, Integer teamId, Integer deviceId, Integer workCenterId, Integer state, String workOrderNumber,
                                                String workOrderName, String materialCode, String materialName, Integer current, Integer size, String assignmentState, Boolean isSubcontract, String projectDefineName) throws ParseException;

    /**
     * 统计产线下各个状态工单数量
     *
     * @return
     */
    Map<Integer, Long> listStateCount(Integer lineId, Integer deviceId, Integer teamId, Integer workCenterId, Boolean isBarCodePad, String username) throws ParseException;

    /**
     * 质检app获取产线下所有状态为投入、的工单
     *
     * @param
     * @return
     */
    List<WorkOrderEntity> listWorkOrderByLineIdForDefect(Integer productionLineId);

    /**
     * 通过产线id获取产线所有发放、投入、挂起、完成的批次
     *
     * @param productionLineId
     * @return
     */
    List<WorkOrderEntity> getOrdersByLineId(Integer productionLineId);

    /**
     * 根据id获取子工单列表
     *
     * @param
     * @return
     */
    List<WorkOrderEntity> getSubListById(Integer id);

    /**
     * 获取所有父工单
     *
     * @return
     */
    List<WorkOrderEntity> parentWorkOrderList();

    double calculatePlannedWorkHours(WorkOrderEntity workOrderEntity);

    /**
     * 批量新增工单（新建父工单，自动生成子工单）
     * 这个接口好像没用了，前端也没有使用到的地方。。。
     *
     * @param workOrderEntity
     * @param username
     * @return
     */
    @Deprecated
    WorkOrderEntity batchAdd(WorkOrderEntity workOrderEntity, String username);

    /**
     * 查询备料工单列表
     *
     * @param workOrderNumber
     * @param workOrderName
     * @param current
     * @param size
     * @param username
     * @return
     */
    Page<WorkOrderEntity> preparationOrderList(String workOrderNumber, String workOrderName, Integer current, Integer size, String username);

    /**
     * 获取状态修改的字段
     *
     * @param workOrderId
     * @param newState
     * @return
     */
    List<FieldInfoDTO> getFieldList(String workOrderId, Integer newState);

    /**
     * 状态修改
     *
     * @param workOrderEntity
     * @param isBatch
     * @param username
     * @return
     */
    UpdateTipsDTO updateStateOnly(WorkOrderEntity workOrderEntity, Integer isBatch, String username);

    /**
     * 查看工单图纸
     *
     * @param
     * @return
     */
    String viewDrawing(String materialCode);


    /**
     * 通过订单号获取工单
     *
     * @param orderId
     * @return
     */
    List<WorkOrderEntity> getByOrderId(Integer orderId);

    /**
     * 保存编辑前提示计划数量与每日计划数量总和是否一致
     *
     * @param workOrderNumber
     * @param planQuantity
     * @return
     */
    String checkPlan(String workOrderNumber, Double planQuantity);

    /**
     * 审批
     *
     * @param approvalStatus
     * @param approvalSuggestion
     * @param id
     * @param username
     */
    void examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer id, String username);

    /**
     * 将 workOrders 转化成 List<WorkOrderMaterialDTO>
     *
     * @param workOrders
     * @return
     */
    List<WorkOrderExportDTO> convertToWorkOrderExportDTO(List<WorkOrderEntity> workOrders);


    /**
     * 获取该产线下实际开始时间最近正在投产的工单
     *
     * @param lineId
     * @return
     */
    WorkOrderEntity getInvestmentLatelyWorkOrder(Integer lineId);

    /**
     * 单据审批更新
     * @param entity
     * @param toApproved
     * @param isBatch
     * @param list
     */
    void orderApprove(WorkOrderEntity entity, Boolean toApproved, Boolean isBatch, List<WorkOrderEntity> list);

    /**
     * 批量审批
     *
     * @param dto
     */
    void approveBatch(ApproveBatchDTO dto);

    /**
     * 根据产线和多个状态查询工单列表
     *
     * @param lineId
     * @param stateList
     * @return
     */
    List<WorkOrderEntity> getWorkOrderByLineAndState(Integer lineId, List<Integer> stateList);

    /**
     * 根据产线和多个状态查询工单列表(分页)
     *
     * @param lineId
     * @param stateList
     * @param workOrderNumber
     * @param current
     * @param size
     * @return
     */
    Page<WorkOrderEntity> getWorkOrderByLineAndStatePage(Integer lineId, List<Integer> stateList, String workOrderNumber, Integer current, Integer size);


    /**
     * 根据产线查询未完成的工单——（pad端）
     *
     * @param lineId
     * @return
     */
    List<WorkOrderEntity> getIncompleteList(String lineId);

    /**
     * 查询工单列表（可模糊、条件查询）
     *
     * @param workOrderSelectDTO
     * @param username
     * @return
     */
    Page<WorkOrderEntity> getList(WorkOrderSelectDTO workOrderSelectDTO, String username);

    /**
     * 获取工单列表精简版
     *
     * @param workOrderSelectDTO
     * @param username
     * @return
     */
    Page<WorkOrderEntity> getWorkOrderEntityPage(WorkOrderSelectDTO workOrderSelectDTO, String username);

    /**
     * 设置工单项目合同
     * @param list
     */
    void setProjectContract(List<WorkOrderEntity> list);

    /**
     * 获取入库数量
     * 根据生产工单 入库单(类型为成品入库)
     *
     * @param workOrderNumber
     * @param materialCode
     * @return
     * @deprecated 2.23版本后不使用该方法，改由wms主动调用openApi接口更新dfs相关的仓库字段数据
     */
    @Deprecated
    Double getInventoryQuantity(String workOrderNumber, String materialCode);

    /**
     * 获取送检单下相关联的工单信息
     *
     * @param workOrderNumber 工单号,支持模糊查询
     * @return
     */
    List<WorkOrderEntity> getQualityList(String workOrderNumber);

    /**
     * 修改工单产前状态
     *
     * @param workOrderNumber
     * @param prenatalStatus
     * @return
     */
    Boolean updatePrenatalStatus(String workOrderNumber, Boolean prenatalStatus, String username);


    /**
     * 通过工单号查询工单附件(支持附件名称模糊查询)
     *
     * @param workOrderNumber
     * @param fileName
     * @return
     */
    List<CraftFileDTO> getWorkOrderFileByWorkNumber(String workOrderNumber, String fileName);

    /**
     * 通过工单编号集合、产线id、物料id查询工单集合
     *
     * @param workOrderNumbers
     * @param lineId
     * @param materialCode
     * @return
     */
    List<WorkOrderEntity> selectByNumbersAndOther(List<String> workOrderNumbers, Integer lineId, String materialCode);

    /**
     * 获取所有工单执行状态以及对应的code
     *
     * @return
     */
    List<CommonType> getAllWorkOrderExecutionState();


    /**
     * 根据工序id查询工单列表
     *
     * @param current
     * @param size
     * @return
     */
    Page<WorkOrderEntity> getWorkOrderListByProcedure(Integer procedureId, Integer current, Integer size, String username);


    /**
     * 获取工单物料的可选工序列表
     *
     * @param craftId      工艺id, 如果为空,取最新的工艺
     * @param materialCode
     * @return
     */
    List<CraftProcedureEntity> getCraftProcedureList(Integer craftId, String materialCode);

    /**
     * 获取工单的工艺绑定的方案
     *
     * @param workOrderNumber  工单号
     * @param inspectSheetType 检验类型
     * @return
     */
    List<CraftProcedureInspectControllerEntity> getWorkOrderCraftScheme(String workOrderNumber, Integer inspectSheetType);

    /**
     * 刷新作业工单相关数量
     *
     * @param workOrderNumber
     */
    void refreshProcedureCount(String workOrderNumber);

    /**
     * 刷新作业工单投入量
     *
     * @param workOrderNumber
     */
    void refreshInputCount(String workOrderNumber);

    /**
     * 查询生效、投产、挂起工单列表（模糊查询）
     *
     * @param workOrderSelectDTO
     * @param username
     * @return
     */
    List<WorkOrderEntity> getTheStateList(WorkOrderSelectDTO workOrderSelectDTO, String username);

    /**
     * 查询生效、投产、挂起的工单，附带模糊查询物料编号、物料名称等条件
     *
     * @param workOrderSelectDTO
     * @return
     */
    List<WorkOrderEntity> getStateList(WorkOrderSelectDTO workOrderSelectDTO);

    /**
     * 发送工单相关数据websocket
     *
     * @param workOrderEntity
     */
    void sendWebSocket(WorkOrderEntity workOrderEntity);

    /**
     * 上料记录app获取产线下所有状态为投入、挂起，生效的工单
     *
     * @param productionLineId
     * @param state            工单状态
     * @return
     */
    List<WorkOrderEntity> listWorkOrderByLineIdForFeed(Integer productionLineId, Integer state);

    /**
     * 获取生效、投产、挂起工单状态
     *
     * @return
     */
    List<StateEnumDTO> getPartOfStates();

    /**
     * 根据工单获取该工单下所有的附件
     *
     * @param workOrderNumber
     * @param name
     * @param type
     * @return
     */
    List<CraftFileNewDTO> getAllFiles(String workOrderNumber, String name, String type, Integer procedureId);

    /**
     * 查询工位下的工单详情(包括工位信息、工位绑定的设备号)
     *
     * @param workOrderNumber
     * @param facId
     * @return
     */
    WorkOrderFacDTO selectWorkOrderFac(String workOrderNumber, Integer facId, Integer craftProcedureId);

    /**
     * 本工序过站数
     *
     * @param workOrderNumber
     * @param craftProcedureId
     * @return
     */
    Integer getPassCount(String workOrderNumber, Integer craftProcedureId);

    /**
     * 生产工单批量编辑
     *
     * @param entity
     * @param username
     * @return
     */
    void batchUpdateWorkOrder(BatchUpdateWorkOrderDTO entity, String username, String code);


    /**
     * 修改工单产线信息（废弃的接口，目前给扫码报工使用）
     *
     * @param entity 工单entity
     * @return
     */
    @Deprecated
    WorkOrderEntity updateLineById(WorkOrderEntity entity, String username);

    /**
     * 查询工单下的工序列表
     *
     * @param workOrderNumber 工单号
     * @return
     */
    List<WorkOrderProcedureRelationEntity> selectWorkOrderProcedureList(String workOrderNumber);

    /**
     * 查询生产订单关联工单，工单关联的工艺工序
     * TODO 临时使用 ps: 工单可以选择工艺而不是使用当前物料的最新工艺,如果生产订单关联两个不同工艺的工单就会出现问题, 现在不处理这种情况
     *
     * @param productOrderId
     * @return
     */
    List<CraftProcedureEntity> listCraftProcedureByProductOrderId(Integer productOrderId);


    /**
     * 获取工单关联的工艺工序的工艺列表
     *
     * @param workOrderNumber
     * @return
     */
    List<CraftProcedureEntity> listCraftProcedureByWorkOrderNumber(String workOrderNumber);

    /**
     * 获取工单关联的工艺工序的工艺id
     *
     * @param workOrderNumber
     * @return
     */
    Integer getCraftIdByWorkOrderNumber(String workOrderNumber);

    /**
     * 校验工单
     *
     * @param workOrderNumber
     * @return
     */
    WorkOrderEntity checkWorkOrder(String workOrderNumber);

    /**
     * 根据制造单元类型获取工单列表
     *
     * @param workCenterId
     * @param teamId
     * @param lineCode
     * @param workOrderNumber
     * @param customerName
     * @param materialName
     * @param materialCode
     * @param startDate
     * @param endDate
     * @param state
     * @param current
     * @param size
     * @param username
     * @return
     */
    Page<WorkOrderEntity> getWorkOrderListByWorkCenterId(Integer workCenterId, Integer teamId, String lineCode, String workOrderNumber, String customerName, String materialName,
                                                         String materialCode, String startDate, String endDate, String state, Integer current, Integer size, String username);


    /**
     * 生产工单新增前的判断：
     * 1、销售订单关联的生产工单的数量之和(包括当前的生产工单) > 销售订单的计划数量，给前端提示
     * 2、如果生产订单关联的生产工单的数量之和(包括当前的生产工单) > 生产订单的计划数量，给前端提示
     *
     * @param entity 生产工单
     * @return
     */
    List<BillVo> judgeBeforeInsert(WorkOrderEntity entity);

    /**
     * 根据工单编号更新
     *
     * @param workOrderEntity
     */
    void updateByWorkOrderNumber(WorkOrderEntity workOrderEntity);

    /**
     * 获取投产、完成、挂起状态的工单
     *
     * @param workOrderNumber
     * @return
     */
    List<WorkOrderEntity> getListForAging(String workOrderNumber);

    /**
     * 设置单位类型。如果单据类型未赋值，则需根据单据类型配置动态获取默认值，并且自动赋值关联的业务类型
     */
    void setOrderTypeAndBusinessType(WorkOrderEntity workOrderEntity);

    @Async
    void dealAfterAdd(WorkOrderEntity workOrderEntity);

    /**
     * 设置生产基本单元id和隔离ID和隔离ID，方便后续过滤查询
     *
     * @param workOrderEntity
     */
    void setProductionBasicUnitIdAndIsolationId(WorkOrderEntity workOrderEntity);
//
//    void saveOrUpdateOrderCompleted(String productOrderNumber, CraftProcedureEntity craftProcedureEntity);

    void saveRelevanceResource(WorkOrderEntity workOrderEntity);

    /**
     * 新增生效工单
     *
     * @param entity
     * @param username
     * @return
     */
    WorkOrderEntity addReleasedWorkOrder(WorkOrderEntity entity, String username);

    /**
     * // 分页获取设备的生产工单列表
     * // 条件如下：
     * // 1. 生产工单状态为生效、投产、挂起；
     * // 2. 生产工单状态为完成，完成时间为当天(0-24);
     *
     * @param current
     * @param size
     * @param gridId
     * @return
     */
    Page<WorkOrderEntityVO> workOrderByPage(Integer current, Integer size, Integer gridId);

    /**
     * 统计设备下的生产工单数量情况：待生产
     * 统计条件如下：
     * 1. 生产工单状态为生效、挂起
     *
     * @param gridId
     * @return
     */
    int producedTotal(Integer gridId);

    /**
     * 统计设备下的生产工单数量情况：生产中
     * 统计条件如下：
     * 1. 生产工单状态为投产；
     *
     * @param gridId
     * @return
     */
    int producingTotal(Integer gridId);

    /**
     * 统计设备下的生产工单数量情况：生产中、已完成
     * 统计条件如下：
     * 生产工单状态为完成，完成时间为当天(0-24);
     *
     * @param gridId
     * @return
     */
    int completedTotal(Integer gridId);

    void updateProductLineRelation(WorkOrderEntity entity);

    /**
     * 更新生产工单与产线关联关系
     *
     * @param operationId
     * @param lineId
     * @param workOrderNum
     */
    void updateProductLineRelation(Integer operationId, Integer lineId, String workOrderNum);

    /**
     * 获取指定状态的工单号（非创建，关闭，取消状态的列表）
     *
     * @param fid
     * @param workOrderNumber
     * @return
     */
    List<String> getWorkOrderNumberByState(Integer fid, String workOrderNumber);

    /**
     * 产线报工 修改工单基础信息
     *
     * @param entity
     * @param username
     * @return
     */
    WorkOrderEntity updateBaseInfo(WorkOrderEntity entity, String username);

    /**
     * 生产作业小程序：
     * 扫码--判断条码 是生产设备 还是生产工单
     *
     * @param barCode  条形码
     * @param scanType
     * @return 条码类型
     */
    Boolean judgeBarCodeType(String barCode, String scanType);

    /**
     * 获取多级BOM(如有多个，找状态生效，且创建时间最近的)
     *
     * @param materialCode
     * @param workOrderNum
     * @return
     */
    BomEntity getMultiLevelBom(String materialCode, String workOrderNum);

    /**
     * 获取工厂所有的制造单元类型（无工序的排在后面）
     *
     * @param materialCode
     * @param craftId
     * @return
     */
    List<WorkCenterEntity> getAllWorkCenterWithMaterialCode(String materialCode, Integer craftId, String businessType);


    /**
     * 查询工单计划工时是否超时,超时多久
     * 1.实际工时>=计划工时*(1+生产超时阈值配置_百分比) 或者
     * 2.实际工时>=计划工时+生产超时阈值配置_百分比
     *
     * @param workOrderNumber
     * @return
     */
    Double getPlanWorkTimeOutByNumber(String workOrderNumber);


    /**
     * 查询工单流转时长是否超时,超时多久
     * 1.流转时长>=标准流转时长*（1+流转超时阈值配置_百分比）或者
     * 2.流转时长>=标准流转时长+流转超时阈值配置_固定值
     *
     * @param workOrderNumber
     * @return
     */
    Double getCirculationTimeOutByNumber(String workOrderNumber);

    /**
     * 统计产线的生产工单预计剩余加工时间
     *
     * @param lineId
     * @param state
     * @param excludeOrders 排除的工单
     * @return
     */
    Double getTheRestOfProductionTime(Integer lineId, Integer state, List<String> excludeOrders);

    /**
     * 获取工单的工艺工序的加工时长
     *
     * @param workOrderNumbers
     * @return
     */
    Double getProcessingHoursOfWorkOrder(List<String> workOrderNumbers);

    /**
     * 获取生产工单关联的生产订单详情
     *
     * @param workOrderId
     * @param craftProcedureId
     * @return
     */
    ProductOrderEntity getProductOrderByWorkOrder(Integer workOrderId, Integer craftProcedureId);

    /**
     * 生产工单 生产订单扫码跳转
     *
     * @param orderNumber 工单 or 订单号
     * @param username
     * @return
     */
    JudgeOrderVO judgeWorkOrderOrProductOrder(String orderNumber, String username);

    /**
     * 通过工单号获取该工单信息(数据隔离)
     *
     * @param workOrderNumber
     * @param username
     * @return
     */
    WorkOrderEntity getWorkOrderByNumberWithIsolation(String workOrderNumber, String username);

    /**
     * 查询导入记录列表
     *
     * @param fileName
     * @param startTime
     * @param endTime
     * @param current
     * @param size
     * @return
     */
    Page<ImportDataRecordEntity> recordList(Integer current, Integer size, String fileName, String startTime, String endTime);

    void showStateAndName(List<WorkOrderEntity> records);

    /**
     * 设置工单的MaterialFields
     *
     * @param entity
     */
    void setMaterialFieldsForWorkOrderEntity(WorkOrderEntity entity);

    /**
     * 工单标签打印
     *
     * @param selectDTO 查询条件
     * @return
     */
    PrintDTO print(WorkOrderSelectDTO selectDTO);

    /**
     * 获取工单列表, 状态 2，3，4，5，6
     *
     * @param workOrderSelectDTO 查询条件
     * @return 列表
     */
    List<WorkOrderSimpleVO> getWorkOrderEntityList(WorkOrderSelectDTO workOrderSelectDTO);

    /**
     * 报工小程序扫码获取工单
     *
     * @param workOrderNumber
     * @return
     */
    WorkOrderEntity getWorkOrderByNumberForAppScan(String workOrderNumber);

    /**
     * 获取工单关联资源
     *
     * @param workOrderEntity
     */
    void getRelevanceLineById(WorkOrderEntity workOrderEntity);

    /**
     * 获取工单关联班组资源
     *
     * @param workOrderEntity
     */
    void getRelevanceTeamById(WorkOrderEntity workOrderEntity);

    /**
     * 设置工艺工序
     *
     * @param workOrderEntity
     */
    void setCraftProcedure(WorkOrderEntity workOrderEntity);

    /**
     * 获取工单关联设备资源
     *
     * @param workOrderEntity
     */
    void getRelevanceDeviceById(WorkOrderEntity workOrderEntity);

    /**
     * 查工单的附件
     *
     * @return 附件列表
     */
    List<CraftFileNewDTO> getWorkOrderFiles(Integer workOrderId);

    /**
     * 查物料的附件
     *
     * @param materialCode 物料
     * @return 附件列表
     */
    List<CraftFileNewDTO> getMaterialFiles(String materialCode);

    /**
     * 查bom的附件
     *
     * @param materialCode 物料
     * @return 附件列表
     */
    List<CraftFileNewDTO> getBomFiles(String materialCode);

    /**
     * 查对应工艺工序的附件
     *
     * @param workOrderNumber 工单号
     * @param fid             工位，用于过滤。非必填
     * @param procedureId     工序id，可选
     * @return 附件列表
     */
    List<CraftFileNewDTO> getProcedureFiles(String workOrderNumber, Integer fid, Integer procedureId);

    /**
     * 工单流水码生成列表
     *
     * @param dto
     * @return
     */
    List<BatchGenerateCodeDTO> generateCodeList(BatchGenerateCodeReqDTO dto);

    /**
     * 工单标签打印前校验 标签是否已打印
     *
     * @param selectDTO
     */
    void judgeBeforePrint(WorkOrderSelectDTO selectDTO);

    /**
     * 自动创建流水码
     *
     * @param workOrderEntities
     */
    void autoGenerate(List<WorkOrderEntity> workOrderEntities);

    /**
     * 根据id与不良的配置跟新工单数据
     *
     * @param workOrderEntity 工单
     * @param sourceEnum      工单不良数配置来源
     */
    void updateByIdAndDefectConfig(WorkOrderEntity workOrderEntity, WorkOrderUnqualifiedSourceEnum sourceEnum);

    /**
     * 获取工单关联资源类型及关联资源列表
     *
     * @param workOrder 工单号
     * @return
     */
    RelevanceResourceDTO getWorkOrderRelevanceResourceList(String workOrder);

    /**
     * 工单列表的对外接口查询
     *
     * @param selectExtendDTO
     * @return
     */
    Page<WorkOrderEntity> getWorkOrderPageByOpenApi(WorkOrderOpenSelectExtendDTO selectExtendDTO);

    /**
     * 保存/更新生产工单前进行数量校验
     *
     * @param quantityVerifyDTOS 生产订单号，生产订单计划数、本次单据新增的物料数量
     * @return
     */
    CheckReportBackDTO quantityVerify(List<WorkOrderQuantityVerifyDTO> quantityVerifyDTOS);


    /**
     * 上传自定义列表导出模板
     *
     * @param file
     * @param username
     * @throws IOException
     */
    void uploadListExportTemplate(MultipartFile file, String username) throws IOException;


    /**
     * 创建列表导出任务
     *
     * @param workOrderSelectDTO
     * @param username
     * @return
     */
    Long exportTask(WorkOrderSelectDTO workOrderSelectDTO, String username);


    /**
     * 获取导出列表任务
     *
     * @param currentPage
     * @param pageSize
     * @return
     */
    IPage<ExcelTask> taskPage(Integer currentPage, Integer pageSize);


    /**
     * 获取列表导出任务进度详情
     *
     * @param taskId
     * @return
     */
    ExcelTask taskById(Long taskId);

    /**
     * 构建查询条件
     *
     * @param wrapper 工单查询条件
     * @param dto     构造入参
     */
    void buildWrapper(LambdaQueryWrapper<WorkOrderEntity> wrapper, WorkOrderWrapperDTO dto);

    /**
     * 展示工单的相关名称
     *
     * @param workOrders 工单列表
     * @param dto        展示的项
     */
    void showWorkOrdersExtraName(List<WorkOrderEntity> workOrders, ShowExtraNameDTO dto);

    List<WorkOrderEntity> showDetailInfo(WorkOrderSelectDTO workOrderSelectDTO, Page<WorkOrderEntity> page);

    /**
     * 设置拓展字段中文名
     */
    void setWorkOrderExtendFieldName(Map<String, String> fieldRuleConfMap, WorkOrderEntity entity);

    /**
     * 批量编辑前校验
     *
     * @param workOrderSelectDTO
     * @return
     */
    List<WorkOrderEntity> batchEditVerify(WorkOrderSelectDTO workOrderSelectDTO);

    /**
     * 删除工单前的判断
     * 工单只要在生产中（投产/挂起），完成数量finishCount>0 || 投入数量inputCount>0 || （存在扫码记录，需要先删除流水码，才能删除工单）时，
     * 提示"该工单在生产中，是否删除"，其他情况允许直接删除
     *
     * @param workOrderNumber 工单号
     * @return
     */
    void judgeBeforeDelete(String workOrderNumber);

    /**
     * 通过工单号模糊查询工单号
     *
     * @param workOrderNumber 生产工单号
     * @return
     */
    List<PrintSourceOrderVO> getNumberListByWorkOrderNumber(String workOrderNumber);

    /**
     * 通过工单号及工序查询出工单对应生产订单下所有含有一下工序的工单
     *
     * @param workOrderNumber 工单号
     * @param procedureIds    工序id
     * @return
     */
    List<String> getWorkerNumbersByProcedureName(String workOrderNumber, String procedureIds);

    /**
     * 过滤出最后一道工序的工单
     * 如果元素不存在工序则默认存在最后一道工序
     *
     * @param workOrders 工单集合
     * @return 最后一道工序的工单集合
     */
    List<WorkOrderEntity> filterLastProcedureWorkOrders(List<WorkOrderEntity> workOrders);

    /**
     * 获取工单列表 -> 通过生产订单号集合
     * 取消的工单不算
     *
     * @param productOrderNumbers 生产订单号集合
     * @return 工单列表
     */
    List<WorkOrderEntity> listByProductOrderOrderNumbers(Collection<String> productOrderNumbers);

    /**
     * 上料防错更新
     *
     * @param entity
     * @param username
     * @return
     */
    WorkOrderEntity materialCheckUpdate(WorkOrderEntity entity, String username);

    /**
     * 工单上料防错物料信息
     *
     * @param workOrderId
     * @return
     */
    WorkOrderEntity selectMaterialCheck(Integer workOrderId);

    /**
     * 上料防错批量编辑
     *
     * @param entity
     * @param username
     * @return
     */
    void batchUpdateWorkOrderMaterialCheck(BatchUpdateWorkOrderDTO entity, String username);

    /**
     * 工单上料防错物料信息
     *
     * @param workOrderId
     * @param craftProcedureId
     * @return
     */
    WorkOrderEntity selectMaterialCheck(Integer workOrderId, Integer craftProcedureId);

    /**
     * 批量修改生产工单计划开始结束时间
     *
     * @param
     * @return
     */
    List<String> batchUpdatePlaneTime(BatchUpdateWorkOrderDTO entity, String username);

    /**
     * 获取数量校验配置前，需要获取完工自动报工配置，如果完工时检查有无报工记录，如无报工记录，自动补充等于计划数的报工记录:
     *
     * @param
     * @return
     */
    Boolean judgeIsAutoReportRecord(String workOrderNumber);

    /**
     * 通过订单和工位找工序再找投产工单
     *
     * @param fid
     * @return
     */
    List<WorkOrderEntity> getWorkOrderByFidProcedure(String productOrderNumber, Integer fid);


    /**
     * 判断用户对工单是否有权限
     *
     * @param userName
     * @param workOrderNumber
     * @return
     */
    Boolean workOrderIsolation(String userName, String workOrderNumber);

    /**
     * 生产工单投产前进行投产检验
     *
     * @param workOrderIds 需要检验的工单Id
     * @return
     */
    CheckReportBackDTO investVerify(List<Integer> workOrderIds);

    /**
     * 生产工单投产检验
     *
     * @param workOrderNumbers 需要检查的工单编号
     * @return
     */
    void investCheck(List<String> workOrderNumbers);

    String getBatchInvestCheckProgress();

    /**
     * 获取简单的工单列表
     *
     * @param workOrderNumbers 工单号集合
     * @return 工单列表
     */
    List<WorkOrderEntity> simpleList(Collection<String> workOrderNumbers);

    /**
     * 最新一条状态更新的工单
     *
     * @param lineId 产线
     * @return 工单
     */
    WorkOrderEntity lastStateChangeOrder(String lineId);

    /**
     * <P>获取工单列表的 单件理论工时h</P>
     * 这里的工单必须已经尝试获取 工艺工序列表, 否则会忽略计算工艺工时
     *
     * @param workOrders 工单列表
     * @return Map
     */
    Map<String, Double> workOrderTheoryHourMap(List<WorkOrderEntity> workOrders);

    /**
     * <P>获取工单的单件理论工时h</P>
     * 这里的工单必须已经尝试获取 工艺工序列表, 否则会忽略计算工艺工时
     *
     * @param workOrder 工单
     * @return 单件理论工时h
     */
    Double workOrderTheoryHour(WorkOrderEntity workOrder);

    /**
     * <P>获取工单号列表的 单件理论工时h</P>
     *
     * @param workOrderNumbers 工单号
     * @return Map
     */
    Map<String, Double> workOrderNumberTheoryHourMap(List<String> workOrderNumbers);

    /**
     * <P>获取工单号的单件理论工时h</P>
     *
     * @param workOrderNumber 工单
     * @return 单件理论工时h
     */
    Double workOrderNumberTheoryHour(String workOrderNumber);
    /**
     * <P>赋予简单的工单列表的 单件理论工时h</P>
     * @param workOrders 工单列表
     */
    void setSimpleWorkOrderTheoryHour(List<WorkOrderEntity> workOrders);

    /**
     * 拆分生产工单*
     *
     * @param workOrderCreateDTOS
     * @param username
     * @return
     */
    List<WorkOrderEntity> createFromWorkOrder(List<WorkOrderCreateDTO> workOrderCreateDTOS, String username);

    /**
     * 通过工位id查询投产状态的工单
     *
     * @param selectDTO
     * @return
     */
    Page<WorkOrderEntity> listInvestmentWork(WorkOrderInspectionPackageSelectDTO selectDTO);

    /**
     * 更新工单的包装方案
     *
     * @param dto
     */
    void updatePackageScheme(UpdateWorkOrderPackageSchemeDTO dto);

    /**
     * 对外接口批量修改工单状态
     *
     * @param dto
     */
    void batchUpdateState(WorkOrderUpdateStateDTO dto);

    void writeBackOrderState(StateChangeDTO dto);

    String existRelateOrder(List<Integer> orderIds);
}
