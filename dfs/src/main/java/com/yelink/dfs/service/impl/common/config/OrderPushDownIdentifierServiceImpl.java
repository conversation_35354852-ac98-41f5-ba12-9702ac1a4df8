package com.yelink.dfs.service.impl.common.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.mapper.common.config.OrderPushDownIdentifierMapper;
import com.yelink.dfs.service.common.config.OrderPushDownConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.PushDownFullPathCodeDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierTreeDTO;
import com.yelink.dfscommon.entity.dfs.OrderPushDownConfigEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 单据下推标识服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderPushDownIdentifierServiceImpl extends ServiceImpl<OrderPushDownIdentifierMapper, OrderPushDownIdentifierEntity> implements OrderPushDownIdentifierService {

    @Resource
    private OrderPushDownConfigService orderPushDownConfigService;

    @Override
    public List<OrderPushDownIdentifierEntity> getByOrderTypeAndMaterialId(String orderType, String orderMaterialId) {
        if (StringUtils.isBlank(orderType) || StringUtils.isBlank(orderMaterialId)) {
            return null;
        }
        
        LambdaQueryWrapper<OrderPushDownIdentifierEntity> wrapper = Wrappers.lambdaQuery(OrderPushDownIdentifierEntity.class)
                .eq(OrderPushDownIdentifierEntity::getOrderType, orderType)
                .eq(OrderPushDownIdentifierEntity::getOrderMaterialId, orderMaterialId);
        
        return this.list(wrapper);
    }

    @Override
    public OrderPushDownIdentifierEntity getByOrderTypeAndMaterialIdAndBatch(String orderType, String orderMaterialId, String batchNumber) {
        if (StringUtils.isBlank(orderType) || StringUtils.isBlank(orderMaterialId)) {
            return null;
        }

        LambdaQueryWrapper<OrderPushDownIdentifierEntity> wrapper = Wrappers.lambdaQuery(OrderPushDownIdentifierEntity.class)
                .eq(OrderPushDownIdentifierEntity::getOrderType, orderType)
                .eq(OrderPushDownIdentifierEntity::getOrderMaterialId, orderMaterialId);

        if (StringUtils.isNotBlank(batchNumber)) {
            wrapper.eq(OrderPushDownIdentifierEntity::getBatchNumber, batchNumber);
        }

        return this.getOne(wrapper);
    }

    @Override
    public List<OrderPushDownIdentifierEntity> getByState(String state) {
        if (StringUtils.isBlank(state)) {
            return null;
        }

        LambdaQueryWrapper<OrderPushDownIdentifierEntity> wrapper = Wrappers.lambdaQuery(OrderPushDownIdentifierEntity.class)
                .eq(OrderPushDownIdentifierEntity::getState, state);

        return this.list(wrapper);
    }

    @Override
    public List<OrderPushDownIdentifierEntity> getByStateEnum(PushDownIdentifierStateEnum stateEnum) {
        if (stateEnum == null) {
            return null;
        }

        return this.getByState(stateEnum.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddIdentifier(List<OrderPushDownIdentifierEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        
        this.saveBatch(entities);
        log.info("批量新增下推标识成功，数量：{}", entities.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateIdentifier(List<OrderPushDownIdentifierEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        
        this.updateBatchById(entities);
        log.info("批量更新下推标识成功，数量：{}", entities.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByOrderTypeAndMaterialId(String orderType, String orderMaterialId) {
        if (StringUtils.isBlank(orderType) || StringUtils.isBlank(orderMaterialId)) {
            return;
        }
        
        LambdaQueryWrapper<OrderPushDownIdentifierEntity> wrapper = Wrappers.lambdaQuery(OrderPushDownIdentifierEntity.class)
                .eq(OrderPushDownIdentifierEntity::getOrderType, orderType)
                .eq(OrderPushDownIdentifierEntity::getOrderMaterialId, orderMaterialId);
        
        this.remove(wrapper);
        log.info("删除下推标识成功，源单据类型：{}，物料行id：{}", orderType, orderMaterialId);
    }

    @Override
    public List<PushDownIdentifierInfoDTO> getPushDownIdentifierInfos(String orderType, String orderMaterialId) {
        if (StringUtils.isBlank(orderType) || StringUtils.isBlank(orderMaterialId)) {
            return new ArrayList<>();
        }

        List<OrderPushDownIdentifierEntity> identifiers = this.getByOrderTypeAndMaterialId(orderType, orderMaterialId);
        if (CollectionUtils.isEmpty(identifiers)) {
            return new ArrayList<>();
        }

        return identifiers.stream().map(identifier -> {
            // 根据下推状态获取对应的中文名称
            String identifierName = getStateDisplayName(identifier.getState(), identifier.getTargetOrderType());

            return PushDownIdentifierInfoDTO.builder()
                    .identifierName(identifierName)
                    .targetOrderType(identifier.getTargetOrderType())
                    .state(identifier.getState())
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 获取下推状态的显示名称
     */
    private String getStateDisplayName(String state, String targetOrderType) {
        String stateName = PushDownIdentifierStateEnum.getNameByCode(state);

        // 可以根据目标单据类型添加更具体的描述
        String targetTypeName = getTargetOrderTypeName(targetOrderType);
        return targetTypeName + "-" + stateName;
    }

    /**
     * 获取目标单据类型的中文名称
     */
    private String getTargetOrderTypeName(String targetOrderType) {
        String typeName = OrderNumTypeEnum.getNameByCode(targetOrderType);
        return StringUtils.isNotBlank(typeName) ? typeName : targetOrderType;
    }

    @Override
    public List<PushDownIdentifierTreeDTO> getPushDownIdentifierTree(PushDownFullPathCodeDTO dto) {
        if (dto == null || StringUtils.isBlank(dto.getFullPathCode())) {
            return new ArrayList<>();
        }

        // 获取配置树
        List<OrderPushDownConfigEntity> configs = orderPushDownConfigService.getTreeByFullPathCode(dto);
        if (CollectionUtils.isEmpty(configs)) {
            return new ArrayList<>();
        }
        return configs.get(0).getChildren().stream().map(config -> {
            // 为每个配置创建下推标识状态列表
            List<PushDownIdentifierTreeDTO.PushDownIdentifierStateDTO> children = new ArrayList<>();

            // 添加三种下推状态
            PushDownIdentifierStateEnum[] states = PushDownIdentifierStateEnum.values();
            for (PushDownIdentifierStateEnum state : states) {
                PushDownIdentifierTreeDTO.PushDownIdentifierStateDTO stateDTO =
                    PushDownIdentifierTreeDTO.PushDownIdentifierStateDTO.builder()
                        .code(state.getCode())
                        .name(state.getName())
                        .fullCode(config.getCode() + "_" + state.getCode())
                        .fullName(config.getName() + "-" + state.getName())
                        .build();
                children.add(stateDTO);
            }

            return PushDownIdentifierTreeDTO.builder()
                    .name(config.getName())
                    .code(config.getCode())
                    .children(children)
                    .build();
        }).collect(Collectors.toList());
    }

}
