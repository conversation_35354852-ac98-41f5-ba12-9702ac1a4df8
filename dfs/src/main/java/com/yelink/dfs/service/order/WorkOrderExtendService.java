package com.yelink.dfs.service.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitSelectDTO;
import com.yelink.dfs.entity.capacity.dto.ProductionBasicUnitTypeSelectDTO;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderExcelDTO;
import com.yelink.dfs.entity.order.vo.OrderTraceGroupVO;
import com.yelink.dfs.entity.task.dto.WorkOrderTaskDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderBasicUnitInvestSelectDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumberDTO;
import com.yelink.dfs.open.v1.workOrder.dto.WorkOrderNumbersDTO;
import com.yelink.dfs.open.v1.workOrder.vo.WorkOrderCraftVO;
import com.yelink.dfs.open.v2.order.vo.WorkOrderBasicUnitRelationVO;
import com.yelink.dfscommon.dto.ams.order.ProductOrderParamDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeInfoVO;
import com.yelink.dfscommon.dto.dfs.OrderMaterialListDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderOperatorDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderOperatorSelectDTO;
import com.yelink.dfscommon.dto.dfs.WorkOrderProductBasicUnitChangeDTO;
import com.yelink.dfscommon.dto.dfs.push.AbstractPushDTO;
import com.yelink.dfscommon.dto.dfs.push.CraftRoutePushWorkOrderVO;
import com.yelink.dfscommon.entity.CommonType;
import com.yelink.dfscommon.entity.dfs.OperationLogEntity;
import com.yelink.dfscommon.vo.pushdown.OrderMaterialListVO;

import java.util.Date;
import java.util.List;


/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-03-11 10:18
 */
public interface WorkOrderExtendService extends IService<WorkOrderEntity> {

    /**
     * 解绑生产基本单元并刷新记录
     * （占用其他工单的生产基本单元，需要设置为未投产，并刷新对应的投产记录）
     */
    void unbindProductBasic(WorkOrderProductBasicUnitChangeDTO changeDTO, String workCenterType, Date refreshInvestTime);

    /**
     * 追溯生产工单的生产订单，销售订单
     *
     * @param orderType
     * @param orderNumber
     * @return
     */
    OrderTraceGroupVO traceOrderByOrderNumber(String orderType, String orderNumber);

    /**
     * 关联的表全部删除，如果涉及其他微服务，发送kafka消息通知删除
     * (异步处理，防止删除的数据量过大导致前端接口超时)
     */
    void removeAllRelatedDataByWorkOrder(WorkOrderEntity entity, String username);

    /**
     * 查询该用户是否允许反改工单状态
     */
    Boolean workOrderStateChange(String username);

    /**
     * 获取生产工单物料行列表
     *
     * @return
     */
    List<OrderMaterialListVO> getPushDownMaterialList(OrderMaterialListDTO dto);

    /**
     * 目标单据：按工艺路线下推生产工单：获取生产工单下推物料行列表
     */
    List<CraftRoutePushWorkOrderVO.TargetOrderPushMaterialListVO> getTargetPushMaterialList(AbstractPushDTO pushDown);

    /**
     * 目标单据：按工艺路线下推生产工单：生成生产工单
     */
    void pushWorkOrder(CraftRoutePushWorkOrderVO pushDown, String username, String code);

    /**
     * 批量删除生产工单
     *
     * @param code 此次删除的唯一标识
     * @return
     */
    void batchDelete(WorkOrderNumbersDTO workOrderNumbersDTO, String code, OperationLogEntity operationLog);

    /**
     * 查询工单绑定的工艺信息
     */
    List<WorkOrderCraftVO> getBindCraftInfo(WorkOrderNumbersDTO workOrderNumbersDTO);

    /**
     * 小程序-修改工单需要投产的生产基本单元
     *
     * @param changeDTO
     * @return
     */
    void changeWorkOrderInvestProductBasicUnit(WorkOrderProductBasicUnitChangeDTO changeDTO);

    /**
     * 查看工单的生产基本单元投产记录
     *
     * @param selectDTO
     * @return
     */
    Page<WorkOrderBasicUnitInputRecordEntity> getWorkOrderInputRecords(WorkOrderBasicUnitInvestSelectDTO selectDTO);

    /**
     * 刷新生产基本单元投产记录
     * @param
     * @return
     */
    void dealProductBasicUnitRecordWhenHangupWorkOrder(String workOrderNumber);

    /**
     * 小程序-获取工单工作中心下所有可选的生产基本单元列表
     *
     * @param workOrderNumberDTO 工单号
     * @return
     */
    List<WorkOrderBasicUnitRelationVO> getWorkOrderAllProductBasicUnit(WorkOrderNumberDTO workOrderNumberDTO);

    /**
     * 查询基本生产单元类型
     *
     * @param
     * @return
     */
    List<CommonType> getBasicUnitType(ProductionBasicUnitTypeSelectDTO selectDTO);

    /**
     * 工单关联绑定操作员
     *
     * @param
     * @return
     */
    void bindOperator(WorkOrderOperatorDTO operatorDTO);

    /**
     * 查询工单已经关联绑定的操作员
     *
     * @param
     * @return
     */
    WorkOrderOperatorDTO selectBindOperator(WorkOrderOperatorSelectDTO operatorDTO);

    void verifyFormat(List<WorkOrderExcelDTO> workOrderExcelDTOS);

    void pushToTask(boolean register, WorkOrderTaskDTO taskDTO);

    OrderTypeInfoVO getOrderTypeByProductOrder(ProductOrderParamDTO paramDTO);

    /**
     * 查询基本生产单元
     *
     * @param
     * @return
     */
    List<CommonType> getBasicUnit(ProductionBasicUnitSelectDTO selectDTO);
}
