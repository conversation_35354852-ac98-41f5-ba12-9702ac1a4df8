package com.yelink.dfs.service.impl.product.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * <AUTHOR> @Date 2021/11/3 21:51
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CraftProcedureMaterialUsedExcelDTO {
    @ExcelProperty(value = "工艺编码*")
    private String craftCode;
    @ExcelIgnore
    private Integer craftId;

    @ExcelProperty(value = "工序名称*")
    private String craftProcedureName;
    /**
     * 工艺工序id
     */
    @ExcelIgnore
    private Integer craftProcedureId;

    /**
     * 物料编码
     */
    @ExcelProperty(value = "物料编码*")
    private String materialCode;

    /**
     * 物料名称
     */
    @ExcelProperty(value = "物料名称")
    private String materialName;

    /**
     * 用量
     */
    @ExcelProperty(value = "用量*")
    private Double number;
    /**
     * 单位
     */
    @ExcelIgnore
    private String unit;

    @ExcelProperty(value = "唯一标识符")
    private String uniCode;

    @ExcelProperty(value = "扩展字段")
    private String extend;


    /**
     * 校验是否通过
     */
    @ExcelIgnore
    private Boolean verifyPass;

    @ExcelProperty(value = "导入结果")
    private String importResult;

}
