package com.yelink.dfs.service.impl.barcode;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.NumericNode;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.user.EmployeeEnum;
import com.yelink.dfs.entity.barcode.BarCodeEntity;
import com.yelink.dfs.entity.device.DeviceEntity;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.model.CompanyEntity;
import com.yelink.dfs.entity.model.ModelEntity;
import com.yelink.dfs.entity.order.CustomerMaterialListEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderSelectDTO;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.supplier.SupplierEntity;
import com.yelink.dfs.entity.supplier.SupplierMaterialEntity;
import com.yelink.dfs.entity.target.record.ReportLineEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.user.SysUserRoleEntity;
import com.yelink.dfs.mapper.model.CompanyMapper;
import com.yelink.dfs.mapper.user.enums.EnabledEnum;
import com.yelink.dfs.open.v1.aksk.ams.ExtProductOrderInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtPurchaseReceiptInterface;
import com.yelink.dfs.open.v1.aksk.ams.ExtSaleOrderInterface;
import com.yelink.dfs.service.barcode.BarCodeService;
import com.yelink.dfs.service.barcode.LabelInfoConfigService;
import com.yelink.dfs.service.barcode.LabelPrintService;
import com.yelink.dfs.service.device.DeviceService;
import com.yelink.dfs.service.model.AreaService;
import com.yelink.dfs.service.model.ModelService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.CustomerMaterialListService;
import com.yelink.dfs.service.order.OrderWorkOrderService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.supplier.SupplierMaterialService;
import com.yelink.dfs.service.supplier.SupplierService;
import com.yelink.dfs.service.target.record.ReportLineService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.service.user.SysUserRoleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.common.unit.UnitUtil;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.dfs.barcode.LabelTypeEnum;
import com.yelink.dfscommon.constant.dfs.code.CodeRelationTypeEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderDetailDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderSelectOpenDTO;
import com.yelink.dfscommon.dto.ams.open.purchase.PurchaseReceiptDetailDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseOpenSelectDTO;
import com.yelink.dfscommon.dto.ams.purchase.PurchaseReceiptSelectDTO;
import com.yelink.dfscommon.dto.dfs.CodeInfoSelectDTO;
import com.yelink.dfscommon.dto.dfs.PrintInfoDTO;
import com.yelink.dfscommon.entity.ams.ProductOrderEntity;
import com.yelink.dfscommon.entity.ams.PurchaseEntity;
import com.yelink.dfscommon.entity.ams.PurchaseReceiptEntity;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import com.yelink.dfscommon.entity.dfs.barcode.LabelInfoConfigEntity;
import com.yelink.dfscommon.utils.DateUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.PrintDataUtils;
import com.yelink.dfscommon.utils.Rational;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-08-04 17:44
 */
@Slf4j
@Service
@AllArgsConstructor
public class LabelPrintServiceImpl implements LabelPrintService {

    private BarCodeService barCodeService;
    private MaterialService materialService;
    private SupplierService supplierService;
    private SupplierMaterialService supplierMaterialService;
    private WorkOrderService workOrderService;
    private WorkCenterService workCenterService;
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    private CompanyMapper companyMapper;
    private ModelService modelService;
    private OrderWorkOrderService orderWorkOrderService;
    private AreaService areaService;
    private ExtSaleOrderInterface extSaleOrderInterface;
    private ExtPurchaseReceiptInterface extPurchaseReceiptInterface;
    private ExtProductOrderInterface extProductOrderInterface;
    private SysUserService userService;
    private SysUserRoleService userRoleService;
    private SysRoleService roleService;
    private DeviceService deviceService;
    private BomService bomService;
    private ReportLineService reportLineService;
    private CustomerMaterialListService customerMaterialListService;
    private ExtPurchaseInterface extPurchaseInterface;
    private LabelInfoConfigService labelInfoConfigService;


    @Override
    public String getWorkOrderNumber(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getRelateNumber(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProductOrderNumber(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getRelateNumber(), selectDTO.getSliceDigits());
    }

    @Override
    public String getPurchaseCode(CodeInfoSelectDTO selectDTO) {
        // 如果条码类型为采购收货， 则通过采购收货单找到采购单号
        // 如果条码类型为采购单品码，则直接获取采购单号
        String sliceDigits = selectDTO.getSliceDigits();
        if (selectDTO.getRuleType().equals(LabelTypeEnum.PURCHASE_SINGLE_PRODUCT_CODE.getCode())) {
            return PrintDataUtils.sliceDigits(selectDTO.getRelateNumber(), sliceDigits);
        } else {
            PurchaseReceiptEntity receiptEntity = extPurchaseReceiptInterface.getDetailByCode(PurchaseReceiptDetailDTO.builder().receiptCode(selectDTO.getRelateNumber()).build());
            return receiptEntity == null ? "" : PrintDataUtils.sliceDigits(receiptEntity.getPurchaseCode(), sliceDigits);
        }
    }

    @Override
    public String getPurchaseSingleProductCode(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getPurchaseSingleProductCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProductBatch(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getProductBatch(), selectDTO.getSliceDigits());
    }

    @Override
    public String getPurchaseBatch(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getPurchaseReceiptBatch(), selectDTO.getSliceDigits());
    }

    @Override
    public String getFinishedProductCode(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getFinishedProductCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getOrderProductCode(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getOrderProductCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getMaterialCode(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getMaterialCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getMaterialName(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(materialEntity.getName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getMaterialStandard(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(materialEntity.getStandard(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCurrentDate(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(DateUtil.format(new Date(), DateUtil.DATETIME_FORMAT), selectDTO.getSliceDigits());
    }

    @Override
    public String getReceiptDate(CodeInfoSelectDTO selectDTO) {
        PurchaseReceiptEntity receiptEntity = extPurchaseReceiptInterface.getDetailByCode(PurchaseReceiptDetailDTO.builder().receiptCode(selectDTO.getRelateNumber()).build());
        return Objects.isNull(receiptEntity) || Objects.isNull(receiptEntity.getReceiptTime()) ? "" : PrintDataUtils.sliceDigits(DateUtil.format(receiptEntity.getReceiptTime(), DateUtil.DATETIME_FORMAT), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderName(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getWorkOrderName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkCenterName(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getWorkCenterName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProcedureName(CodeInfoSelectDTO selectDTO) {
        List<WorkOrderProcedureRelationEntity> procedureRelationEntities = workOrderProcedureRelationService.lambdaQuery().eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, selectDTO.getRelateNumber()).list();
        if (CollectionUtils.isEmpty(procedureRelationEntities)) {
            return "";
        }
        String procedureNames = procedureRelationEntities.stream().map(WorkOrderProcedureRelationEntity::getProcedureName).collect(Collectors.joining(Constant.SEP));
        return PrintDataUtils.sliceDigits(procedureNames, selectDTO.getSliceDigits());
    }

    @Override
    public String getProductionBasicUnitName(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        if (Objects.isNull(workOrderEntity)) {
            return "";
        }
        WorkCenterEntity workCenterEntity = workCenterService.getById(workOrderEntity.getProductionBasicUnitId());
        return workCenterEntity == null ? "" : PrintDataUtils.sliceDigits(WorkCenterTypeEnum.getNameByCode(workCenterEntity.getType()), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderPlanQuantity(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        UnitUtil.formatObj(workOrderEntity);
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getPlanQuantity(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderStartDate(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) || Objects.isNull(workOrderEntity.getStartDate()) ? "" : PrintDataUtils.sliceDigits(DateUtil.format(workOrderEntity.getStartDate(), DateUtil.DATETIME_FORMAT), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderEndDate(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) || Objects.isNull(workOrderEntity.getEndDate()) ? "" : PrintDataUtils.sliceDigits(DateUtil.format(workOrderEntity.getEndDate(), DateUtil.DATETIME_FORMAT), selectDTO.getSliceDigits());
    }

    @Override
    public String getMaterialRemark(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(materialEntity.getRemark(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDrawingNumber(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(materialEntity.getDrawingNumber(), selectDTO.getSliceDigits());
    }

    @Override
    public String getMaterialPrice(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getMaterialPrice()), selectDTO.getSliceDigits());
    }

    @Override
    public String getLoseRate(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getLoseRate()), selectDTO.getSliceDigits());
    }

    @Override
    public String getRawMaterial(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(materialEntity.getRawMaterial(), selectDTO.getSliceDigits());
    }

    @Override
    public String getNameEnglish(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(materialEntity.getNameEnglish(), selectDTO.getSliceDigits());
    }

    @Override
    public String getMinimumProductionLot(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getMinimumProductionLot()), selectDTO.getSliceDigits());
    }

    @Override
    public String getFactoryModel(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(materialEntity.getFactoryModel(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldOne(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldOne()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldTwo(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldTwo()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldThree(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldThree()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldFour(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldFour()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldFive(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldFive()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldSix(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldSix()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldSeven(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldSeven()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldEight(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldEight()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldNine(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldNine()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldTen(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldTen()), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomFieldEleven(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(materialEntity.getCustomFieldEleven()), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserId(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(userEntity.getId()), selectDTO.getSliceDigits());
    }

    @Override
    public String getUsername(CodeInfoSelectDTO selectDTO) {
        return Objects.isNull(selectDTO.getUsername()) ? "" : PrintDataUtils.sliceDigits(selectDTO.getUsername(), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserNickName(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getNickname(), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserSex(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(EmployeeEnum.getNameByCode(userEntity.getSex()), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserRoleName(CodeInfoSelectDTO selectDTO) {
        SysUserEntity sysUserEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        if (Objects.isNull(sysUserEntity)) {
            return "";
        }
        List<SysUserRoleEntity> userRoleEntities = userRoleService.lambdaQuery()
                .eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode())
                .eq(SysUserRoleEntity::getUserId, sysUserEntity.getId()).list();
        if (CollectionUtils.isEmpty(userRoleEntities)) {
            return "";
        }
        List<Integer> roleIds = userRoleEntities.stream().map(SysUserRoleEntity::getRoleId).collect(Collectors.toList());
        String roleNames = roleService.listByIds(roleIds).stream().map(SysRoleEntity::getName).collect(Collectors.joining(Constant.SEP));
        return PrintDataUtils.sliceDigits(roleNames, selectDTO.getSliceDigits());
    }

    @Override
    public String getUserMobile(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getMobile(), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserJobNumber(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getJobNumber(), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserDepartmentName(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getDepartmentName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserWeChat(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getWechat(), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserPost(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getPost(), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserEntryDate(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) || Objects.isNull(userEntity.getEntryDate()) ? "" : PrintDataUtils.sliceDigits(DateUtil.format(userEntity.getEntryDate(), DateUtil.DATETIME_FORMAT), selectDTO.getSliceDigits());
    }

    @Override
    public String getUserEmail(CodeInfoSelectDTO selectDTO) {
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, selectDTO.getUsername()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getEmail(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDeviceCode(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getDeviceCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDeviceName(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        return Objects.isNull(deviceEntity) ? "" : PrintDataUtils.sliceDigits(deviceEntity.getDeviceName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDeviceTypeName(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        ModelEntity modelEntity = modelService.getById(deviceEntity.getModelId());
        return Objects.isNull(deviceEntity) ? "" : PrintDataUtils.sliceDigits(modelEntity.getName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDeviceBrandModel(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        return Objects.isNull(deviceEntity) ? "" : PrintDataUtils.sliceDigits(deviceEntity.getBrandModel(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDeviceRemark(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        return Objects.isNull(deviceEntity) ? "" : PrintDataUtils.sliceDigits(deviceEntity.getRemark(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDevicePurchaseDate(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        return Objects.isNull(deviceEntity) ? "" : PrintDataUtils.sliceDigits(DateUtil.format(deviceEntity.getPurchaseDate(), DateUtil.DATETIME_FORMAT), selectDTO.getSliceDigits());
    }

    @Override
    public String getDeviceMagName(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        if (Objects.isNull(deviceEntity)) {
            return "";
        }
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, deviceEntity.getMagName()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getNickname(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDeviceMaintainer(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        if (Objects.isNull(deviceEntity)) {
            return "";
        }
        SysUserEntity userEntity = userService.lambdaQuery().eq(SysUserEntity::getUsername, deviceEntity.getMaintainer()).one();
        return Objects.isNull(userEntity) ? "" : PrintDataUtils.sliceDigits(userEntity.getNickname(), selectDTO.getSliceDigits());
    }

    @Override
    public String getDevicePlace(CodeInfoSelectDTO selectDTO) {
        DeviceEntity deviceEntity = deviceService.lambdaQuery().eq(DeviceEntity::getDeviceCode, selectDTO.getDeviceCode()).one();
        return Objects.isNull(deviceEntity) ? "" : PrintDataUtils.sliceDigits(deviceEntity.getPlace(), selectDTO.getSliceDigits());
    }

    @Override
    public String getBomCode(CodeInfoSelectDTO selectDTO) {
        BomEntity bomEntity = bomService.getById(selectDTO.getBomId());
        return Objects.isNull(bomEntity) ? "" : PrintDataUtils.sliceDigits(bomEntity.getBomNum(), selectDTO.getSliceDigits());
    }

    @Override
    public String getBatchFinishCount(CodeInfoSelectDTO selectDTO) {
        BarCodeEntity barCodeEntity = barCodeService.lambdaQuery().eq(BarCodeEntity::getBarCode, selectDTO.getBatch()).one();
        UnitUtil.formatObj(barCodeEntity);
        return Objects.isNull(barCodeEntity) || Objects.isNull(barCodeEntity.getFinishCount()) ? "" : PrintDataUtils.sliceDigits(barCodeEntity.getFinishCount(), selectDTO.getSliceDigits());
    }

    @Override
    public String getLineReportFinishCount(CodeInfoSelectDTO selectDTO) {
        ReportLineEntity reportLineEntity = reportLineService.getById(selectDTO.getWorkOrderReportId());
        UnitUtil.formatObj(reportLineEntity);
        return Objects.isNull(reportLineEntity) || Objects.isNull(reportLineEntity.getFinishCount()) ? "" : PrintDataUtils.sliceDigits(reportLineEntity.getFinishCount(), selectDTO.getSliceDigits());
    }

    @Override
    public String getLineReportUnqualifiedCount(CodeInfoSelectDTO selectDTO) {
        ReportLineEntity reportLineEntity = reportLineService.getById(selectDTO.getWorkOrderReportId());
        UnitUtil.formatObj(reportLineEntity);
        return Objects.isNull(reportLineEntity) || Objects.isNull(reportLineEntity.getUnqualified()) ? "" : PrintDataUtils.sliceDigits(reportLineEntity.getUnqualified(), selectDTO.getSliceDigits());

    }

    @Override
    public String getLineReportTime(CodeInfoSelectDTO selectDTO) {
        ReportLineEntity reportLineEntity = reportLineService.getById(selectDTO.getWorkOrderReportId());
        return Objects.isNull(reportLineEntity) ? "" : PrintDataUtils.sliceDigits(DateUtil.format(reportLineEntity.getReportTime(), DateUtil.DATE_FORMAT), selectDTO.getSliceDigits());
    }

    @Override
    public String getLineOperator(CodeInfoSelectDTO selectDTO) {
        ReportLineEntity reportLineEntity = reportLineService.getById(selectDTO.getWorkOrderReportId());
        return Objects.isNull(reportLineEntity) || Objects.isNull(reportLineEntity.getOperatorName()) ? "" : PrintDataUtils.sliceDigits(reportLineEntity.getOperatorName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderRemark(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getRemark(), selectDTO.getSliceDigits());
    }

    @Override
    public String getMaterialScaleFactor(CodeInfoSelectDTO selectDTO) {
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        return Objects.isNull(materialEntity) ? "" : PrintDataUtils.sliceDigits(String.valueOf(new Rational(materialEntity.getUnitNumerator(), materialEntity.getUnitDenominator()).doubleValue()), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderCustomerMaterialCode(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getCustomerMaterialCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderCustomerMaterialName(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getCustomerMaterialName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderCustomerSpecification(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getCustomerSpecification(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProductOrderCustomerMaterialCode(CodeInfoSelectDTO selectDTO) {
        ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(selectDTO.getRelateNumber()).build());
        if (productOrderEntity == null) {
            return "";
        }
        return PrintDataUtils.sliceDigits(productOrderEntity.getProductOrderMaterial().getCustomerMaterialCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProductOrderCustomerMaterialName(CodeInfoSelectDTO selectDTO) {
        ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(selectDTO.getRelateNumber()).build());
        if (productOrderEntity == null) {
            return "";
        }
        return PrintDataUtils.sliceDigits(productOrderEntity.getProductOrderMaterial().getCustomerMaterialName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProductOrderCustomerSpecification(CodeInfoSelectDTO selectDTO) {
        ProductOrderEntity productOrderEntity = extProductOrderInterface.selectProductOrderByNumber(ProductOrderDetailDTO.builder().productOrderNumber(selectDTO.getRelateNumber()).build());
        if (productOrderEntity == null) {
            return "";
        }
        return PrintDataUtils.sliceDigits(productOrderEntity.getProductOrderMaterial().getCustomerSpecification(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomerCode(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getCustomerCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomerMaterialName(CodeInfoSelectDTO selectDTO) {
        CustomerMaterialListEntity one = null;
        if (StringUtils.isNotBlank(selectDTO.getCustomerCode()) && StringUtils.isNotBlank(selectDTO.getMaterialCode())) {
            one = customerMaterialListService.lambdaQuery()
                    .eq(CustomerMaterialListEntity::getCustomerCode, selectDTO.getCustomerCode())
                    .eq(CustomerMaterialListEntity::getMaterialCode, selectDTO.getMaterialCode())
                    .one();
        }
        return one == null ? "" : PrintDataUtils.sliceDigits(one.getCustomerMaterialName(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomerMaterialStandard(CodeInfoSelectDTO selectDTO) {
        CustomerMaterialListEntity one = null;
        if (StringUtils.isNotBlank(selectDTO.getCustomerCode()) && StringUtils.isNotBlank(selectDTO.getMaterialCode())) {
            one = customerMaterialListService.lambdaQuery()
                    .eq(CustomerMaterialListEntity::getCustomerCode, selectDTO.getCustomerCode())
                    .eq(CustomerMaterialListEntity::getMaterialCode, selectDTO.getMaterialCode())
                    .one();
        }
        return one == null ? "" : PrintDataUtils.sliceDigits(one.getCustomerMaterialStandard(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomerMaterialCode(CodeInfoSelectDTO selectDTO) {
        CustomerMaterialListEntity one = null;
        if (StringUtils.isNotBlank(selectDTO.getCustomerCode()) && StringUtils.isNotBlank(selectDTO.getMaterialCode())) {
            one = customerMaterialListService.lambdaQuery()
                    .eq(CustomerMaterialListEntity::getCustomerCode, selectDTO.getCustomerCode())
                    .eq(CustomerMaterialListEntity::getMaterialCode, selectDTO.getMaterialCode())
                    .one();
        }
        return one == null ? "" : PrintDataUtils.sliceDigits(one.getCustomerMaterialCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomerNames(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getCustomerNames(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderRelateProductOrderNumber(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getProductOrderNumber(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderCustomerCode(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getCustomerCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getRelateInspectOrder(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getRelateInspectOrder(), selectDTO.getSliceDigits());
    }

    @Override
    public String getSupplier(CodeInfoSelectDTO selectDTO) {
        if (selectDTO.getRuleType().equals(LabelTypeEnum.PURCHASE.getCode())) {
            PurchaseReceiptEntity receiptEntity = extPurchaseReceiptInterface.getDetailByCode(PurchaseReceiptDetailDTO.builder().receiptCode(selectDTO.getRelateNumber()).build());
            return receiptEntity != null ? PrintDataUtils.sliceDigits(receiptEntity.getSupplierCode(), selectDTO.getSliceDigits()) : "";
        }
        if (selectDTO.getRuleType().equals(LabelTypeEnum.FINISHED.getCode())) {
            List<SupplierMaterialEntity> entities = getSupplierMaterialEntities(selectDTO.getMaterialCode());
            if (!CollectionUtils.isEmpty(entities)) {
                SupplierMaterialEntity supplierMaterialEntity = entities.get(0);
                SupplierEntity supplierEntity = supplierService.getById(supplierMaterialEntity.getSupplierId());
                return supplierEntity == null ? "" : PrintDataUtils.sliceDigits(supplierEntity.getCode(), selectDTO.getSliceDigits());
            }
        }
        return "";
    }

    @Override
    public String getSupplierName(CodeInfoSelectDTO selectDTO) {
        if (selectDTO.getRuleType().equals(LabelTypeEnum.PURCHASE.getCode())) {
            PurchaseReceiptEntity receiptEntity = extPurchaseReceiptInterface.getDetailByCode(PurchaseReceiptDetailDTO.builder().receiptCode(selectDTO.getRelateNumber()).build());
            return receiptEntity == null ? "" : PrintDataUtils.sliceDigits(receiptEntity.getSupplierName(), selectDTO.getSliceDigits());
        }
        if (selectDTO.getRuleType().equals(LabelTypeEnum.FINISHED.getCode())) {
            List<SupplierMaterialEntity> entities = getSupplierMaterialEntities(selectDTO.getMaterialCode());
            if (!CollectionUtils.isEmpty(entities)) {
                SupplierMaterialEntity supplierMaterialEntity = entities.get(0);
                SupplierEntity supplierEntity = supplierService.getById(supplierMaterialEntity.getSupplierId());
                return supplierEntity == null ? "" : PrintDataUtils.sliceDigits(supplierEntity.getName(), selectDTO.getSliceDigits());
            }
        }
        return "";
    }

    @Override
    public String getSupplierMaterialCode(CodeInfoSelectDTO selectDTO) {
        SupplierEntity supplierEntity = supplierService.lambdaQuery().eq(SupplierEntity::getName, selectDTO.getSupplierName()).one();
        if (supplierEntity == null) {
            return "";
        }
        MaterialEntity materialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, selectDTO.getMaterialCode()).one();
        SupplierMaterialEntity supplierMaterialEntity = supplierMaterialService.lambdaQuery().eq(SupplierMaterialEntity::getSupplierId, supplierEntity.getId())
                .eq(SupplierMaterialEntity::getMaterialId, materialEntity.getId())
                .one();
        return Objects.isNull(supplierMaterialEntity) || StringUtils.isBlank(supplierMaterialEntity.getSupplierMaterialCode()) ? "" : PrintDataUtils.sliceDigits(supplierMaterialEntity.getSupplierMaterialCode(), selectDTO.getSliceDigits());

    }

    @Override
    public String getSupplierContactWay(CodeInfoSelectDTO selectDTO) {
        SupplierEntity supplierEntity = supplierService.lambdaQuery().eq(SupplierEntity::getName, selectDTO.getSupplierName()).one();
        return Objects.isNull(supplierEntity) ? "" : PrintDataUtils.sliceDigits(supplierEntity.getPhone(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProducer(CodeInfoSelectDTO selectDTO) {
        List<CompanyEntity> list = companyMapper.selectList(null);
        return !CollectionUtils.isEmpty(list) ? PrintDataUtils.sliceDigits(list.get(0).getCname(), selectDTO.getSliceDigits()) : null;
    }

    @Override
    public String getBarCodeCount(CodeInfoSelectDTO selectDTO) {
        // 获取批次数量
        BarCodeEntity barCodeEntity = barCodeService.lambdaQuery().eq(BarCodeEntity::getBarCode, selectDTO.getBatch()).one();
        UnitUtil.formatObj(barCodeEntity);
        return Objects.isNull(barCodeEntity) || Objects.isNull(barCodeEntity.getCount()) ? "" : PrintDataUtils.sliceDigits(barCodeEntity.getCount(), selectDTO.getSliceDigits());
    }

    @Override
    public String getBatchRemark(CodeInfoSelectDTO selectDTO) {
        BarCodeEntity barCodeEntity = barCodeService.lambdaQuery().eq(BarCodeEntity::getBarCode, selectDTO.getBatch()).one();
        return Objects.isNull(barCodeEntity) ? "" : PrintDataUtils.sliceDigits(barCodeEntity.getRemarks(), selectDTO.getSliceDigits());
    }

    @Override
    public String getLineModelCode(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        if (Objects.isNull(workOrderEntity)) {
            return "";
        }
        ModelEntity modelEntity = modelService.getById(workOrderEntity.getLineId());
        return Objects.isNull(modelEntity) ? "" : PrintDataUtils.sliceDigits(modelEntity.getCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getLineCode(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getLineCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getProductFlowCode(CodeInfoSelectDTO selectDTO) {
        return PrintDataUtils.sliceDigits(selectDTO.getProductFlowCode(), selectDTO.getSliceDigits());
    }

    @Override
    public String getWorkOrderActualFinishDate(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) || Objects.isNull(workOrderEntity.getActualEndDate()) ? PrintDataUtils.sliceDigits(DateUtil.format(workOrderEntity.getActualEndDate(), DateUtil.DATETIME_FORMAT), selectDTO.getSliceDigits()) : null;
    }

    @Override
    public String getAreaName(CodeInfoSelectDTO selectDTO) {
        List<AreaEntity> areaEntities = areaService.list();
        return CollectionUtils.isEmpty(areaEntities) ? "" : PrintDataUtils.sliceDigits(areaEntities.get(0).getAname(), selectDTO.getSliceDigits());
    }

    @Override
    public String getCustomerName(CodeInfoSelectDTO selectDTO) {
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(selectDTO.getRelateNumber());
        return Objects.isNull(workOrderEntity) ? "" : PrintDataUtils.sliceDigits(workOrderEntity.getCustomerName(), selectDTO.getSliceDigits());
    }

    /**
     * 获取供应商物料对象
     *
     * @param materialCode
     * @return
     */
    private List<SupplierMaterialEntity> getSupplierMaterialEntities(String materialCode) {
        MaterialEntity materialEntity = materialService.getSimpleMaterialByCode(materialCode);
        Integer materialId = materialEntity.getId();
        LambdaQueryWrapper<SupplierMaterialEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SupplierMaterialEntity::getMaterialId, materialId);
        return supplierMaterialService.list(wrapper);
    }

    @Override
    public Map<String, PrintInfoDTO> getPrintInfoMap(String relateType, List<String> relateNumbers) {
        Map<String, PrintInfoDTO> printInfoMap = new HashMap<>();
        if (StringUtils.isBlank(relateType) || CollectionUtils.isEmpty(relateNumbers)) {
            return printInfoMap;
        }
        if (CodeRelationTypeEnum.WORK_ORDER.getCodeStr().equals(relateType)) {
            // 生产工单
            WorkOrderSelectDTO workOrderSelectDTO = WorkOrderSelectDTO.builder()
                    .workOrderNumbers(relateNumbers).build();
            List<WorkOrderEntity> list = workOrderService.getWorkOrderEntityPage(workOrderSelectDTO, null).getRecords();
            List<String> productOrderNumbers = list.stream().map(WorkOrderEntity::getProductOrderNumber).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            // 查询生产订单信息
            Map<String, ProductOrderEntity> productOrderEntityMap = this.getProductOrderEntities(productOrderNumbers)
                    .stream().collect(Collectors.toMap(ProductOrderEntity::getProductOrderNumber, v -> v));

            for (WorkOrderEntity entity : list) {
                PrintInfoDTO dto = new PrintInfoDTO();
                dto.setWorkOrderEntity(JacksonUtil.convertObject(entity, com.yelink.dfscommon.entity.dfs.WorkOrderEntity.class));
                if (entity != null) {
                    dto.setProductOrderEntity(productOrderEntityMap.get(entity.getProductOrderNumber()));
                }
                printInfoMap.put(entity.getWorkOrderNumber(), dto);
            }
        } else if (CodeRelationTypeEnum.PRODUCT_ORDER.getCodeStr().equals(relateType)) {
            // 生产订单
            List<ProductOrderEntity> list = this.getProductOrderEntities(relateNumbers);
            for (ProductOrderEntity entity : list) {
                PrintInfoDTO dto = new PrintInfoDTO();
                dto.setProductOrderEntity(entity);
                printInfoMap.put(entity.getProductOrderNumber(), dto);
            }
        } else if (CodeRelationTypeEnum.PURCHASE_ORDER.getCodeStr().equals(relateType)) {
            // 采购订单
            List<PurchaseEntity> list = this.getPurchaseEntities(relateNumbers);
            for (PurchaseEntity entity : list) {
                PrintInfoDTO dto = new PrintInfoDTO();
                dto.setPurchaseEntity(entity);
                printInfoMap.put(entity.getPurchaseCode(), dto);
            }
        } else if (CodeRelationTypeEnum.PURCHASE_RECEIPT.getCodeStr().equals(relateType)) {
            // 采购收料单
            PurchaseReceiptSelectDTO selectDTO = PurchaseReceiptSelectDTO.builder().showType(ShowTypeEnum.ORDER.getType())
                    .receiptCodes(relateNumbers).build();
            List<PurchaseReceiptEntity> list = extPurchaseReceiptInterface.getPage(selectDTO).getRecords();
            if (CollectionUtils.isEmpty(list)) {
                return printInfoMap;
            }
            List<String> purchaseCodes = list.stream().map(PurchaseReceiptEntity::getPurchaseCode).distinct().collect(Collectors.toList());
            Map<String, PurchaseEntity> purchaseEntityMap = this.getPurchaseEntities(purchaseCodes)
                    .stream().collect(Collectors.toMap(PurchaseEntity::getPurchaseCode, v -> v));
            for (PurchaseReceiptEntity entity : list) {
                PrintInfoDTO dto = new PrintInfoDTO();
                dto.setPurchaseEntity(purchaseEntityMap.get(entity.getPurchaseCode()));
                printInfoMap.put(entity.getReceiptCode(), dto);
            }
        } else {
            return printInfoMap;
        }
        return printInfoMap;
    }

    @Override
    public String getPlaceholderValue(CodeInfoSelectDTO selectDTO) {
        String placeholder = selectDTO.getPlaceholder();
        LabelInfoConfigEntity infoConfigEntity = selectDTO.getLabelInfoConfigEntity();
        // 基础字段和非系统字段不处理
        if (infoConfigEntity == null || Constants.LABEL_BASE.equals(infoConfigEntity.getModuleCode())
                || Boolean.FALSE.equals(infoConfigEntity.getIsSysField())) {
            // 原封不动的返回
            return placeholder;
        }
        PrintInfoDTO dto = selectDTO.getPrintInfoDto();
        if (dto == null) {
            dto = new PrintInfoDTO();
        }
        UnitUtil.formatObj(dto);
        JsonNode jsonNode = JacksonUtil.convertObject(dto, JsonNode.class);
        // 去除占位符 ${ }
        String propertyPath = placeholder.substring(2, placeholder.length() - 1);
        // 从json对象中获取实际值
        JsonNode valueNode = JacksonUtil.getValue(jsonNode, propertyPath);
        if(valueNode instanceof NumericNode) {
            return PrintDataUtils.sliceDigits(valueNode.numberValue(), selectDTO.getSliceDigits());
        }else {
            return valueNode == null? "" : PrintDataUtils.sliceDigits(valueNode.textValue(), selectDTO.getSliceDigits());
        }
    }

    /**
     * 查询生产订单信息
     * @param relateNumbers
     * @return
     */
    private List<ProductOrderEntity> getProductOrderEntities(List<String> relateNumbers) {
        if (CollectionUtils.isEmpty(relateNumbers)) {
            return new ArrayList<>();
        }
        ProductOrderSelectOpenDTO productOrderSelectDTO = ProductOrderSelectOpenDTO.builder()
                .productOrderNumbers(relateNumbers).build();
        List<ProductOrderEntity> records = extProductOrderInterface.getPage(productOrderSelectDTO).getRecords();
        return CollectionUtils.isEmpty(records) ? new ArrayList<>() : records;
    }

    /**
     * 查询采购订单信息
     * @param relateNumbers
     * @return
     */
    private List<PurchaseEntity> getPurchaseEntities(List<String> relateNumbers) {
        if (CollectionUtils.isEmpty(relateNumbers)) {
            return new ArrayList<>();
        }
        PurchaseOpenSelectDTO purchaseOpenSelectDTO = PurchaseOpenSelectDTO.builder().showType(ShowTypeEnum.ORDER.getType())
                .purchaseCodes(relateNumbers).build();
        List<PurchaseEntity> records = extPurchaseInterface.getPage(purchaseOpenSelectDTO).getRecords();
        return CollectionUtils.isEmpty(records) ? new ArrayList<>() : records;
    }

}
