package com.yelink.dfs.service.impl.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yelink.dfs.entity.manufacture.ProductionLineEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.pack.PackageOrderEntity;
import com.yelink.dfs.entity.terminal.SysPadBindRoleEntity;
import com.yelink.dfs.entity.terminal.SysPadEntity;
import com.yelink.dfs.entity.user.SysRoleEntity;
import com.yelink.dfs.entity.user.SysRoleProductionBasicUnitEntity;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.entity.user.SysUserRoleEntity;
import com.yelink.dfs.mapper.manufacture.ProductionLineMapper;
import com.yelink.dfs.mapper.model.WorkCenterMapper;
import com.yelink.dfs.mapper.terminal.SysPadMapper;
import com.yelink.dfs.mapper.user.SysRoleMapper;
import com.yelink.dfs.mapper.user.enums.EnabledEnum;
import com.yelink.dfs.service.order.IsolationService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.user.SysRoleProductionBasicUnitService;
import com.yelink.dfs.service.user.SysRoleService;
import com.yelink.dfs.service.user.SysUserRoleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.dto.IsolationDTO;
import com.yelink.dfscommon.entity.dfs.WorkCenterEntity;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service
 *
 * <AUTHOR>
 * @Date 2021-07-20 19:08
 */
@AllArgsConstructor
@Service
public class IsolationServiceImpl implements IsolationService {

    private SysUserService userService;
    private SysUserRoleService userRoleService;
    private SysPadMapper sysPadMapper;
    private SysRoleProductionBasicUnitService roleProductionBasicUnitService;
    private SysRoleMapper sysRoleMapper;
    private SysRoleService roleService;
    private ProductionLineMapper productionLineMapper;
    private WorkCenterMapper workCenterMapper;
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;

    /**
     * 数据隔离，只能查询角色绑定工作中心下的工单
     *
     * @param username 用户名
     * @return false为无任何权限
     */
    @Override
    public Boolean dataIsolation(String username, LambdaQueryWrapper<WorkOrderEntity> wrapper) {
        IsolationDTO dto = this.getIsolationDTO(username);
        if (dto.getAllWorkCenter()) {
            return true;
        }
        List<String> isolationIds = dto.getIsolationIds();
        if (CollectionUtils.isEmpty(isolationIds)) {
            return false;
        }
        //生产基本单元精准隔离或工作中心下生产基本单元为空的（生产基本单元为空时， isolationId = workCenterId）
        // 工单对应多个生产基本单元处理
        List<WorkOrderBasicUnitRelationEntity> relations = workOrderBasicUnitRelationService.lambdaQuery()
                .in(WorkOrderBasicUnitRelationEntity::getIsolationId, isolationIds)
                .list();
        boolean hasFirstCondition = !CollectionUtils.isEmpty(dto.getWorkCenterIds());
        boolean hasSecondCondition = !CollectionUtils.isEmpty(relations);
        if (hasFirstCondition || hasSecondCondition) {
            wrapper.and(lambWrapper -> {
                lambWrapper.in(hasFirstCondition, WorkOrderEntity::getIsolationId, dto.getWorkCenterIds());
                if (hasSecondCondition) {
                    List<String> workOrderNumbers = relations.stream().map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber).collect(Collectors.toList());
                    // 当两个条件都存在时才需要 OR
                    if (hasFirstCondition) {
                        lambWrapper.or();
                    }
                    lambWrapper.in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers);
                }
            });
        }
        return true;
    }

    /**
     * 数据隔离，只能查询角色绑定工作中心下的包装工单
     *
     * @param username 用户名
     * @return false为无任何权限
     */
    @Override
    public Boolean packageDataIsolation(String username, LambdaQueryWrapper<PackageOrderEntity> wrapper) {
        IsolationDTO dto = this.getIsolationDTO(username);
        if (dto.getAllWorkCenter()) {
            return true;
        }
        List<String> isolationIds = dto.getIsolationIds();
        if (CollectionUtils.isEmpty(isolationIds)) {
            return false;
        }
        //生产基本单元精准隔离或工作中心下生产基本单元为空的（生产基本单元为空时， isolationId = workCenterId）
        wrapper.and(lambWrapper -> lambWrapper.in(PackageOrderEntity::getIsolationId, isolationIds).or()
                .in(PackageOrderEntity::getIsolationId, dto.getWorkCenterIds()));
        return true;
    }

    /**
     * 数据隔离，根据用户名获取隔离信息
     */
    @Override
    public IsolationDTO getIsolationDTO(String username) {
        IsolationDTO isolationDTO = new IsolationDTO();
        List<Integer> roleIds = getRoleIdsByUsername(username);
        if (CollectionUtils.isEmpty(roleIds)) {
            return isolationDTO;
        }
        List<SysRoleEntity> sysRoleEntities = roleService.listByIds(roleIds);
        for (SysRoleEntity roleEntity : sysRoleEntities) {
            if (roleEntity.getAllWorkCenter() != null && roleEntity.getAllWorkCenter()) {
                isolationDTO.setAllWorkCenter(true);
                return isolationDTO;
            }
        }

        List<String> isolationIds = roleProductionBasicUnitService.getIsolationIdsByRoleIds(roleIds);
        List<String> workCenterIds = roleProductionBasicUnitService.getList(roleIds, null, null)
                .stream().map(SysRoleProductionBasicUnitEntity::getWorkCenterId).map(String::valueOf)
                .distinct().collect(Collectors.toList());
        isolationDTO.setIsolationIds(isolationIds);
        isolationDTO.setWorkCenterIds(workCenterIds);
        return isolationDTO;
    }

    /**
     * 获取隔离的生产基本单元id列表
     */
    @Override
    public IsolationDTO getIsolationDTO(String username, String type, Integer workCenterId) {
        IsolationDTO isolationDTO = new IsolationDTO();
        List<Integer> roleIds = getRoleIdsByUsername(username);
        if (CollectionUtils.isEmpty(roleIds)) {
            return isolationDTO;
        }
        List<SysRoleEntity> sysRoleEntities = roleService.listByIds(roleIds);
        for (SysRoleEntity roleEntity : sysRoleEntities) {
            if (roleEntity.getAllWorkCenter()) {
                isolationDTO.setAllWorkCenter(true);
                return isolationDTO;
            }
        }

        List<Integer> ids = roleProductionBasicUnitService.getList(roleIds, type, workCenterId)
                .stream().map(SysRoleProductionBasicUnitEntity::getProductionBasicUnitId).distinct().collect(Collectors.toList());
        isolationDTO.setIds(ids);
        return isolationDTO;
    }

    /**
     * 获取数据隔离产线id列表
     */
    @Override
    public List<Integer> getIsolationLineIds(String username) {
        List<Integer> lineIds = new ArrayList<>();
        List<Integer> roleIds = getRoleIdsByUsername(username);
        if (CollectionUtils.isEmpty(roleIds)) {
            return lineIds;
        }
        List<SysRoleEntity> sysRoleEntities = roleService.listByIds(roleIds);
        for (SysRoleEntity roleEntity : sysRoleEntities) {
            if (roleEntity.getAllWorkCenter()) {
                LambdaQueryWrapper<ProductionLineEntity> queryWrapper = new LambdaQueryWrapper<>();
                return productionLineMapper.selectList(queryWrapper)
                        .stream().map(ProductionLineEntity::getProductionLineId).collect(Collectors.toList());
            }
        }
        lineIds = roleProductionBasicUnitService.getList(roleIds, WorkCenterTypeEnum.LINE.getCode(), null)
                .stream().map(SysRoleProductionBasicUnitEntity::getProductionBasicUnitId).distinct().collect(Collectors.toList());
        return lineIds;
    }

    /**
     * 获取数据隔离工作中心id列表
     */
    @Override
    public List<Integer> getIsolationWorkCenterIds(String username) {
        List<Integer> workCenterIds = new ArrayList<>();
        List<Integer> roleIds = getRoleIdsByUsername(username);
        if (CollectionUtils.isEmpty(roleIds)) {
            return workCenterIds;
        }
        List<SysRoleEntity> sysRoleEntities = roleService.listByIds(roleIds);
        for (SysRoleEntity roleEntity : sysRoleEntities) {
            if (roleEntity.getAllWorkCenter()) {
                LambdaQueryWrapper<WorkCenterEntity> queryWrapper = new LambdaQueryWrapper<>();
                return workCenterMapper.selectList(queryWrapper)
                        .stream().map(WorkCenterEntity::getId).collect(Collectors.toList());
            }
        }
        workCenterIds = roleProductionBasicUnitService.getList(roleIds, null, null)
                .stream().map(SysRoleProductionBasicUnitEntity::getWorkCenterId).distinct().collect(Collectors.toList());
        return workCenterIds;
    }

    private List<Integer> getRoleIdsByUsername(String username) {
        List<Integer> roleIds = new ArrayList<>();
        if (StringUtils.isBlank(username)) {
            return roleIds;
        }
        SysUserEntity sysUserEntity = userService.selectByUsername(username);
        if (sysUserEntity != null) {
            roleIds = userRoleService.lambdaQuery().eq(SysUserRoleEntity::getEnabled, EnabledEnum.ENABLE.getCode())
                    .eq(SysUserRoleEntity::getUserId, sysUserEntity.getId())
                    .list().stream()
                    .map(SysUserRoleEntity::getRoleId).collect(Collectors.toList());
        } else {
            // 查询设备对应角色及产线权限
            SysPadEntity sysPadEntity = sysPadMapper.selectByPadMac(username);
            if (sysPadEntity != null) {
                roleIds = sysRoleMapper.selectRoleBindPad(sysPadEntity.getId())
                        .stream().map(SysPadBindRoleEntity::getRoleId).collect(Collectors.toList());
            }
        }
        return roleIds;
    }

}
