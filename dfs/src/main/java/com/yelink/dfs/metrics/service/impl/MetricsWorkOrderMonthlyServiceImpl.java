package com.yelink.dfs.metrics.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.HashBasedTable;
import com.yelink.dfs.constant.Constant;
import com.yelink.dfs.constant.code.MaintainTypeEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.attendance.dto.AttendanceRecordDTO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO;
import com.yelink.dfs.entity.capacity.vo.CapacityVO;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.model.AreaEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderDayCountEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.order.dto.ShowExtraNameDTO;
import com.yelink.dfs.entity.reporter.dto.CodeReportAccessDTO;
import com.yelink.dfs.metrics.entity.MetricsWorkOrderMonthlyEntity;
import com.yelink.dfs.metrics.entity.dto.BasicUnitDTO;
import com.yelink.dfs.metrics.mapper.MetricsWorkOrderMonthlyMapper;
import com.yelink.dfs.metrics.service.CommonMetricsService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.attendance.AttendanceRecordService;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.code.CodeReportService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.model.WorkCenterService;
import com.yelink.dfs.service.order.RecordWorkOrderDayCountService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitInputRecordService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.metrics.api.MetricsApi;
import com.yelink.metrics.api.time.MonthlyVO;
import com.yelink.metrics.core.domain.TargetModel;
import com.yelink.metrics.core.service.BaseMetricsService;
import com.yelink.metrics.core.service.BaseMetricsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MetricsWorkOrderMonthlyServiceImpl extends BaseMetricsServiceImpl<MetricsWorkOrderMonthlyMapper, MetricsWorkOrderMonthlyEntity> implements BaseMetricsService<MetricsWorkOrderMonthlyEntity> {

    @Resource
    private MetricsApi metricsApi;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private RecordWorkOrderDayCountService recordWorkOrderDayCountService;
    @Resource
    private MaintainRecordService maintainRecordService;
    @Resource
    private AttendanceRecordService attendanceRecordService;
    @Resource
    private CodeReportService codeReportService;
    @Resource
    private WorkOrderBasicUnitInputRecordService workOrderBasicUnitInputRecordService;
    @Resource
    private CapacityService capacityService;
    @Resource
    private WorkOrderPlanService workOrderPlanService;
    @Resource
    private AlarmService alarmService;
    @Resource
    private CommonMetricsService commonMetricsService;
    @Resource
    private RecordWorkOrderUnqualifiedService recordWorkOrderUnqualifiedService;
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;
    @Resource
    private GridService gridService;
    @Resource
    private WorkCenterService workCenterService;

    @Override
    public List<MetricsWorkOrderMonthlyEntity> deal(TargetModel targetModel) {
        MonthlyVO monthly = metricsApi.getMonthly();

        // v3.23 计划 + 投产 + 实际生产
        // 今日计划
        List<WorkOrderPlanEntity> workOrderPlans = workOrderPlanService.lambdaQuery()
                .ge(WorkOrderPlanEntity::getTime, monthly.getRecordTime())
                .lt(WorkOrderPlanEntity::getTime, monthly.getEndRecordTime())
                .list();
        // 资源投入工时
        List<WorkOrderBasicUnitInputRecordEntity> basicUnitInputRecords = workOrderBasicUnitInputRecordService.lambdaQuery()
                // 投产开始时间 < 最晚时刻
                .lt(WorkOrderBasicUnitInputRecordEntity::getStartTime, monthly.getEndTime())
                .and(o -> o
                        // 投产结束时间 > 最早时刻 || 投产结束时间为空
                        .gt(WorkOrderBasicUnitInputRecordEntity::getEndTime, monthly.getStartTime())
                        .or()
                        .isNull(WorkOrderBasicUnitInputRecordEntity::getEndTime)
                )
                .list();
        // 每日的统计
        List<RecordWorkOrderDayCountEntity> dayCounts = recordWorkOrderDayCountService.list(Wrappers.lambdaQuery(RecordWorkOrderDayCountEntity.class)
                .ge(RecordWorkOrderDayCountEntity::getTime, monthly.getRecordTime())
                .lt(RecordWorkOrderDayCountEntity::getTime, monthly.getEndRecordTime())
        );
        // 根据 计划 + 投产 + 实际生产 --> 工单号集合
        List<String> workOrderNumbers = Stream.of(
                workOrderPlans.stream().map(WorkOrderPlanEntity::getWorkOrderNumber),
                basicUnitInputRecords.stream().map(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber),
                dayCounts.stream().map(RecordWorkOrderDayCountEntity::getWorkOrderNumber)
        ).flatMap(Function.identity()).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        if(CollUtil.isEmpty(workOrderNumbers)) {
            return Collections.emptyList();
        }
        List<WorkOrderEntity> allWorkOrders = workOrderService.simpleList(workOrderNumbers);
        // 构建分组
        Map<String, List<WorkOrderPlanEntity>> workOrderPlanGroup = workOrderPlans.stream().collect(Collectors.groupingBy(WorkOrderPlanEntity::getWorkOrderNumber));
        Map<String, Double> resourceInputMap = basicUnitInputRecords.stream().collect(
                Collectors.groupingBy(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, Collectors.summingDouble(e -> e.getDurationH(monthly.getStartTime(), monthly.getEndTime())))
        );
        Map<String, List<RecordWorkOrderDayCountEntity>> dayCountGroup = dayCounts.stream().collect(Collectors.groupingBy(RecordWorkOrderDayCountEntity::getWorkOrderNumber));

        // 维修相关
        List<MaintainRecordEntity> maintainRecords = maintainRecordService.lambdaQuery()
                .in(MaintainRecordEntity::getWorkOrder, workOrderNumbers)
                .between(MaintainRecordEntity::getCreateTime,  monthly.getStartTime(),  monthly.getEndTime())
                .list();
        Map<String, List<MaintainRecordEntity>> maintainRecordsMap = maintainRecords.stream().collect(Collectors.groupingBy(MaintainRecordEntity::getWorkOrder));
        // 人员工时统计
        Page<AttendanceRecordVO> page = attendanceRecordService.recordList(
                AttendanceRecordDTO.builder()
                        .workOrderNumbers(workOrderNumbers)
                        .startRecordDate(monthly.getRecordTime())
                        .endRecordDate(monthly.getEndRecordTime())
                        .simple(true)
                        .smartCal(true)
                        .build()
        );
        Map<String, List<AttendanceRecordVO>> attendanceRecordsMap = page.getRecords().stream().collect(Collectors.groupingBy(AttendanceRecordVO::getWorkOrderNumber));

        // 扫码相关
        Map<String, Integer> directAccessMap = codeReportService.accessQuantityMap(CodeReportAccessDTO.builder()
                .isFirstUnqualified(false)
                .relationNumbers(workOrderNumbers)
                .reportTimeDown(monthly.getStartTime())
                .reportTimeUp(monthly.getEndTime())
                .build()
        );
        Map<String, Integer> accessMap = codeReportService.accessQuantityMap(CodeReportAccessDTO.builder()
                .relationNumbers(workOrderNumbers)
                .reportTimeDown(monthly.getStartTime())
                .reportTimeUp(monthly.getEndTime())
                .build()
        );

        // 产能
        Map<String, CapacityVO> workOrderCapacityMap = capacityService.getWorkOrderListCapacity(allWorkOrders);

        // 告警
        List<AlarmEntity> allAlarms = alarmService.lambdaQuery()
                .in(AlarmEntity::getWorkOrderNumber, workOrderNumbers)
                .between(AlarmEntity::getAlarmTime, monthly.getStartTime(), monthly.getEndTime())
                .list();
        Map<String, List<AlarmEntity>> workOrderAlarmGroup = allAlarms.stream().collect(Collectors.groupingBy(AlarmEntity::getWorkOrderNumber));
        // 不良记录
        Map<String, List<RecordWorkOrderUnqualifiedEntity>> unqualifiedRecordsGroup = recordWorkOrderUnqualifiedService.workOrderUnqualifiedRecordsMap(workOrderNumbers, monthly.getStartTime(), monthly.getEndTime());
        // 生产基本单元
        Map<String, List<WorkOrderBasicUnitRelationEntity>> basicUnitGroup = workOrderBasicUnitRelationService.groupByWorkOrderNumbers(workOrderNumbers);
        // 生产基本单元：模型名称 Map
        HashBasedTable<String, Integer, String> modelType = commonMetricsService.getModelType(
                basicUnitGroup.values().stream().flatMap(Collection::stream).map(e -> BasicUnitDTO.builder().workCenterType(e.getWorkCenterType()).productionBasicUnitId(e.getProductionBasicUnitId()).build()).collect(Collectors.toList())
        );
        // 单件理论工时: 这里会把工序的信息也查出来，故下面无需在查工序
        workOrderService.setSimpleWorkOrderTheoryHour(allWorkOrders);
        // 工艺
        workOrderService.showWorkOrdersExtraName(allWorkOrders, ShowExtraNameDTO.builder().craft(true).build());
        // 车间
        List<Integer> lineIds = allWorkOrders.stream().map(WorkOrderEntity::getLineId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, GridEntity> lindIdGridMap = gridService.getLindIdGridMap(lineIds);
        // 厂区
        List<Integer> workCenterIds = allWorkOrders.stream().map(WorkOrderEntity::getWorkCenterId).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, AreaEntity> areaWorkCenterIdMap = workCenterService.areaWorkCenterIdMap(workCenterIds);

        List<MetricsWorkOrderMonthlyEntity> results = allWorkOrders.stream().map(workOrder -> {
            String workOrderNumber = workOrder.getWorkOrderNumber();
            String materialCode = workOrder.getMaterialCode();

            double planQuantity =workOrderPlanGroup.getOrDefault(workOrderNumber, Collections.emptyList())
                    .stream().map(WorkOrderPlanEntity::getPlanQuantity).reduce(Double::sum).orElse(0d);
            double produceQuantity = dayCountGroup.getOrDefault(workOrderNumber, Collections.emptyList())
                    .stream().map(RecordWorkOrderDayCountEntity::getCount).reduce(Double::sum).orElse(0d);
            double unqualifiedQuantity = dayCountGroup.getOrDefault(workOrderNumber, Collections.emptyList())
                    .stream().map(RecordWorkOrderDayCountEntity::getUnqualified).reduce(Double::sum).orElse(0d);
            double repairScrapQuantity = maintainRecordsMap.getOrDefault(workOrderNumber, Collections.emptyList())
                    .stream().filter(e -> !MaintainTypeEnum.SCRAP.getCode().equals(e.getMaintainResultType())).count();
            double repairQualifiedQuantity = maintainRecordsMap.getOrDefault(workOrderNumber, Collections.emptyList())
                    .stream().filter(e -> !MaintainTypeEnum.FINISHED.getCode().equals(e.getMaintainResultType())).count();
            double directAccessQuantity = directAccessMap.getOrDefault(workOrderNumber, 0);
            double accessQuantity = accessMap.getOrDefault(workOrderNumber, 0);
            // 不良记录
            List<RecordWorkOrderUnqualifiedEntity> unqualifiedRecordItems = workOrderNumbers.stream().map(e -> unqualifiedRecordsGroup.getOrDefault(e, Collections.emptyList())).flatMap(Collection::stream).collect(Collectors.toList());
            int unqualifiedRecordQuantity = (int) unqualifiedRecordItems.stream().map(RecordWorkOrderUnqualifiedEntity::getSequenceId).distinct().count();
            // 工时
            double humanWorkingHour = attendanceRecordsMap.getOrDefault(workOrderNumber, Collections.emptyList())
                    .stream().map(AttendanceRecordVO::getDurationH).filter(Objects::nonNull)
                    .reduce(BigDecimal::add).map(e -> Double.valueOf(e.toString())).orElse(0d);
            double resourceWorkingHour = resourceInputMap.getOrDefault(workOrderNumber, 0d);
            // 产能 -> 理论工时
            CapacityVO capacityVO = workOrderCapacityMap.get(workOrderNumber);
            Double theoryWorkingHour;
            if(capacityVO == null) {
                theoryWorkingHour = null;
                log.warn("工单号: {} 下未找到产能数据", workOrderNumber);
            }else {
                // 理论产出工时 = 工单产出数 / 标准产能
                theoryWorkingHour = NullableDouble.of(produceQuantity).div(capacityVO.getCapacity()).cal();
            }
            // 告警相关
            List<AlarmEntity> alarms = workOrderAlarmGroup.getOrDefault(workOrderNumber, Collections.emptyList());
            double alarmDealDurationH = alarms.stream().map(AlarmEntity::getDealDurationH).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
            double alarmRecoverDurationH = alarms.stream().map(AlarmEntity::getResponseDurationH).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
            // 生产基本单元
            List<WorkOrderBasicUnitRelationEntity> basicUnitRelations = basicUnitGroup.getOrDefault(workOrderNumber, Collections.emptyList()).stream()
                    .filter(e -> Objects.equals(workOrder.getWorkCenterType(), e.getWorkCenterType()))
                    .collect(Collectors.toList());
            // 车间
            GridEntity grid = lindIdGridMap.get(workOrder.getLineId());
            AreaEntity area = areaWorkCenterIdMap.get(workOrder.getWorkCenterId());

            return MetricsWorkOrderMonthlyEntity.builder()
                    .recordTime(monthly.getRecordTime())
                    .saleOrderNumber(workOrder.getSaleOrderNumber())
                    .productOrderNumber(workOrder.getProductOrderNumber())
                    .workOrderNumber(workOrderNumber)
                    .materialCode(materialCode)
                    .aName(Optional.ofNullable(area).map(AreaEntity::getAname).orElse(null))
                    .businessUnitName(workOrder.getBusinessUnitName())
                    .workCenterType(workOrder.getWorkCenterType())
                    .workCenterId(workOrder.getWorkCenterId())
                    .workCenterName(workOrder.getWorkCenterName())
                    .productionBasicUnitName(basicUnitRelations.stream().map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitName).collect(Collectors.joining(Constant.SEP)))
                    .productionBasicUnitModelType(basicUnitRelations.stream().map(e -> modelType.get(e.getWorkCenterType(), e.getProductionBasicUnitId())).filter(Objects::nonNull).collect(Collectors.joining(Constant.SEP)))
                    .gid(Optional.ofNullable(grid).map(GridEntity::getGid).orElse(null))
                    .gname(Optional.ofNullable(grid).map(GridEntity::getGname).orElse(null))
                    .craftName(workOrder.getCraftName())
                    .craftProcedureName(workOrder.getProcedureName())
                    .theoryHour(workOrder.getTheoryHour())
                    .planTheoryHour(workOrder.getPlanTheoryHour())
                    .startDate(workOrder.getStartDate())
                    .endDate(workOrder.getEndDate())
                    .actualStartDate(workOrder.getActualStartDate())
                    .actualEndDate(workOrder.getActualEndDate())
                    // 指标
                    .planQuantity(planQuantity)
                    .produceQuantity(produceQuantity)
                    .unqualifiedQuantity(unqualifiedQuantity)
                    .unqualifiedRecordItemQuantity(unqualifiedRecordItems.size())
                    .unqualifiedRecordQuantity(unqualifiedRecordQuantity)
                    .repairQuantity(repairScrapQuantity + repairQualifiedQuantity)
                    .repairScrapQuantity(repairScrapQuantity)
                    .repairQualifiedQuantity(repairQualifiedQuantity)
                    .directAccessQuantity(directAccessQuantity)
                    .qualifiedRate(NullableDouble.of(produceQuantity).div(produceQuantity + unqualifiedQuantity).cal())
                    .directAccessRate(NullableDouble.of(directAccessQuantity).div(accessQuantity).cal())
                    .humanWorkingHour(humanWorkingHour)
                    .resourceWorkingHour(resourceWorkingHour)
                    .productHour(theoryWorkingHour)
                    .humanEfficiencyRate(NullableDouble.of(theoryWorkingHour).div(humanWorkingHour).cal())
                    .resourceEfficiencyRate(NullableDouble.of(theoryWorkingHour).div(resourceWorkingHour).cal())
                    .planAchievementRate(NullableDouble.of(produceQuantity).div(planQuantity).cal())
                    .alarmCount(alarms.size())
                    .alarmDealDurationH(alarmDealDurationH)
                    .alarmRecoverDurationH(alarmRecoverDurationH)
                    .build();
        }).collect(Collectors.toList());
        commonMetricsService.setMaterialAttr(results);
        return results;
    }
}
