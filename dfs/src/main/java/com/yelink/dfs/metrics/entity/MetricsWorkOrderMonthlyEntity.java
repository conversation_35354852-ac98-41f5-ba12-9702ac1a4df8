package com.yelink.dfs.metrics.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.metrics.api.dto.common.BaseMaterialEntity;
import com.yelink.metrics.core.constant.annotations.MetricsColumn;
import com.yelink.metrics.core.constant.annotations.MetricsInfo;
import com.yelink.metrics.core.constant.annotations.MetricsUniCode;
import com.yelink.metrics.core.constant.enums.FieldDefine;
import com.yelink.metrics.core.constant.enums.TimeDimension;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 生产月度汇总-按产品
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@MetricsInfo(comment = "生产月度汇总-按工单(计划+投产+实际生产)", modelCode = "workOrderTarget", diyUniCode = true)
@TableName(value = "dfs_metrics_work_order_monthly", schema = "dfs_metrics")
public class MetricsWorkOrderMonthlyEntity extends BaseMaterialEntity {


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @MetricsColumn(comment = "记录日期", filedDefine = FieldDefine.TIME_DIMENSION, timeEnum = TimeDimension.MONTH)
    @TableField(value = "record_time")
    @MetricsUniCode
    private Date recordTime;

    @MetricsColumn(comment = "销售订单号")
    @TableField(value = "sale_order_number")
    private String saleOrderNumber;

    @MetricsColumn(comment = "生产订单号")
    @TableField(value = "product_order_number")
    private String productOrderNumber;

    @MetricsColumn(comment = "生产工单号", filedDefine = FieldDefine.DIMENSION)
    @TableField(value = "work_order_number")
    @MetricsUniCode
    private String workOrderNumber;

    @MetricsColumn(comment = "物料编码")
    @TableField(value = "material_code")
    private String materialCode;

    @MetricsColumn(comment = "厂区名称", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "a_name")
    private String aName;

    @MetricsColumn(comment = "业务单元名称")
    @TableField(value = "business_unit_name")
    private String businessUnitName;

    @MetricsColumn(comment = "生产资源类型", remark = "工作中心类型", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "work_center_type")
    private String workCenterType;

    @MetricsColumn(comment = "工作中心id", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "work_center_id")
    private Integer workCenterId;

    @MetricsColumn(comment = "工作中心名称", filedDefine = FieldDefine.DIMENSION)
    @TableField(value = "work_center_name")
    private String workCenterName;

    @MetricsColumn(comment = "生产基本单元名称", filedDefine = FieldDefine.ATTACHMENT, remark = "多个按','分隔")
    @TableField(value = "production_basic_unit_name")
    private String productionBasicUnitName;

    @MetricsColumn(comment = "生产基本单元类型", remark = "模型的类型，多个按','分隔", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "production_basic_unit_model_type")
    private String productionBasicUnitModelType;

    @MetricsColumn(comment = "车间id", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "gid")
    private Integer gid;

    @MetricsColumn(comment = "车间名称", filedDefine = FieldDefine.DIMENSION)
    @TableField(value = "gname")
    private String gname;

    @MetricsColumn(comment = "工艺名称", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "craft_name")
    private String craftName;

    @MetricsColumn(comment = "工序名称，多个按','分隔", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "craft_procedure_name")
    private String craftProcedureName;

    @MetricsColumn(comment = "单件理论工时", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "theory_hour")
    private Double theoryHour;

    @MetricsColumn(comment = "计划理论工时", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "plan_theory_hour")
    private Double planTheoryHour;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @MetricsColumn(comment = "计划开始时间", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "start_date")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @MetricsColumn(comment = "计划结束时间", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "end_date")
    private Date endDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @MetricsColumn(comment = "实际开始时间", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "actual_start_date")
    private Date actualStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @MetricsColumn(comment = "实际结束时间", filedDefine = FieldDefine.ATTACHMENT)
    @TableField(value = "actual_end_date")
    private Date actualEndDate;



    // ---------指标分界线---------

    @MetricsColumn(comment = "计划数量", filedDefine = FieldDefine.TARGET)
    @TableField(value = "plan_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Double planQuantity;

    @MetricsColumn(comment = "产出数量", filedDefine = FieldDefine.TARGET)
    @TableField(value = "produce_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Double produceQuantity;

    @MetricsColumn(comment = "不良数量", filedDefine = FieldDefine.TARGET)
    @TableField(value = "unqualified_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Double unqualifiedQuantity;

    @MetricsColumn(comment = "不良品记录数量", remark = "流水码去重", filedDefine = FieldDefine.TARGET)
    @TableField(value = "unqualified_record_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Integer unqualifiedRecordQuantity;

    @MetricsColumn(comment = "不良项记录数量", remark = "流水码不去重", filedDefine = FieldDefine.TARGET)
    @TableField(value = "unqualified_record_item_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Integer unqualifiedRecordItemQuantity;

    @MetricsColumn(comment = "维修数", filedDefine = FieldDefine.TARGET)
    @TableField(value = "repair_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Double repairQuantity;

    @MetricsColumn(comment = "维修报废数", remark = "维修中结果报废的数量", filedDefine = FieldDefine.TARGET)
    @TableField(value = "repair_scrap_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Double repairScrapQuantity;

    @MetricsColumn(comment = "维修合格数", remark = "维修中结果正常的数量", filedDefine = FieldDefine.TARGET)
    @TableField(value = "repair_qualified_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Double repairQualifiedQuantity;

    @MetricsColumn(comment = "直通数", remark = "没有被标记为首次不良的成品数量", filedDefine = FieldDefine.TARGET)
    @TableField(value = "direct_access_quantity", updateStrategy = FieldStrategy.IGNORED)
    private Double directAccessQuantity;

    @MetricsColumn(comment = "合格率", remark = "合格数/(合格数+不良数)", filedDefine = FieldDefine.TARGET)
    @TableField(value = "qualified_rate", updateStrategy = FieldStrategy.IGNORED)
    private Double qualifiedRate;

    @MetricsColumn(comment = "直通率", remark = "直通数/(完成数+维修报废数)", filedDefine = FieldDefine.TARGET)
    @TableField(value = "direct_access_rate", updateStrategy = FieldStrategy.IGNORED)
    private Double directAccessRate;

    @MetricsColumn(comment = "人员投入工时", remark = "人员打卡的时间求和", filedDefine = FieldDefine.TARGET)
    @TableField(value = "human_working_hour", updateStrategy = FieldStrategy.IGNORED)
    private Double humanWorkingHour;

    @MetricsColumn(comment = "资源投入工时", remark = "生产资源分配时间求和", filedDefine = FieldDefine.TARGET)
    @TableField(value = "resource_working_hour", updateStrategy = FieldStrategy.IGNORED)
    private Double resourceWorkingHour;

    @MetricsColumn(comment = "产出工时", remark = "完成数/产能", filedDefine = FieldDefine.TARGET)
    @TableField(value = "product_hour", updateStrategy = FieldStrategy.IGNORED)
    private Double productHour;

    @MetricsColumn(comment = "人员生产效率", remark = "产出工时/人员投入工时", filedDefine = FieldDefine.TARGET)
    @TableField(value = "human_efficiency_rate", updateStrategy = FieldStrategy.IGNORED)
    private Double humanEfficiencyRate;

    @MetricsColumn(comment = "资源生产效率", remark = "产出工时/资源投入工时", filedDefine = FieldDefine.TARGET)
    @TableField(value = "resource_efficiency_rate", updateStrategy = FieldStrategy.IGNORED)
    private Double resourceEfficiencyRate;

    @MetricsColumn(comment = "计划达成率", remark = "实际生产数/计划生产数", filedDefine = FieldDefine.TARGET)
    @TableField(value = "plan_achievement_rate", updateStrategy = FieldStrategy.IGNORED)
    private Double planAchievementRate;

    @MetricsColumn(comment = "告警发生次数", remark = "告警次数计数", filedDefine = FieldDefine.TARGET)
    @TableField(value = "alarm_count", updateStrategy = FieldStrategy.IGNORED)
    private Integer alarmCount;

    @MetricsColumn(comment = "总处理时长", remark = "告警处理时长求和", filedDefine = FieldDefine.TARGET)
    @TableField(value = "alarm_deal_duration_h", updateStrategy = FieldStrategy.IGNORED)
    private Double alarmDealDurationH;

    @MetricsColumn(comment = "总恢复时长", remark = "告警恢复时长求和", filedDefine = FieldDefine.TARGET)
    @TableField(value = "alarm_recover_duration_h", updateStrategy = FieldStrategy.IGNORED)
    private Double alarmRecoverDurationH;
}
