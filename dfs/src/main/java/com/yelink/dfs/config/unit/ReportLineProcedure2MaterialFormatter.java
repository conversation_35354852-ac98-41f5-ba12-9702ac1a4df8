
package com.yelink.dfs.config.unit;

import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.target.record.ReportLineProcedureEntity;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.target.record.ReportLineProcedureService;
import com.yelink.dfscommon.cache.RedisCache;
import com.yelink.dfscommon.common.unit.config.UnitCodeFormatter;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 自定义单位格式化
 * <AUTHOR>
 */
@Component
public class ReportLineProcedure2MaterialFormatter implements UnitCodeFormatter {

    @Resource
    private ReportLineProcedureService reportLineProcedureService;
    @Resource
    private WorkOrderService workOrderService;

    @Override
    @RedisCache(prefix = "UNIT:RPP2M", timeOut = 15, timeUnit = TimeUnit.MINUTES)
    public String format(String originCode) {
        ReportLineProcedureEntity one = reportLineProcedureService.getById(originCode);
        if(one == null) {
            return  null;
        }
        WorkOrderEntity workOrder = workOrderService.getSimpleWorkOrderByNumber(one.getWorkOrderNumber());
        return Optional.ofNullable(workOrder).map(WorkOrderEntity::getMaterialCode).orElse(null);
    }
}
