package com.yelink.inner.listen.manager.impl.pushdown;//package com.yelink.inner.listen.manager.impl;


import com.yelink.dfs.constant.ValueChainKafkaMessageConstants;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierFullPathEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.inner.listen.manager.OrderPushDownMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(ValueChainKafkaMessageConstants.WORK_ORDER_MATERIAL_LIST_PUSH_MSG_SERVICE)
public class WorkOrderMaterialListPushMsgServiceImpl implements OrderPushDownMessageService {

    @Resource
    private WorkOrderMaterialListService workOrderMaterialListService;
    @Resource
    private OrderPushDownIdentifierService pushDownIdentifierService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;

    @Async
    @Override
    public void deal(List<OrderPushDownRecordEntity> recordEntities) {
        if (CollectionUtils.isEmpty(recordEntities)) {
            return;
        }

        try {
            // 1. 按工单用料清单编号和目标单据类型分组传入的下推记录
            Map<String, Map<String, List<OrderPushDownRecordEntity>>> recordsByMaterialListAndTargetType = recordEntities.stream()
                    .filter(record -> Objects.nonNull(record.getSourceOrderNumber()) && Objects.nonNull(record.getTargetOrderType()))
                    .collect(Collectors.groupingBy(
                            OrderPushDownRecordEntity::getSourceOrderNumber,
                            Collectors.groupingBy(OrderPushDownRecordEntity::getTargetOrderType)
                    ));

            if (recordsByMaterialListAndTargetType.isEmpty()) {
                log.warn("未找到有效的工单用料清单编号和目标单据类型，跳过处理");
                return;
            }

            // 2. 查询涉及的工单用料清单信息
            List<String> materialListCodes = new ArrayList<>(recordsByMaterialListAndTargetType.keySet());
            List<WorkOrderMaterialListEntity> materialListEntities = workOrderMaterialListService.lambdaQuery()
                    .in(WorkOrderMaterialListEntity::getMaterialListCode, materialListCodes)
                    .list();

            if (CollectionUtils.isEmpty(materialListEntities)) {
                log.warn("未找到对应的工单用料清单信息，用料清单编号: {}", materialListCodes);
                return;
            }

            // 3. 为每个工单用料清单的每种目标单据类型计算下推状态并更新标识
            List<OrderPushDownIdentifierEntity> identifierEntities = new ArrayList<>();
            for (WorkOrderMaterialListEntity materialList : materialListEntities) {
                Map<String, List<OrderPushDownRecordEntity>> targetTypeRecords = recordsByMaterialListAndTargetType.get(materialList.getMaterialListCode());
                if (targetTypeRecords == null) {
                    continue;
                }

                // 为每种目标单据类型分别处理
                for (Map.Entry<String, List<OrderPushDownRecordEntity>> entry : targetTypeRecords.entrySet()) {
                    String targetOrderType = entry.getKey();

                    // 查询该工单用料清单对该目标单据类型的所有下推记录
                    List<OrderPushDownRecordEntity> allTypeRecords = orderPushDownRecordService.lambdaQuery()
                            .eq(OrderPushDownRecordEntity::getSourceOrderNumber, materialList.getMaterialListCode())
                            .eq(OrderPushDownRecordEntity::getSourceOrderType, OrderNumTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode())
                            .eq(OrderPushDownRecordEntity::getTargetOrderType, targetOrderType)
                            .list();

                    // 计算该目标单据类型的下推状态
                    String pushDownState = calculatePushDownState(materialList, allTypeRecords);

                    // 获取对应的全路径编码
                    String valueFullPathCode = PushDownIdentifierFullPathEnum.getFullPathCode(targetOrderType, pushDownState);

                    // 创建或更新下推标识
                    OrderPushDownIdentifierEntity identifierEntity = OrderPushDownIdentifierEntity.builder()
                            .orderType(OrderNumTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode())
                            .orderMaterialId(materialList.getMaterialListId().toString())
                            .targetOrderType(targetOrderType)
                            .state(pushDownState)
                            .build();

                    identifierEntities.add(identifierEntity);
                }
            }

            // 4. 批量保存或更新下推标识
            if (CollectionUtils.isNotEmpty(identifierEntities)) {
                updatePushDownIdentifiers(identifierEntities);
            }

        } catch (Exception e) {
            log.error("处理工单用料清单下推标识时发生异常", e);
        }
    }

    /**
     * 计算工单用料清单的下推状态
     *
     * @param materialList 工单用料清单信息
     * @param pushDownRecords 下推记录列表
     * @return 下推状态
     */
    private String calculatePushDownState(WorkOrderMaterialListEntity materialList, List<OrderPushDownRecordEntity> pushDownRecords) {
        if (CollectionUtils.isEmpty(pushDownRecords)) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 过滤异常记录，计算有效下推数量
        List<OrderPushDownRecordEntity> validRecords = pushDownRecords.stream()
                .filter(record -> !Boolean.TRUE.equals(record.getIsAbnormal()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validRecords)) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 去重并计算下推数量总和
        List<OrderPushDownRecordEntity> distinctRecords = validRecords.stream()
                .filter(WrapperUtil.distinctByKey(OrderPushDownRecordEntity::getPushDownCode))
                .collect(Collectors.toList());

        double totalPushDownQuantity = distinctRecords.stream()
                .mapToDouble(OrderPushDownRecordEntity::getPushDownQuantity)
                .sum();

        // 工单用料清单的计划数量使用关联单据数量
        double planQuantity = materialList.getRelateQuantity() != null ? materialList.getRelateQuantity() : 0.0;

        // 判断下推状态
        if (totalPushDownQuantity == 0) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        } else if (totalPushDownQuantity < planQuantity) {
            return PushDownIdentifierStateEnum.PART_PUSH_DOWN.getCode();
        } else {
            return PushDownIdentifierStateEnum.ALL_PUSH_DOWN.getCode();
        }
    }

    /**
     * 更新下推标识
     *
     * @param identifierEntities 下推标识实体列表
     */
    private void updatePushDownIdentifiers(List<OrderPushDownIdentifierEntity> identifierEntities) {
        for (OrderPushDownIdentifierEntity entity : identifierEntities) {
            // 查询是否已存在相同的标识记录（包括目标单据类型）
            OrderPushDownIdentifierEntity existingEntity = pushDownIdentifierService.lambdaQuery()
                    .eq(OrderPushDownIdentifierEntity::getOrderType, entity.getOrderType())
                    .eq(OrderPushDownIdentifierEntity::getOrderMaterialId, entity.getOrderMaterialId())
                    .eq(OrderPushDownIdentifierEntity::getTargetOrderType, entity.getTargetOrderType())
                    .one();

            if (existingEntity != null) {
                // 更新现有记录
                existingEntity.setState(entity.getState());
                pushDownIdentifierService.updateById(existingEntity);
                log.debug("更新工单用料清单下推标识: 用料清单ID={}, 目标单据类型={}, 状态={}",
                        entity.getOrderMaterialId(), entity.getTargetOrderType(), entity.getState());
            } else {
                // 新增记录
                pushDownIdentifierService.save(entity);
                log.debug("新增工单用料清单下推标识: 用料清单ID={}, 目标单据类型={}, 状态={}",
                        entity.getOrderMaterialId(), entity.getTargetOrderType(), entity.getState());
            }
        }
    }
}
